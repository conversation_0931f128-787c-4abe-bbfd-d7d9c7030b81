# Frontend PostgreSQL Compatibility Audit Report

**Generated:** July 11, 2025  
**Migration:** MongoDB → PostgreSQL Frontend Compatibility  
**Total Files Scanned:** 236  
**Files with Issues:** 61  
**Total Issues Found:** 590  

## Executive Summary

The frontend codebase contains **590 compatibility issues** across 61 files that need to be addressed for full PostgreSQL compatibility. The issues are primarily related to field naming conventions and MongoDB-specific patterns that are incompatible with the new PostgreSQL backend.

### Issue Severity Breakdown:
- 🔴 **HIGH Severity:** 87 issues (MongoDB ObjectId references, nested objects)
- 🟡 **MEDIUM Severity:** 503 issues (camelCase field naming)
- 🟢 **LOW Severity:** 0 issues

## Critical Issues Requiring Immediate Attention

### 1. MongoDB ObjectId References (87 HIGH severity issues)
**Problem:** Frontend code still uses `_id` fields expecting MongoDB ObjectIds
**Impact:** API calls will fail as PostgreSQL uses `id` with UUID format

**Examples:**
```typescript
// ❌ Current MongoDB pattern
const addr = addresses.filter(addr => addr._id === value)[0];
onClick={() => router.push(`/buyers/produce/${item._id}`)}
setOrderId(data._id);

// ✅ Required PostgreSQL pattern  
const addr = addresses.filter(addr => addr.id === value)[0];
onClick={() => router.push(`/buyers/produce/${item.id}`)}
setOrderId(data.id);
```

**Files Most Affected:**
- `eweko/app/(buyers)/buyers/checkout/components/Checkout.tsx` (15 issues)
- `eweko/app/(farmers)/farmers/settings/components/address.tsx` (8 issues)
- `eweko/app/(buyers)/buyers/cart/components/Cart.tsx` (5 issues)

### 2. MongoDB Nested Object Access (HIGH severity)
**Problem:** Frontend assumes MongoDB document structure with nested objects
**Impact:** Data will be null/undefined as PostgreSQL uses relational structure

**Examples:**
```typescript
// ❌ MongoDB nested access
produce.category.name
produce.farmer.firstName

// ✅ PostgreSQL relational access (requires separate API calls or JOINs)
produce.category_name  // or separate category API call
produce.farmer_name    // or separate farmer API call
```

## Field Naming Convention Issues (503 MEDIUM severity issues)

### Most Common Field Naming Issues:

| MongoDB Field | PostgreSQL Field | Occurrences |
|---------------|------------------|-------------|
| `firstName` | `first_name` | 45 |
| `lastName` | `last_name` | 38 |
| `userType` | `user_type` | 35 |
| `phoneNumber` | `primary_phone` | 32 |
| `createdAt` | `created_at` | 28 |
| `updatedAt` | `updated_at` | 25 |
| `isActive` | `is_active` | 22 |
| `houseNumber` | `house_number` | 20 |
| `streetName` | `street_name` | 20 |
| `totalCost` | `total_cost` | 18 |

## Module-by-Module Analysis

### Authentication Module (High Priority)
**Files:** 12 files with 89 issues
**Critical Issues:**
- User type detection using `userType` instead of `user_type`
- Phone number fields using `phoneNumber` instead of `primary_phone`
- Name fields using `firstName`/`lastName` instead of `first_name`/`last_name`

### Cart & Checkout Module (High Priority)
**Files:** 8 files with 156 issues
**Critical Issues:**
- Cart item IDs using `_id` instead of `id`
- Price fields using `totalPrice`/`totalCost` instead of `total_price`/`total_cost`
- Address fields using camelCase instead of snake_case

### Settings & Profile Module (Medium Priority)
**Files:** 15 files with 178 issues
**Critical Issues:**
- Address management using `_id` references
- Profile fields using camelCase naming
- Notification preferences using camelCase

### Farmers Module (Medium Priority)
**Files:** 18 files with 134 issues
**Critical Issues:**
- Produce management using `_id` references
- Farm-specific fields using camelCase
- Transaction data using MongoDB field names

## Required Frontend Updates

### 1. TypeScript Interface Updates
```typescript
// ❌ Current MongoDB interfaces
interface IFarmer {
  _id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  userType: string;
  farmName: string;
  createdAt: Date;
  updatedAt: Date;
  __v: number;
}

// ✅ Required PostgreSQL interfaces
interface IFarmer {
  id: string;
  first_name: string;
  last_name: string;
  primary_phone: string;
  user_type: string;
  farm_name: string;
  created_at: Date;
  updated_at: Date;
  // Remove __v (MongoDB version field)
}
```

### 2. API Call Updates
```typescript
// ❌ Current MongoDB API calls
const response = await fetch(`/api/addresses/${address._id}`, {
  method: 'PATCH',
  body: JSON.stringify({
    houseNumber: data.houseNumber,
    streetName: data.streetName,
    isDefault: data.isDefault
  })
});

// ✅ Required PostgreSQL API calls
const response = await fetch(`/api/addresses/${address.id}`, {
  method: 'PATCH',
  body: JSON.stringify({
    house_number: data.house_number,
    street_name: data.street_name,
    is_default: data.is_default
  })
});
```

### 3. Form Field Updates
```typescript
// ❌ Current MongoDB form fields
<input name="firstName" />
<input name="lastName" />
<input name="phoneNumber" />
<input name="houseNumber" />
<input name="streetName" />

// ✅ Required PostgreSQL form fields
<input name="first_name" />
<input name="last_name" />
<input name="primary_phone" />
<input name="house_number" />
<input name="street_name" />
```

## Implementation Strategy

### Phase 1: Critical Fixes (Week 1)
1. **Replace all `_id` with `id`** throughout the codebase
2. **Update TypeScript interfaces** for core entities (User, Address, Product, Order)
3. **Fix authentication flow** field naming issues
4. **Update cart and checkout** critical paths

### Phase 2: Field Naming Standardization (Week 2)
1. **Convert all camelCase to snake_case** in API calls
2. **Update form field names** and validation
3. **Fix component prop names** and data binding
4. **Update state management** field references

### Phase 3: Data Structure Updates (Week 3)
1. **Handle relational data structure** changes
2. **Update nested object access** patterns
3. **Implement proper JOIN handling** or separate API calls
4. **Remove MongoDB-specific fields** like `__v`

### Phase 4: Testing & Validation (Week 4)
1. **Update all tests** to use PostgreSQL data structures
2. **Validate API integration** with new field names
3. **Test user flows** end-to-end
4. **Performance testing** with new data structure

## Automated Migration Tools

### 1. Field Name Replacement Script
```bash
# Replace common field names across all files
find eweko -name "*.ts" -o -name "*.tsx" | xargs sed -i 's/_id/id/g'
find eweko -name "*.ts" -o -name "*.tsx" | xargs sed -i 's/firstName/first_name/g'
find eweko -name "*.ts" -o -name "*.tsx" | xargs sed -i 's/lastName/last_name/g'
find eweko -name "*.ts" -o -name "*.tsx" | xargs sed -i 's/phoneNumber/primary_phone/g'
find eweko -name "*.ts" -o -name "*.tsx" | xargs sed -i 's/userType/user_type/g'
```

### 2. Interface Generator
Create a script to automatically generate PostgreSQL-compatible TypeScript interfaces from the backend entities.

### 3. API Call Transformer
Develop a transformation layer that automatically converts camelCase to snake_case for API requests and vice versa for responses.

## Risk Assessment

### High Risk Areas:
1. **Authentication Flow** - Critical for user access
2. **Cart & Checkout** - Critical for business operations
3. **Payment Processing** - Financial data integrity
4. **User Profile Management** - Core user functionality

### Medium Risk Areas:
1. **Settings & Preferences** - User experience impact
2. **Notifications** - Communication functionality
3. **Search & Filtering** - Discovery functionality

### Low Risk Areas:
1. **Static Content** - Minimal database interaction
2. **UI Components** - Mostly presentation layer
3. **Utility Functions** - Helper functions

## Success Metrics

### Technical Metrics:
- ✅ Zero `_id` references in frontend code
- ✅ 100% snake_case field naming compliance
- ✅ All API calls return successful responses
- ✅ All forms submit without field name errors

### Business Metrics:
- ✅ User registration/login success rate maintained
- ✅ Cart/checkout conversion rate maintained
- ✅ Page load times within acceptable limits
- ✅ Zero data corruption incidents

## Conclusion

The frontend requires significant updates to achieve full PostgreSQL compatibility, but the issues are well-defined and can be systematically addressed. The migration should be prioritized by business impact, starting with authentication and checkout flows, followed by user management and settings.

**Estimated Effort:** 3-4 weeks for complete migration
**Risk Level:** Medium (manageable with proper testing)
**Business Impact:** High (affects all user interactions)

**Next Steps:**
1. Begin with automated field name replacements
2. Update critical path TypeScript interfaces
3. Test authentication and checkout flows
4. Gradually migrate remaining modules
5. Comprehensive end-to-end testing
