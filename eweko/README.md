
## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npm run dev

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## API URLs

- **Staging API URL:** https://goldfish-app-cghp9.ondigitalocean.app/
- **Production API URL:** https://ewekoaggregate.com/


## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```
