
export const AuthRoutes = {
  // Unified signup endpoint - user type determined from frontend route context
  signup: `${process.env.NEXT_PUBLIC_APIURL}/auth/signup`,
  // Legacy endpoints - kept for backward compatibility
  buyerSignup: `${process.env.NEXT_PUBLIC_APIURL}/auth/signup/buyers`,
  farmerSignup: `${process.env.NEXT_PUBLIC_APIURL}/auth/signup/farmers`,
  agentSignup: `${process.env.NEXT_PUBLIC_APIURL}/auth/signup/agents`,
  login: `${process.env.NEXT_PUBLIC_APIURL}/auth/login`,
  verifyByEmail: `${process.env.NEXT_PUBLIC_APIURL}/auth/verify/email`,
  verifyByPhone: `${process.env.NEXT_PUBLIC_APIURL}/auth/verify/phone`,
  forgetPasswordEmail: `${process.env.NEXT_PUBLIC_APIURL}/auth/forgot-password/email`,
  forgetPasswordSms: `${process.env.NEXT_PUBLIC_APIURL}/auth/forgot-password/phone`,
  resetPasswordPhone: `${process.env.NEXT_PUBLIC_APIURL}/auth/reset-password/phone`,
  resetPasswordEmail: `${process.env.NEXT_PUBLIC_APIURL}/auth/reset-password/email`,

  verifyOtp: `${process.env.NEXT_PUBLIC_APIURL}/auth/verify/otp/`,
  verifyLogin: `${process.env.NEXT_PUBLIC_APIURL}/auth/verify/login`,
  verifyLoginAdmin: `${process.env.NEXT_PUBLIC_APIURL}/auth/verify/login/admin`,
};
