'use client';

import ewekologo from '@/public/ewekologo.png';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { IoReturnUpBack } from 'react-icons/io5';
import { Bounce, toast } from 'react-toastify';
import { ErrorDisplay } from '../../../../../components/ErrorDisplay';
import { Button } from '../../../../../components/ui/button';
import { useGlobalState } from '../../../../../globalStore';
import { InlineLoader } from '../../../../components/InlineLoader';
import { AuthRoutes } from '../../auth.routes';
import { OtpInput } from '../../components/OtpInput';

export const ForgotPasswordEnterOtp = () => {
  const router = useRouter();
  const { passwordResetPhoneNumber, setPasswordResetOtp } = useGlobalState();

  const [otpValue, setOtpValue] = useState('');
  const [errors, setErrors] = useState<(string | undefined)[]>();
  const [isLoading, setIsLoading] = useState(false);
  const handleOtpChange = (otp: string) => {
    setOtpValue(otp);
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    if (otpValue.length < 6) {
      setErrors([
        'Your OTP is incomplete. Please enter the 6-digits code sent to you.',
      ]);
      setIsLoading(false);
    } else {
      setErrors(undefined);

      setPasswordResetOtp(otpValue);
      setIsLoading(false);
      router.push('/auth/buyers/reset-password');
    }
  };

  const handleResendOtp = async () => {
    setIsLoading(true);

    const apiUrl = AuthRoutes.forgetPasswordSms;
    const resendData = JSON.stringify({
      phoneNumber: passwordResetPhoneNumber,
    });

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: resendData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        setErrors([errorData.message || 'Failed to resend OTP']);
      }

      const data = await response.json();
      if (data.code) {
        toast.success('OTP resent to your phone', {
          position: 'top-center',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: 'light',
          transition: Bounce,
        });
      }
    } catch (error: any) {
      toast.error(error.message, {
        position: 'top-center',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: false,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: 'light',
        transition: Bounce,
      });
      console.error('Fetch Error:', error.message || error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full flex flex-col gap-12 bg-white">
      <div
        onClick={() => window.history.back()}
        className={`hover:bg-eweko_green/20 rounded-full h-[60px] w-[60px] flex items-center justify-center cursor-pointer absolute top-[50px] left-[50px] transition-all duration-500`}
      >
        <IoReturnUpBack size={27} className={`text-eweko_green_dark`} />
      </div>

      <div className="w-[90%] sm2:w-[80%] md3:w-[70%] mx-auto flex flex-col gap-12 justify-center items-center overflow-hidden relative">
        <Link rel="prefetch" href="/">
          <Image
            src={ewekologo}
            alt="EwekoAggregate"
            className="w-[250px] max-h-auto object-contain"
            loading="eager"
          />
        </Link>

        <div className="w-full flex flex-col justify-center items-center gap-12">
          <h2 className="text-center text-2xl md3:text-3xl text-eweko_green_dark">
            Recover your account
          </h2>

          <p className="text-eweko_green_dark text-lg w-full text-center">
            Kindly enter the OTP you received on the phone number you provided
          </p>

          {errors && errors.length > 0 && <ErrorDisplay message={errors} />}

          <div className="w-full flex flex-col gap-12">
            <OtpInput length={6} onChange={handleOtpChange} />

            <Button
              type="button"
              onClick={handleSubmit}
              className="rounded-[10px] px-12 py-6 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-full"
            >
              {isLoading ? (
                <InlineLoader color="white" size="30" textSize="10" />
              ) : (
                'Next'
              )}
            </Button>

            <p className="w-full flex justify-center -mt-3">
              Didn&apos;t receive OTP yet?
              <span
                onClick={() => handleResendOtp()}
                className="text-eweko_green_light hover:font-bold ml-2"
              >
                RESEND
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
