'use client';

import ewekologo from '@/public/ewekologo.png';
import Image from 'next/image';
import Link from 'next/link';
import { IoReturnUpBack } from 'react-icons/io5';
import { SigninForm } from './SigninForm';
import { useGlobalState } from '../../../../../globalStore';
import { useEffect } from 'react';

export const Login = () => {
  const { loggedInUser } = useGlobalState();

  useEffect(() => {
    if (loggedInUser?.id) {
      window.location.reload();
    }
  }, [loggedInUser]);

  return (
    <div className="w-full flex flex-col gap-12 bg-white">
      <div
        onClick={() => window.history.back()}
        className={`hover:bg-eweko_green/20 rounded-full h-[60px] w-[60px] flex items-center justify-center cursor-pointer absolute top-[50px] left-[50px] transition-all duration-500`}
      >
        <IoReturnUpBack size={27} className={`text-eweko_green_dark`} />
      </div>

      <div className="w-[90%] sm2:w-[80%] md3:w-[70%] mx-auto flex flex-col gap-12 justify-center items-center overflow-hidden relative">
        <Link rel="prefetch" href="/">
          <Image
            src={ewekologo}
            alt="EwekoAggregate"
            className="w-[250px] max-h-auto object-contain"
            loading="eager"
          />
        </Link>

        <SigninForm />

        <div className="flex flex-col gap-4 w-full justify-center items-center text-center">
          <div className="h-[1px] w-full bg-[#d9d9d9] block" />
          <p className="text-eweko_green_dark">
            Don&apos;t have an account?{' '}
            <Link
              href="/auth/buyers/signup"
              className="text-eweko_green_light hover:font-bold cursor-pointer"
            >
              Sign Up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};
