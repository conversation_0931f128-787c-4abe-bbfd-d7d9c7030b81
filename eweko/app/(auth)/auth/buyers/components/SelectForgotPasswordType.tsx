'use client';

import ewekologo from '@/public/ewekologo.png';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { IoReturnUpBack } from 'react-icons/io5';
import { ErrorDisplay } from '../../../../../components/ErrorDisplay';
import { Button } from '../../../../../components/ui/button';
import { Label } from '../../../../../components/ui/label';
import {
  RadioGroup,
  RadioGroupItem,
} from '../../../../../components/ui/radio-group';

export const SelectForgotPasswordType = () => {
  const [errors, setErrors] = useState<(string | undefined)[]>();
  const [selectedForgotPasswordType, setSelectedForgotPasswordType] = useState<
    string | undefined
  >(undefined);

  const router = useRouter();

  const handleChange = (value: string) => {
    setSelectedForgotPasswordType(value);
  };

  const handleSubmit = () => {
    if (selectedForgotPasswordType) {
      if (selectedForgotPasswordType === 'email')
        router.push('/auth/buyers/forgot-password/email');
      else router.push('/auth/buyers/forgot-password/phone');
    } else {
      setErrors(['Please choose a convenient method of password reset']);
    }
  };

  return (
    <div className="w-full flex flex-col gap-12 bg-white">
      <div
        onClick={() => window.history.back()}
        className={`hover:bg-eweko_green/20 rounded-full h-[60px] w-[60px] flex items-center justify-center cursor-pointer absolute top-[50px] left-[50px] transition-all duration-500`}
      >
        <IoReturnUpBack size={27} className={`text-eweko_green_dark`} />
      </div>

      <div className="w-[90%] sm2:w-[80%] md3:w-[70%] mx-auto flex flex-col gap-12 justify-center items-center overflow-hidden relative">
        <Link rel="prefetch" href="/">
          <Image
            src={ewekologo}
            alt="EwekoAggregate"
            className="w-[250px] max-h-auto object-contain"
            loading="eager"
          />
        </Link>

        <div className="w-full flex flex-col justify-center items-center gap-12">
          <h2 className="text-center text-2xl md3:text-3xl text-eweko_green_dark">
            Recover your account
          </h2>

          <p className="text-eweko_green_dark text-lg w-full">
            Select your preferred method of resetting your password
          </p>

          {errors && errors.length > 0 && <ErrorDisplay message={errors} />}

          <RadioGroup
            value={selectedForgotPasswordType}
            onValueChange={handleChange}
            className="w-full space-y-6"
          >
            <div className="flex items-center space-x-3">
              <RadioGroupItem value="email" id="email" />
              <Label
                className="text-[16px] text-eweko_green_dark"
                htmlFor="email"
              >
                Reset by Email
              </Label>
            </div>
            <div className="flex items-center space-x-3">
              <RadioGroupItem value="sms" id="sms" />
              <Label
                className="text-[16px] text-eweko_green_dark"
                htmlFor="sms"
              >
                Reset by SMS
              </Label>
            </div>
          </RadioGroup>

          <Button
            type="button"
            onClick={handleSubmit}
            className="rounded-[10px] px-12 py-6 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-full"
          >
            {selectedForgotPasswordType === undefined
              ? 'Pick a Method'
              : selectedForgotPasswordType === 'email'
                ? 'Reset by Link'
                : 'Reset by OTP'}
          </Button>
        </div>
      </div>
    </div>
  );
};
