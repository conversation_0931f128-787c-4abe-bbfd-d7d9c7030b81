'use client';

import { useSafeMutation } from '@/axios/query-client';
import { ErrorDisplay } from '@/components/ErrorDisplay';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useGlobalState } from '@/globalStore';
import { maskText, getDashboardUrl } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { BsFillEyeFill, BsFillEyeSlashFill } from 'react-icons/bs';
import { Bounce, toast } from 'react-toastify';
import { z } from 'zod';
import { InlineLoader } from '../../../../components/InlineLoader';
import { AuthRoutes } from '../../auth.routes';
import { SigninResponse } from '../../types';
import Cookies from 'js-cookie';

const signinFormSchema = z.object({
  email: z
    .string()
    .email({ message: 'Please enter a valid email address.' })
    .min(12, { message: 'Email must be at least 12 characters long.' })
    .max(40, { message: 'Email must not exceed 40 characters.' }),
    // .refine(email => email.includes('.com'), {
    //   message: "Email must contain '.com'.",
    // }),
  password: z
    .string()
    .min(4, { message: 'Password must be at least 4 characters long.' })
    .max(40, { message: 'Password must not exceed 40 characters.' })
    .refine(
      password =>
        /[A-Z]/.test(password) &&
        /[a-z]/.test(password) &&
        /\d/.test(password) &&
        /[!@#$%^&*()_\-+=\[\]{};:'",.<>?/\\|`~]/.test(password),
      {
        message:
          'Password must contain at least one uppercase letter, one lowercase letter, one number, and one of these symbols (!@#$%^&*()_-+=[]{};:\'",.<>?/\\|`~).',
      }
    ),
});

type SigninFormData = z.infer<typeof signinFormSchema>;

export const SigninForm = () => {
  const form = useForm<z.infer<typeof signinFormSchema>>({
    resolver: zodResolver(signinFormSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const { setSignupData, setAuthToken } = useGlobalState();

  const router = useRouter();

  const { mutate, isPending } = useSafeMutation<any, Error, SigninFormData>(
    AuthRoutes.login,
    'post',
    {
      onSuccess: data => {
        if (
          data.message &&
          data.message.includes('Please verify your account first') &&
          data?.data &&
          data?.data?.email &&
          data?.data?.phoneNumber
        ) {
          setFormErrors([data?.message]);
          setSignupData(data?.data);

          toast.error(data?.message);
          setTimeout(() => {
            router.push('/auth/verify');
          }, 3000);
        }

        if (
          data.message &&
          !data.message.includes('Please verify your account first')
        ) {
          toast.success(data.message);
          setAuthToken(data.token);
          Cookies.set('3wkAt', data.token, { expires: 999 });

          // Use utility function to get dashboard URL
          const dashboardUrl = getDashboardUrl(data.userType as 'BUYER' | 'FARMER');

          setIsLoading(false);
          router.push(dashboardUrl);
        }
      },
      onError: (error: any) => {
        if (typeof error === 'object') {
          if (Array.isArray(error?.errorMessage)) {
            // Set form errors from array
            setFormErrors(error.errorMessage);

            // Show each error as a toast
            error.errorMessage.forEach((msg: string) => {
              toast.error(msg);
            });
          } else if (typeof error?.errorMessage === 'string') {
            setFormErrors([error.errorMessage]);
            toast.error(error.errorMessage);
          } else if (error?.message) {
            setFormErrors([error.message]);
            toast.error(error.message);
          } else {
            setFormErrors(['An unexpected error occurred.']);
            toast.error('An unexpected error occurred.');
          }
        } else {
          setFormErrors(['An unknown error occurred.']);
          toast.error('An unknown error occurred.');
        }
      },
    }
  );

  const [isLoading, setIsLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<(string | undefined)[]>([]);

  const [showPassword, setShowPassword] = useState(false);
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const onSubmit = async (data: z.infer<typeof signinFormSchema>) => {
    mutate(data);
  };

  useEffect(() => {
    setIsLoading(isPending);
  }, [isPending]);

  useEffect(() => {
    const errors = form.formState.errors;
    if (errors) {
      const errorMessages = Object.values(errors).map(error => error.message);
      setFormErrors(errorMessages);
    }
  }, [form.formState.errors]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8 w-full">
        <h2 className="text-center text-2xl md:text-3xl text-eweko_green_dark">
          Log In
        </h2>

        {formErrors.length > 0 && <ErrorDisplay message={formErrors} />}

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  placeholder="Email Address"
                  type="email"
                  {...field}
                  className="outline-none border-b-[1px] border-t-0 border-l-0 border-r-0 border-eweko_green_light py-6 border-t-ring-0 border-l-ring-0 border-r-ring-0 focus-within:ring-0 focus-visible:ring-0 text-lg placeholder:text-eweko_green_dark text-eweko_green_dark"
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <section className="relative">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Password"
                    {...field}
                    className="outline-none border-b-[1px] border-t-0 border-l-0 border-r-0 border-eweko_green_light py-6 border-t-ring-0 border-l-ring-0 border-r-ring-0 focus-within:ring-0 focus-visible:ring-0 text-lg placeholder:text-eweko_green_dark text-eweko_green_dark"
                  />
                  <span
                    onClick={togglePasswordVisibility}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer duration-500"
                  >
                    {showPassword ? (
                      <BsFillEyeFill size={16} color="#394532" />
                    ) : (
                      <BsFillEyeSlashFill size={16} color="#394532" />
                    )}
                  </span>
                </section>
              </FormControl>
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="rounded-[10px] px-12 py-6 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-full"
        >
          {isLoading ? (
            <InlineLoader color="white" size="30" textSize="10" />
          ) : (
            'Log In'
          )}
        </Button>
        <Link
          rel="prefetch"
          href="/auth/buyers/forgot-password"
          className="text-eweko_green_light hover:font-bold cursor-pointer flex justify-center"
        >
          I forgot my password
        </Link>
      </form>
    </Form>
  );
};
