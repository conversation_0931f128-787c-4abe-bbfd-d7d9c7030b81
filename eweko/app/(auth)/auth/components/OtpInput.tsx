import React, { useRef, useState } from 'react';

interface OtpInputProps {
  length: number;
  onChange: (otp: string) => void;
}

export const OtpInput: React.FC<OtpInputProps> = ({ length, onChange }) => {
  const [otp, setOtp] = useState<string[]>(Array(length).fill(''));
  const inputsRef = useRef<(HTMLInputElement | null)[]>([]);

  const handleChange = (value: string, index: number) => {
    if (!/^\d$/.test(value) && value !== '') return; // Only accept numeric input

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    onChange(newOtp.join(''));

    if (value && index < length - 1) {
      inputsRef.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    const { key } = event;

    if (key === 'Backspace') {
      if (!otp[index] && index > 0) {
        inputsRef.current[index - 1]?.focus();
      }

      const newOtp = [...otp];
      newOtp[index] = '';
      setOtp(newOtp);
      onChange(newOtp.join(''));
    }
  };

  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    const pastedData = event.clipboardData.getData('text').slice(0, length);
    const newOtp = [...otp];

    for (let i = 0; i < pastedData.length; i++) {
      if (!/^\d$/.test(pastedData[i])) break;
      newOtp[i] = pastedData[i];
    }

    setOtp(newOtp);
    onChange(newOtp.join(''));

    const lastIndex = Math.min(pastedData.length, length - 1);
    inputsRef.current[lastIndex]?.focus();
  };

  return (
    <div className="flex space-x-2 w-full justify-between">
      {otp.map((_, index) => (
        <input
          key={index}
          ref={el => {
            inputsRef.current[index] = el; // Assign ref without returning anything
          }}
          type="text"
          maxLength={1}
          value={otp[index]}
          onChange={e => handleChange(e.target.value, index)}
          onKeyDown={e => handleKeyDown(e, index)}
          onPaste={handlePaste}
          className="block w-[13%] h-[60px] text-center font-bold border-b-[1px] border-r-0 border-t-0 border-l-0 border-eweko_green focus:outline-none focus:ring-0 focus:ring-eweko_green focus:border-t-0 focus:border-r-0 focus:border-l-0 focus:border-b-2 text-eweko_green_dark text-3xl"
        />
      ))}
    </div>
  );
};
