'use client';

import ewekologo from '@/public/ewekologo.png';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { FaCheck } from 'react-icons/fa6';
import { IoReturnUpBack } from 'react-icons/io5';

export const ResetPasswordSuccess = () => {
  const router = useRouter();

  useEffect(() => {
    setTimeout(() => {
      router.push('/auth');
    }, 3000);
  }, []);

  return (
    <div className="w-full flex flex-col gap-12 bg-white">
      <div
        onClick={() => window.history.back()}
        className={`hover:bg-eweko_green/20 rounded-full h-[60px] w-[60px] flex items-center justify-center cursor-pointer absolute top-[50px] left-[50px] transition-all duration-500`}
      >
        <IoReturnUpBack size={27} className={`text-eweko_green_dark`} />
      </div>

      <div className="w-[90%] sm2:w-[80%] md3:w-[70%] mx-auto flex flex-col gap-12 justify-center items-center overflow-hidden relative">
        <Link rel="prefetch" href="/">
          <Image
            src={ewekologo}
            alt="EwekoAggregate"
            className="w-[250px] max-h-auto object-contain"
            loading="eager"
          />
        </Link>

        <div className="w-full flex flex-col gap-12 items-center justify-center">
          <h2 className="text-center text-2xl md3:text-3xl text-eweko_green_dark">
            Password reset successful
          </h2>

          <div className="bg-eweko_green/20 rounded-full h-[116px] w-[116px] flex items-center justify-center">
            <FaCheck className="text-[60px] text-eweko_green" />
          </div>

          <p className="text-eweko_green_dark text-lg w-full text-center">
            Login to your account using your new password.
          </p>
        </div>
      </div>
    </div>
  );
};
