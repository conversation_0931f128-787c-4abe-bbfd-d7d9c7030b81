'use client';

import ewekologo from '@/public/ewekologo.png';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { IoReturnUpBack } from 'react-icons/io5';
import { Bounce, toast } from 'react-toastify';
import { useSafeMutation } from '../../../../axios/query-client';
import { ErrorDisplay } from '../../../../components/ErrorDisplay';
import { Button } from '../../../../components/ui/button';
import { Label } from '../../../../components/ui/label';
import {
  RadioGroup,
  RadioGroupItem,
} from '../../../../components/ui/radio-group';
import { useGlobalState } from '../../../../globalStore';
import { maskText } from '../../../../lib/utils';
import { InlineLoader } from '../../../components/InlineLoader';
import { AuthRoutes } from '../auth.routes';
import { OtpDetails, VerifyFormData } from '../types';

export const SelectVerifyAccountType = () => {
  const {
    signupData,
    maskedDetails,
    setMaskedDetails,
    setSelectedOTPchannel,
    selectedOTPchannel,
  } = useGlobalState();

  const [errors, setErrors] = useState<(string | undefined)[]>();
  const [apiUrl, setApiUrl] = useState('');

  const router = useRouter();

  const { mutate, isPending } = useSafeMutation<
    OtpDetails,
    Error,
    VerifyFormData
  >(apiUrl, 'post', {
    onSuccess: data => {
      if (data?.code) {
        toast.success(
          `${apiUrl.includes('email') ? 'OTP sent to your email' : 'OTP sent to your phone'}`
        );
      }

      setTimeout(() => {
        setIsLoading(false);
        router.push(`/auth/verify/enter-otp`);
      }, 3000);
    },
    onError: (error: any) => {
      if (typeof error === 'object') {
        if (Array.isArray(error?.errorMessage)) {
          // Set form errors from array
          setErrors(error.errorMessage);

          // Show each error as a toast
          error.errorMessage.forEach((msg: string) => {
            toast.error(msg);
          });
        } else if (typeof error?.errorMessage === 'string') {
          setErrors([error.errorMessage]);
          toast.error(error.errorMessage);
        } else if (error?.message) {
          setErrors([error.message]);
          toast.error(error.message);
        } else {
          setErrors(['An unexpected error occurred.']);
          toast.error('An unexpected error occurred.');
        }
      } else {
        setErrors(['An unknown error occurred.']);
        toast.error('An unknown error occurred.');
      }
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const handleChange = (value: string) => {
    setSelectedOTPchannel(value);
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    if (selectedOTPchannel) {
      if (selectedOTPchannel.includes('@')) {
        setApiUrl(AuthRoutes.verifyByEmail);
        mutate({ email: selectedOTPchannel });
      } else if (selectedOTPchannel.startsWith('+234')) {
        setApiUrl(AuthRoutes.verifyByPhone);
        mutate({ primaryPhone: selectedOTPchannel });
      }
    } else {
      setErrors(['Please choose a convenient method of verification']);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setIsLoading(isPending);
  }, [isPending]);

  useEffect(() => {
    if (signupData !== undefined) {
      const { email, phoneNumber } = signupData;

      const emailArr = email.split('@');
      const maskedEmailArr1 = maskText(emailArr[0]);
      const maskedPhoneNumber = maskText(phoneNumber);

      setMaskedDetails({
        email: `${maskedEmailArr1}@${emailArr[1]}`,
        primaryPhone: maskedPhoneNumber,
      });
    }
  }, []);

  return (
    <div className="w-full flex flex-col gap-12 bg-white">
      <div
        onClick={() => window.history.back()}
        className={`hover:bg-eweko_green/20 rounded-full h-[60px] w-[60px] flex items-center justify-center cursor-pointer absolute top-[50px] left-[50px] transition-all duration-500`}
      >
        <IoReturnUpBack size={27} className={`text-eweko_green_dark`} />
      </div>

      <div className="w-[90%] sm2:w-[80%] md3:w-[70%] mx-auto flex flex-col gap-12 justify-center items-center overflow-hidden relative">
        <Link rel="prefetch" href="/">
          <Image
            src={ewekologo}
            alt="EwekoAggregate"
            className="w-[250px] max-h-auto object-contain"
            loading="eager"
          />
        </Link>

        <div className="w-full flex flex-col justify-center items-center gap-12">
          <h2 className="text-center text-2xl md3:text-3xl text-eweko_green_dark">
            Verify your account
          </h2>

          <p className="text-eweko_green_dark text-lg w-full">
            Select how you want to receive “OTP” to verify your account
          </p>

          {errors && errors.length > 0 && <ErrorDisplay message={errors} />}

          <RadioGroup
            value={selectedOTPchannel}
            onValueChange={handleChange}
            className="w-full space-y-6"
          >
            <div className="flex items-center space-x-3">
              <RadioGroupItem value={signupData?.email ?? ''} id="email" />
              <Label
                className="text-[16px] text-eweko_green_dark"
                htmlFor="email"
              >
                Receive by email {maskedDetails?.email}
              </Label>
            </div>
            <div className="flex items-center space-x-3">
              <RadioGroupItem
                value={signupData?.phoneNumber ?? ''}
                id="primaryPhone" disabled
              />
              <Label
                className="text-[16px] text-eweko_green_dark"
                htmlFor="primaryPhone"
              >
                Receive by SMS to {maskedDetails?.primaryPhone}
              </Label>
            </div>
          </RadioGroup>
          <Button
            type="button"
            onClick={handleSubmit}
            className="rounded-[10px] px-12 py-6 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-full"
          >
            {isLoading ? (
              <InlineLoader color="white" size="30" textSize="10" />
            ) : (
              'Send OTP'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};
