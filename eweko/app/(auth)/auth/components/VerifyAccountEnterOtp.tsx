'use client';

import ewekologo from '@/public/ewekologo.png';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { IoReturnUpBack } from 'react-icons/io5';
import { Bounce, toast } from 'react-toastify';
import { useSafeMutation } from '../../../../axios/query-client';
import { getUserTypeFromRoute, getDashboardUrl } from '../../../../lib/utils';
import { ErrorDisplay } from '../../../../components/ErrorDisplay';
import { Button } from '../../../../components/ui/button';
import { useGlobalState } from '../../../../globalStore';
import { InlineLoader } from '../../../components/InlineLoader';
import { AuthRoutes } from '../auth.routes';
import { OtpInput } from './OtpInput';

export const VerifyAccountEnterOtp = () => {
  const router = useRouter();
  const pathname = usePathname();

  const {
    selectedOTPchannel,
    maskedDetails,
    signupUserType,
    setSignupData,
    setSelectedOTPchannel,
    setMaskedDetails,
    setAccountCreated,
    setSignupUserType,
  } = useGlobalState();

  const [otpValue, setOtpValue] = useState('');
  const [errors, setErrors] = useState<(string | undefined)[]>();
  const [isLoading, setIsLoading] = useState(false);
  const handleOtpChange = (otp: string) => {
    setOtpValue(otp);
  };

  const { mutate, isPending } = useSafeMutation<{ message: string }, Error>(
    `${AuthRoutes.verifyOtp}${otpValue}`,
    'post',
    {
      onSuccess: data => {
        if (data?.message) {
          toast.success(data.message);
        }

        setErrors(undefined);
        setSignupData(undefined);
        setSelectedOTPchannel(undefined);
        setMaskedDetails({ email: '', primaryPhone: '' });
        setAccountCreated(false);

        setTimeout(() => {
          setIsLoading(false);
          if (signupUserType) {
            // Redirect to appropriate dashboard based on stored user type
            router.push(getDashboardUrl(signupUserType));
            // Clear the stored user type after successful verification
            setSignupUserType(null);
          } else {
            // Fallback: try to determine from current path
            const userType = getUserTypeFromRoute(pathname) || getUserTypeFromRoute(window.location.href);
            if (userType) {
              router.push(getDashboardUrl(userType));
            } else {
              // Last resort: redirect to auth page
              router.push('/auth');
            }
          }
        }, 3000);
      },
      onError: (error: any) => {
        if (typeof error === 'object') {
          if (Array.isArray(error?.errorMessage)) {
            // Set form errors from array
            setErrors(error.errorMessage);

            // Show each error as a toast
            error.errorMessage.forEach((msg: string) => {
              toast.error(msg);
            });
          } else if (typeof error?.errorMessage === 'string') {
            setErrors([error.errorMessage]);
            toast.error(error.errorMessage);
          } else if (error?.message) {
            setErrors([error.message]);
            toast.error(error.message);
          } else {
            setErrors(['An unexpected error occurred.']);
            toast.error('An unexpected error occurred.');
          }
        } else {
          setErrors(['An unknown error occurred.']);
          toast.error('An unknown error occurred.');
        }
      },
    }
  );

  const handleSubmit = async () => {
    setIsLoading(true);
    if (otpValue.length < 6) {
      setErrors([
        'Your OTP is incomplete. Please enter the 6-digits code sent to you.',
      ]);
      setIsLoading(false);
    } else {
      mutate();
    }
  };

  const handleResendOtp = async () => {
    setIsLoading(true);

    let apiUrl = '';
    if (selectedOTPchannel?.includes('@')) {
      apiUrl = AuthRoutes.verifyByEmail;
    } else if (selectedOTPchannel?.startsWith('+234')) {
      apiUrl = AuthRoutes.verifyByPhone;
    }

    const resendData = apiUrl.includes('email')
      ? JSON.stringify({ email: selectedOTPchannel })
      : JSON.stringify({ primaryPhone: selectedOTPchannel });

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: resendData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        setErrors([errorData.message || 'Failed to resend OTP']);
      }

      const data = await response.json();
      if (data.code) {
        toast.success(
          `${apiUrl.includes('email') ? 'OTP resent to your email' : 'OTP resent to your phone'}`
        );
      }
    } catch (error: any) {
      console.error('Fetch Error:', error.message || error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setIsLoading(isPending);
  }, [isPending]);

  return (
    <div className="w-full flex flex-col gap-12 bg-white">
      <div
        onClick={() => window.history.back()}
        className={`hover:bg-eweko_green/20 rounded-full h-[60px] w-[60px] flex items-center justify-center cursor-pointer absolute top-[50px] left-[50px] transition-all duration-500`}
      >
        <IoReturnUpBack size={27} className={`text-eweko_green_dark`} />
      </div>

      <div className="w-[90%] sm2:w-[80%] md3:w-[70%] mx-auto flex flex-col gap-12 justify-center items-center overflow-hidden relative">
        <Link rel="prefetch" href="/">
          <Image
            src={ewekologo}
            alt="EwekoAggregate"
            className="w-[250px] max-h-auto object-contain"
            loading="eager"
          />
        </Link>

        {selectedOTPchannel && selectedOTPchannel.includes('.com') ? (
          <p className="text-eweko_green_dark text-lg w-full text-center">
            Kindly input the OTP you received at {maskedDetails?.email}
          </p>
        ) : (
          <p className="text-eweko_green_dark text-lg w-full text-center">
            Kindly input the OTP you received on {maskedDetails?.primaryPhone}
          </p>
        )}

        {errors && errors.length > 0 && <ErrorDisplay message={errors} />}

        <div className="w-full flex flex-col gap-12">
          <OtpInput length={6} onChange={handleOtpChange} />

          <Button
            type="button"
            onClick={handleSubmit}
            className="rounded-[10px] px-12 py-6 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-full"
          >
            {isLoading ? (
              <InlineLoader color="white" size="30" textSize="10" />
            ) : (
              'Verify'
            )}
          </Button>

          <p className="w-full flex justify-center -mt-3">
            Didn&apos;t receive OTP yet?
            <span
              onClick={() => handleResendOtp()}
              className="text-eweko_green_light hover:font-bold ml-2"
            >
              RESEND
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};
