'use client';

import ewekologo from '@/public/ewekologo.png';
import { zodResolver } from '@hookform/resolvers/zod';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { IoReturnUpBack } from 'react-icons/io5';
import { <PERSON>unce, toast } from 'react-toastify';
import { z } from 'zod';
import { useSafeMutation } from '../../../../../axios/query-client';
import { ErrorDisplay } from '../../../../../components/ErrorDisplay';
import { Button } from '../../../../../components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from '../../../../../components/ui/form';
import { Input } from '../../../../../components/ui/input';
import { useGlobalState } from '../../../../../globalStore';
import { InlineLoader } from '../../../../components/InlineLoader';
import { AuthRoutes } from '../../auth.routes';

export const ForgotPasswordEnterPhone = () => {
  const router = useRouter();
  const {
    setPasswordResetPhoneNumber,
  } = useGlobalState();

  const forgotPasswordPhoneNumberSchema = z.object({
    phoneNumber: z
      .string()
      .min(10, { message: 'Phone number must be at least 10 characters long.' })
      .max(13, { message: 'Phone number must not exceed 13 characters.' })
      .refine(phoneNumber => phoneNumber.startsWith('234'), {
        message: "Phone number must start with '234'.",
      }),
  });

  const form = useForm<z.infer<typeof forgotPasswordPhoneNumberSchema>>({
    resolver: zodResolver(forgotPasswordPhoneNumberSchema),
    defaultValues: {
      phoneNumber: '',
    },
  });

  const [formErrors, setFormErrors] = useState<(string | undefined)[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const errors = form.formState.errors;
    if (errors) {
      const errorMessages = Object.values(errors).map(error => error.message);
      setFormErrors(errorMessages);
    }
  }, [form.formState.errors]);

  const { mutate, isPending } = useSafeMutation<
    { message: string },
    Error,
    { phoneNumber: string }
  >(AuthRoutes.forgetPasswordSms, 'post', {
    onSuccess: data => {
      if (data?.message) {
        toast.success(data.message, {
          position: 'top-center',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: 'light',
          transition: Bounce,
        });
      }

      setIsLoading(false);
      setTimeout(() => {
        router.push('/auth/farmers/forgot-password/phone/enter-otp');
      }, 3000);
    },
    onError: (error: any) => {
      if (error?.message) setFormErrors([error?.message]);

      toast.error(error?.message, {
        position: 'top-center',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: false,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: 'light',
        transition: Bounce,
      });

      if (error.message) {
        setFormErrors([error?.message]);
      }
      if (typeof error === 'object' && error?.errorMessage) {
        setFormErrors([error?.errorMessage]);
      }

      setIsLoading(false);
    },
  });

  const onSubmit = async (
    data: z.infer<typeof forgotPasswordPhoneNumberSchema>
  ) => {
    data.phoneNumber = data.phoneNumber.replace('234', '+234');
    setPasswordResetPhoneNumber(data.phoneNumber);
    mutate(data);
  };

  useEffect(() => {
    setIsLoading(isPending);
  }, [isPending]);

  return (
    <div className="w-full flex flex-col gap-12 bg-white">
      <div
        onClick={() => window.history.back()}
        className={`hover:bg-eweko_green/20 rounded-full h-[60px] w-[60px] flex items-center justify-center cursor-pointer absolute top-[50px] left-[50px] transition-all duration-500`}
      >
        <IoReturnUpBack size={27} className={`text-eweko_green_dark`} />
      </div>

      <div className="w-[90%] sm2:w-[80%] md3:w-[70%] mx-auto flex flex-col gap-12 justify-center items-center overflow-hidden relative">
        <Link rel="prefetch" href="/">
          <Image
            src={ewekologo}
            alt="EwekoAggregate"
            className="w-[250px] max-h-auto object-contain"
            loading="eager"
          />
        </Link>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-8 w-full"
          >
            <h2 className="text-center text-2xl md:text-3xl text-eweko_green_dark">
              Recover your account
            </h2>
            <p className="text-eweko_green_dark text-lg w-full text-center">
              Enter the phone number you used when you signed up to recover your
              account. You will receive an OTP via sms.
            </p>

            {formErrors.length > 0 && <ErrorDisplay message={formErrors} />}

            <div className="pt-6 space-y-8">
              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Phone Number"
                        {...field}
                        className="outline-none border-b-[1px] border-t-0 border-l-0 border-r-0 border-eweko_green_light py-6 border-t-ring-0 border-l-ring-0 border-r-ring-0 focus-within:ring-0 focus-visible:ring-0 text-lg placeholder:text-eweko_green_dark text-eweko_green_dark"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="rounded-[10px] px-12 py-6 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-full"
              >
                {isLoading ? (
                  <InlineLoader color="white" size="30" textSize="10" />
                ) : (
                  'Send OTP'
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};
