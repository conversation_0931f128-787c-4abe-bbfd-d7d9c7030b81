'use client';

import { useGlobalState } from '@/globalStore';
import ewekologo from '@/public/ewekologo.png';
import Image from 'next/image';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { useEffect } from 'react';
import { IoReturnUpBack } from 'react-icons/io5';
import { SignupForm } from './signupForm';

export const Signup = () => {
  const { accountCreated } = useGlobalState();

  useEffect(() => {
    if (accountCreated) {
      setTimeout(() => {
        redirect('/auth/verify');
      }, 3000);
    }
  }, [accountCreated]);

  return (
    <div className="w-full flex flex-col gap-12 bg-white">
      <div
        onClick={() => window.history.back()}
        className={`hover:bg-eweko_green/20 rounded-full h-[60px] w-[60px] flex items-center justify-center cursor-pointer absolute top-[50px] left-[50px] transition-all duration-500`}
      >
        <IoReturnUpBack size={27} className={`text-eweko_green_dark`} />
      </div>

      <div className="w-[90%] sm2:w-[80%] md3:w-[70%] mx-auto flex flex-col gap-12 justify-center items-center overflow-hidden relative">
        <Link rel="prefetch" href="/">
          <Image
            src={ewekologo}
            alt="EwekoAggregate"
            className="w-[250px] max-h-auto object-contain"
            loading="eager"
          />
        </Link>

        <SignupForm />

        <div className="flex flex-col gap-4 w-full justify-center items-center text-center">
          <div className="h-[1px] w-full bg-[#d9d9d9] block" />
          <p className="text-eweko_green_dark">
            Already have an account?{' '}
            <Link
              href="/auth/farmers"
              className="text-eweko_green_light hover:font-bold cursor-pointer"
            >
              Log In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

// 'use client';

// import { AuthType } from '@/app/enums';
// import { useGlobalState } from '@/globalStore';
// import { redirect } from 'next/navigation';
// import { useEffect } from 'react';
// import { FaCheck } from 'react-icons/fa6';
// import { SignupForm } from './signupForm';

// export const Signup = () => {
//   const { accountCreated, setAuthType, setAuthVerificationStep } =
//     useGlobalState();

//   useEffect(() => {
//     if (accountCreated) {
//       setTimeout(() => {
//         redirect('/auth/verify');
//       }, 3000);
//     }
//   }, [accountCreated]);

//   return (
//     <div className="w-full">
//       {accountCreated ? (
//         <div className="w-full flex flex-col gap-12 items-center justify-center">
//           <h2 className="text-center text-2xl md3:text-3xl text-eweko_green_dark">
//             Account
//             <br />
//             successfully created
//           </h2>

//           <div className="bg-eweko_green/20 rounded-full h-[116px] w-[116px] flex items-center justify-center">
//             <FaCheck className="text-[60px] text-eweko_green" />
//           </div>

//           <p className="text-eweko_green_dark text-center">
//             An email has been sent to your emails address for
//             <br />
//             verification. Kindly click the link to verify your account.
//           </p>
//         </div>
//       ) : (
//         <div className="w-full flex flex-col gap-12">
//           <SignupForm />

//           <div className="flex flex-col gap-4 w-full justify-center items-center text-center">
//             <div className="h-[1px] w-full bg-[#d9d9d9] block" />
//             <p className="text-eweko_green_dark">
//               Already have an account?{' '}
//               <span
//                 onClick={() => setAuthType(AuthType.Login)}
//                 className="text-eweko_green_light hover:font-bold cursor-pointer"
//               >
//                 Log in
//               </span>
//             </p>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };
