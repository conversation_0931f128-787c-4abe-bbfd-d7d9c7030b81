'use client';

import { useSafeMutation } from '@/axios/query-client';
import { ErrorDisplay } from '@/components/ErrorDisplay';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useGlobalState } from '@/globalStore';
import { maskText, getUserTypeFromRoute } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Bounce, toast } from 'react-toastify';
import { z } from 'zod';
import { InlineLoader } from '../../../../components/InlineLoader';
import { AuthRoutes } from '../../auth.routes';
import { SignupResponse } from '../../types';
import { BsFillEyeFill, BsFillEyeSlashFill } from 'react-icons/bs';

const signupFormSchema = z.object({
  email: z
    .string()
    .email({ message: 'Please enter a valid email address.' })
    .min(12, { message: 'Email must be at least 12 characters long.' })
    .max(40, { message: 'Email must not exceed 40 characters.' }),

  firstName: z
    .string()
    .min(3, { message: 'Firstname must be at least 3 characters long.' })
    .max(20, { message: 'Firstname must not exceed 20 characters.' }),
  lastName: z
    .string()
    .min(3, { message: 'Lastname must be at least 3 characters long.' })
    .max(20, { message: 'Lastname must not exceed 20 characters.' }),
  username: z
    .string()
    .min(3, { message: 'Username must be at least 3 characters long.' })
    .max(20, { message: 'Username must not exceed 20 characters.' }),
  farmName: z
    .string()
    .min(3, { message: 'Farmname must be at least 3 characters long.' })
    .max(20, { message: 'Farmname must not exceed 20 characters.' }),
  primaryPhone: z
    .string()
    .regex(/^[0-9+]+$/, {
      message: 'Phone number must contain only digits or start with +234',
    })
    .refine(
      value => {
        const digitsOnly = value.replace(/\D/g, '');
        const length = digitsOnly.length;

        if (value.startsWith('0')) return length === 11;
        if (value.startsWith('234')) return length === 13;
        if (value.startsWith('+234')) return length === 13;
        return length === 10;
      },
      {
        message: 'Enter a valid phone number (e.g., 08012345678 or 8012345678)',
      }
    ),
  password: z
    .string()
    .min(4, { message: 'Password must be at least 4 characters long.' })
    .max(40, { message: 'Password must not exceed 40 characters.' })
    .refine(
      password =>
        /[A-Z]/.test(password) &&
        /[a-z]/.test(password) &&
        /\d/.test(password) &&
        /[!@#$%^&*()_\-+=\[\]{};:'",.<>?/\\|`~]/.test(password),
      {
        message:
          'Password must contain at least one uppercase letter, one lowercase letter, one number, and one of these symbols (!@#$%^&*()_-+=[]{};:\'",.<>?/\\|`~).',
      }
    ),
});

type SignupFormData = z.infer<typeof signupFormSchema>;

export const SignupForm = () => {
  const { setSignupData, setAccountCreated, setMaskedDetails, signupData, setSignupUserType } =
    useGlobalState();

  const form = useForm<z.infer<typeof signupFormSchema>>({
    resolver: zodResolver(signupFormSchema),
    defaultValues: {
      email: '',
      firstName: '',
      lastName: '',
      username: '',
      farmName: '',
      primaryPhone: '',
      password: '',
    },
  });

  const router = useRouter();
  const pathname = usePathname();

  const { mutate, isPending } = useSafeMutation<
    SignupResponse,
    Error,
    SignupFormData
  >(AuthRoutes.signup, 'post', {
    onSuccess: data => {
      setAccountCreated(true);
      setMaskedDetails({
        email: maskText(signupData ? signupData?.email : ''),
        primaryPhone: maskText(signupData ? signupData?.phoneNumber : ''),
      });
      toast.success(data.message);
    },
    onError: (error: any) => {
      if (typeof error === 'object') {
        if (Array.isArray(error?.errorMessage)) {
          // Set form errors from array
          setFormErrors(error.errorMessage);

          // Show each error as a toast
          error.errorMessage.forEach((msg: string) => {
            toast.error(msg);
          });
        } else if (typeof error?.errorMessage === 'string') {
          setFormErrors([error.errorMessage]);
          toast.error(error.errorMessage);
        } else if (error?.message) {
          setFormErrors([error.message]);
          toast.error(error.message);
        } else {
          setFormErrors(['An unexpected error occurred.']);
          toast.error('An unexpected error occurred.');
        }
      } else {
        setFormErrors(['An unknown error occurred.']);
        toast.error('An unknown error occurred.');
      }
    },
  });
  const [formErrors, setFormErrors] = useState<(string | undefined)[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);



  const onSubmit = async (data: z.infer<typeof signupFormSchema>) => {
    let cleanedPhone = data.primaryPhone.trim().replace(/\s+/g, '');

    if (cleanedPhone.startsWith('0')) {
      // Starts with 0 => Replace 0 with +234
      cleanedPhone = '+234' + cleanedPhone.slice(1);
    } else if (cleanedPhone.startsWith('+234')) {
      // Already in correct format
    } else if (cleanedPhone.startsWith('234')) {
      // Prefix with +
      cleanedPhone = '+' + cleanedPhone;
    } else {
      // Assume it's a 10-digit number without 0 => prefix +234
      cleanedPhone = '+234' + cleanedPhone;
    }

    // Determine user type from current route
    const userType = getUserTypeFromRoute(pathname);

    const formattedData = {
      ...data,
      primaryPhone: cleanedPhone,
      user_type: userType, // Add user type based on route
    };

    // Store user type in global state for use during verification
    setSignupUserType(userType);

    setSignupData({
      email: formattedData.email,
      phoneNumber: formattedData.primaryPhone,
    });
    mutate(formattedData);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  useEffect(() => {
    setIsLoading(isPending);
  }, [isPending]);

  useEffect(() => {
    const errors = form.formState.errors;
    if (errors) {
      const errorMessages = Object.values(errors).map(error => error.message);
      setFormErrors(errorMessages);
    }
  }, [form.formState.errors]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5 w-full">
        <h2 className="text-center text-2xl md3:text-3xl text-eweko_green_dark">
          Create an account
        </h2>
        {formErrors && formErrors.length > 0 && (
          <ErrorDisplay message={formErrors} />
        )}
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  placeholder="Email Address"
                  type="email"
                  {...field}
                  className="outline-none border-b-[1px] border-t-0 border-l-0 border-r-0 border-eweko_green_light py-6 border-t-ring-0 border-l-ring-0 border-r-ring-0 focus-within:ring-0 focus-visible:ring-0 text-lg placeholder:text-eweko_green_dark text-eweko_green_dark"
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="First Name"
                  {...field}
                  className="outline-none border-b-[1px] border-t-0 border-l-0 border-r-0 border-eweko_green_light py-6 border-t-ring-0 border-l-ring-0 border-r-ring-0 focus-within:ring-0 focus-visible:ring-0 text-lg placeholder:text-eweko_green_dark text-eweko_green_dark"
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Last Name"
                  {...field}
                  className="outline-none border-b-[1px] border-t-0 border-l-0 border-r-0 border-eweko_green_light py-6 border-t-ring-0 border-l-ring-0 border-r-ring-0 focus-within:ring-0 focus-visible:ring-0 text-lg placeholder:text-eweko_green_dark text-eweko_green_dark"
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Username"
                  {...field}
                  className="outline-none border-b-[1px] border-t-0 border-l-0 border-r-0 border-eweko_green_light py-6 border-t-ring-0 border-l-ring-0 border-r-ring-0 focus-within:ring-0 focus-visible:ring-0 text-lg placeholder:text-eweko_green_dark text-eweko_green_dark"
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="farmName"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Farm Name"
                  {...field}
                  className="outline-none border-b-[1px] border-t-0 border-l-0 border-r-0 border-eweko_green_light py-6 border-t-ring-0 border-l-ring-0 border-r-ring-0 focus-within:ring-0 focus-visible:ring-0 text-lg placeholder:text-eweko_green_dark text-eweko_green_dark"
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="primaryPhone"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex items-center px-2 w-full outline-none border-b-[1px] border-t-0 border-l-0 border-r-0 border-eweko_green_light border-t-ring-0 border-l-ring-0 border-r-ring-0 focus-within:ring-0 focus-visible:ring-0 text-lg placeholder:text-eweko_green_dark text-eweko_green_dark rounded-lg">
                  <span className="text-[16px]">+234</span>

                  <Input
                    type="text"
                    placeholder="Phone Number"
                    {...field}
                    className="outline-none border-none py-6 border-ring-0 focus-within:ring-0 focus-visible:ring-0 placeholder:text-eweko_green_dark pl-2 shadow-none"
                  />
                </div>
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <section className="relative">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Password"
                    {...field}
                    className="outline-none border-b-[1px] border-t-0 border-l-0 border-r-0 border-eweko_green_light py-6 border-t-ring-0 border-l-ring-0 border-r-ring-0 focus-within:ring-0 focus-visible:ring-0 text-lg placeholder:text-eweko_green_dark text-eweko_green_dark"
                  />
                  <span
                    onClick={togglePasswordVisibility}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer duration-500"
                  >
                    {showPassword ? (
                      <BsFillEyeFill size={16} color="#394532" />
                    ) : (
                      <BsFillEyeSlashFill size={16} color="#394532" />
                    )}
                  </span>
                </section>
              </FormControl>
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="rounded-[10px] px-12 py-6 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-full"
        >
          {isLoading ? (
            <InlineLoader color="white" size="30" textSize="10" />
          ) : (
            'Create Account'
          )}
        </Button>

        <span className="w-full flex justify-center text-center text-[#636363]">
          By clicking Create account, I agree that I have read and accepted the
          Terms of Use and Privacy Policy.
        </span>
      </form>
    </Form>
  );
};
