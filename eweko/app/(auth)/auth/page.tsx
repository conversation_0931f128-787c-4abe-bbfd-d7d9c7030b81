'use client';

import { userCategories } from '@/constants';
import ewekologo from '@/public/ewekologo.png';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect } from 'react';
import { useGlobalState } from '../../../globalStore';

const AuthHome = () => {
  const {
    setSignupData,
    setSelectedOTPchannel,
    setMaskedDetails,
    setAccountCreated,
    setPasswordResetOtp,
  } = useGlobalState();

  useEffect(() => {
    setSignupData(undefined);
    setSelectedOTPchannel(undefined);
    setMaskedDetails({ email: '', primaryPhone: '' });
    setAccountCreated(false);
    setPasswordResetOtp(undefined);
  }, []);

  return (
    <div className="w-[90%] md:w-[85%] mx-auto flex flex-col justify-center items-center gap-6 h-screen text-center overflow-y-auto py-10 bg-white">
      <Link rel="prefetch" href="/">
        <Image
          src={ewekologo}
          alt="EwekoAggregate"
          className="w-[150px] md:w-[200px] max-h-auto object-contain"
          loading="eager"
        />
      </Link>

      <p className="text-eweko_green_dark w-[85%]">
        Kindly select a category that best describes your service or functions
      </p>

      <div className="flex flex-wrap justify-center items-center gap-3 md:gap-6 w-full mt-5 md:mt-8">
        {userCategories.map((cate, i) => (
          <Link
            rel="prefetch"
            href={cate.url}
            className={`flex flex-col justify-center items-center gap-3 border rounded-lg p-3 md:p-6 w-[80%] sm2:w-[45%] md:w-[30%] lg:w-[20%] h-[150px] ${
              cate.isActive
                ? 'border-eweko_green text-eweko_green'
                : 'border-gray-400 cursor-not-allowed pointer-events-none text-gray-400'
            }`}
            key={i}
          >
            {cate.isActive && (
              <cate.icon className="text-[36px] md:text-[48px]" />
            )}
            <span className="">{cate.name}</span>
            {cate.isActive === false && <span>(Coming soon)</span>}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default AuthHome;
