import buyerAuth from '@/public/buyerAuth.png';
import Image from 'next/image';
import React from 'react';

const Layout = async ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex flex-col min-h-screen transition-all duration-500 relative ">
      <main className="w-full flex flex-col md:flex-row flex-wrap">
        <div className="bg-eweko_green text-white p-8 md:p-12 flex flex-col  justify-center h-screen w-full md:w-[50%] relative">
          <div className="xl:p-8">
            <div className="text-3xl sm2:text-4xl sm3:text-5xl md:text-2xl md2:text-3xl md3:text-4xl xl:text-5xl">
              <h1 className="font-jsbold leading-[40px] sm2:leading-[50px] sm3:leading-[60px] md:leading-[40px] md2:leading-[50px] xl:leading-[60px]">
                Transforming Fresh
                <br />
                Produce supply chains,
                <br />
                one customer at a time.
              </h1>
            </div>
            <p className="my-10 sm3:text-xl md3:text-2xl lg:my-5">
              Connecting People, Process & Platform.
            </p>
          </div>
          <div className="relative w-full block md:h-[200px] xl:h-[250px] 2xl:h-[350px]">
            <span className="md:absolute md:right-[-70px] md3:right-[-80px] xl:right-[-100px]">
              <Image
                src={buyerAuth}
                alt="EwekoAggregate"
                className="max-h-auto object-contain"
                loading="eager"
              />
            </span>
          </div>
        </div>

        <div className="py-12 md:p-10 md:py-14 w-full md:w-[50%] flex justify-center items-center overflow-y-auto h-screen relative bg-white">
          {children}
        </div>
      </main>
    </div>
  );
};
export default Layout;
