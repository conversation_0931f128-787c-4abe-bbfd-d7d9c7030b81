import { Metadata } from 'next';
import { Suspense } from 'react';
import { FullscreenLoader } from '../../../components/FullscreenLoader';
import { ResetPassword } from '../components/ResetPassword';

export const metadata: Metadata = {
  title: 'Reset Password | Auth',
  description: '',
};

const BuyerResetPassword = () => {
  return (
    <Suspense fallback={<FullscreenLoader />}>
      <ResetPassword />
    </Suspense>
  );
};

export default BuyerResetPassword;
