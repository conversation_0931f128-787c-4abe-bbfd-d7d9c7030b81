export type SignupFormData = {
  email: string;
  phoneNumber: string;
};

export type MaskedAccountDetails = {
  email: string;
  primaryPhone: string;
};

export type SessionPayload = {
  id: string;
  firstName: string;
  type: string;
};

export interface SignupResponse {
  message: string;
  data: {
    email: string;
    primaryPhone: string;
  };
}

export interface SigninResponse {
  message: string;
  userType: string;
  token: string;
}

export interface Signin2faResponse {
  userId?: string;
  message?: string;
  data?: { email: string; primaryPhone: string };
}

export interface OtpDetails {
  code: string;
  useCase: string;
}

export interface VerifyFormData {
  email?: string;
  primaryPhone?: string;
}

export interface VerifyLogin {
  userId: string | undefined;
  otp: string;
}
