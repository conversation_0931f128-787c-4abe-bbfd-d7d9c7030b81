'use client';

import { InlineLoader } from '@/app/components/InlineLoader';
import { useSafeMutation } from '@/axios/query-client';
import { ErrorDisplay } from '@/components/ErrorDisplay';
import { Button } from '@/components/ui/button';
import { useGlobalState } from '@/globalStore';
import ewekologo from '@/public/ewekologo.png';
import Cookies from 'js-cookie';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { IoReturnUpBack } from 'react-icons/io5';
import { Bounce, toast } from 'react-toastify';
import { AuthRoutes } from '../../auth.routes';
import { OtpInput } from '../../components/OtpInput';
import { VerifyLogin } from '../../types';

const Verify2FA = () => {
  const router = useRouter();

  const { userLoginId, setAuthToken } = useGlobalState();

  const [otpValue, setOtpValue] = useState('');
  const [errors, setErrors] = useState<(string | undefined)[]>();
  const [isLoading, setIsLoading] = useState(false);
  const handleOtpChange = (otp: string) => {
    setOtpValue(otp);
  };

  const { mutate, isPending } = useSafeMutation<
    { message: string; userType: string; token: string },
    Error,
    VerifyLogin
  >(AuthRoutes.verifyLogin, 'post', {
    onSuccess: data => {
      if (data?.message) {
        toast.success(data.message);
      }

      setErrors(undefined);

      setAuthToken(data.token);
      Cookies.set('3wkAt', data.token, { expires: 999 });

      let dashboardUrl = '';

      if (data.userType === 'BUYER') {
        dashboardUrl = '/buyers';
      } else if (data.userType === 'FARMER') {
        dashboardUrl = '/farmers';
      }

      setIsLoading(false);
      router.push(dashboardUrl);
    },
    onError: (error: any) => {
      setErrors(error.errorMessage);
      toast.error(error.errorMessage);
      setIsLoading(false);
    },
  });

  const handleSubmit = async () => {
    setIsLoading(isPending);
    if (otpValue.length < 6) {
      setErrors([
        'Your OTP is incomplete. Please enter the 6-digits code sent to you.',
      ]);
      setIsLoading(isPending);
    } else {
      mutate({
        userId: userLoginId,
        otp: otpValue,
      });
    }
  };

  useEffect(() => {
    setIsLoading(isPending);
  }, [isPending]);

  return (
    <div className="w-full flex flex-col gap-12 bg-white">
      <div
        onClick={() => window.history.back()}
        className={`hover:bg-eweko_green/20 rounded-full h-[60px] w-[60px] flex items-center justify-center cursor-pointer absolute top-[50px] left-[50px] transition-all duration-500`}
      >
        <IoReturnUpBack size={27} className={`text-eweko_green_dark`} />
      </div>

      <div className="w-[90%] sm2:w-[80%] md3:w-[70%] mx-auto flex flex-col gap-12 justify-center items-center overflow-hidden relative">
        <Link rel="prefetch" href="/">
          <Image
            src={ewekologo}
            alt="EwekoAggregate"
            className="w-[250px] max-h-auto object-contain"
            loading="eager"
          />
        </Link>

        <div className="w-full flex flex-col justify-center items-center gap-12">
          <h2 className="text-center text-2xl md3:text-3xl text-eweko_green_dark">
            Verify Your Login
          </h2>

          <p className="text-eweko_green_dark text-lg w-full text-center">
            Kindly input the OTP sent to you
          </p>

          {errors && errors.length > 0 && <ErrorDisplay message={errors} />}

          <div className="w-full flex flex-col gap-12">
            <OtpInput length={6} onChange={handleOtpChange} />

            <Button
              type="button"
              onClick={handleSubmit}
              className="rounded-[10px] px-12 py-6 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-full"
            >
              {isLoading ? (
                <InlineLoader color="white" size="30" textSize="10" />
              ) : (
                'Proceed'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Verify2FA;
