'use client';
import { useSafeMutation, useSafeQuery } from '@/axios/query-client';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { AllRoutes } from '../../../../../routes';
import { Cart, CartItem } from '../../types';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { BiMinus, BiSolidTrashAlt } from 'react-icons/bi';
import { BsPlus } from 'react-icons/bs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../../../components/ui/table';
import { useGlobalState } from '../../../../../globalStore';
import { InlineLoader } from '../../../../components/InlineLoader';
import { FreshlyHarvested } from './FreshlyHarvested';
import axiosInstance from '../../../../../axios/axios.instance';

interface ShoppingCartItem {
  _id: string; // Unique identifier for the cart item
  produceId: string; // ID of the produce
  name: string; // Name of the produce
  price: number; // Price per unit
  stock: number; // Stock available
  moq: number;
  quantity: number; // Quantity in the cart
  totalPrice: number; // Total price for this item (quantity × price)
  images: string[]; // Array of image URLs
  category: string; // Category of the produce
}

export const CartPage = () => {
  const { loggedInUser, cart, setCart } = useGlobalState();
  const router = useRouter();
  const [item2update, setItem2update] = useState<string>('');
  const [refresh, setRefresh] = useState(false);
  const [cartUpdated, setCartUpdated] = useState(false);
  const [cartItems, setCartItems] = useState<ShoppingCartItem[]>([]); // State to store fetched cart items
  const [loading, setLoading] = useState<boolean>(true); // Loading state for fetching cart
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Fetch the user's cart from the backend
  const fetchCart = async () => {
    try {
      if (!loggedInUser) return;

      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_APIURL}/cart`
      );
      const cartData = response.data; // Assuming the response contains the cart details
      // Map over the cart items and extract the necessary details


      const enrichedCartItems = (cartData.items || []).map((item: any) => ({
        _id: item._id, // Cart item ID
        produceId: item.produce._id, // Produce ID
        name: item.produce.name, // Produce name
        price: item.produce.price, // Produce price
        stock: item.produce.stock, // Produce stock
        quantity: item.quantity, // Quantity in the cart
        totalPrice: item.totalPrice, // Total price for this item
        images: item.produce.images || [], // Produce images
        moq: item.produce.minOrderQty,
        category: item.produce.categoryId?.name || 'Unknown', // Produce category
      }));

      setCartItems(enrichedCartItems); // Set the enriched cart items in state
      setCart(cartData)

      setLoading(false);
    } catch (error) {
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (loggedInUser?.id) {
      fetchCart(); // Fetch cart data when the user ID is available
    }
  }, [loggedInUser?.id]);
  // Mutation to update cart item quantity using PATCH method
  const { mutate: updateCartItemMutation } = useSafeMutation<
    any,
    Error,
    { itemId: string; data: any }
  >(
    `${process.env.NEXT_PUBLIC_APIURL}/cart/items/`, // Base URL (not used directly in the mutationFn)
    'patch', // HTTP method
    {
      mutationFn: async ({ itemId, data }: { itemId: string; data: any }) => {
        // Dynamically construct the API URL with the itemId
        const url = `${process.env.NEXT_PUBLIC_APIURL}/cart/items/${itemId}`;
        // Send a PATCH request to update the cart item
        return axiosInstance.patch(url, data);
      },
      onSuccess: (data, variables) => {
        const { itemId } = variables;

        fetchCart(); // Refetch the cart after updating
      },
      onError: (error, variables) => {
        const { itemId } = variables;
        setErrorMessage('An error occurred while updating the cart item.');
      },
    }
  );

  // Function to handle quantity update
  // Function to handle quantity update
  // Function to handle quantity update
  const handleQuantityUpdate = (
    produceId: string,
    newQuantity: number,
    item: ShoppingCartItem
  ) => {
    try {
      if (!isValidMongoId(produceId)) {
        setErrorMessage('Invalid product ID.');
        return; // Exit early if produceId is invalid
      }

      // Extract minimumOrderQuantity, availableQuantity, itemId, and price from the selected produce
      const {
        moq: minimumOrderQuantity = item.moq,
        stock: availableQuantity = item.stock,
        _id: itemId, // Extract the itemId (unique identifier for the cart item)
        price, // Extract the price of the produce
      } = item;

      // Ensure the quantity does not go below the minimumOrderQuantity
      if (newQuantity < minimumOrderQuantity) {
        setErrorMessage(
          `Quantity cannot be less than the minimum order quantity (${minimumOrderQuantity}) for this produce.`
        );
        return;
      }

      // Ensure the quantity does not exceed the availableQuantity
      if (newQuantity > availableQuantity) {
        setErrorMessage(
          `Quantity cannot exceed the available quantity (${availableQuantity}) for this produce.`
        );
        return;
      }

      setErrorMessage('');

      // Prepare the request body with a valid produceId, quantity, and price
      const requestBody = {
        produceId, // MongoDB ID of the product
        quantity: newQuantity,
        price: price, // Include the price field in the request body
      };

      // Trigger the mutation with the itemId and request body
      updateCartItemMutation({ itemId, data: requestBody });
    } catch (error) {
      setErrorMessage('An unexpected error occurred while updating the cart.');
    }
  };

  // Helper function to validate MongoDB ObjectID
  const isValidMongoId = (id: string): boolean => {
    // Use a regex pattern to validate the MongoDB ObjectID format
    const mongoIdPattern = /^[0-9a-fA-F]{24}$/;
    return mongoIdPattern.test(id);
  };

  // Mutation to remove a cart item using DELETE method
  const { mutate: removeFromCartMutation } = useSafeMutation<
    any,
    Error,
    { itemId: string }
  >(`${process.env.NEXT_PUBLIC_APIURL}/cart/items/`, 'delete', {
    mutationFn: async ({ itemId }: { itemId: string }) => {
      // Dynamically construct the API URL with the itemId
      const url = `${process.env.NEXT_PUBLIC_APIURL}/cart/items/${itemId}`;
      // Send a DELETE request to remove the item
      return axiosInstance.delete(url);
    },
    onSuccess: (data, variables) => {
      const { itemId } = variables;
      fetchCart(); // Refetch the cart after removing
    },
    onError: (error, variables) => {
      const { itemId } = variables;
    },
  });

  // Function to handle item removal
  // Function to handle item removal
  const handleRemoveItem = (item: ShoppingCartItem) => {
    try {
      const { _id: itemId } = item; // Extract the itemId (unique identifier for the cart item)

      if (!isValidMongoId(itemId)) {
        return; // Exit early if itemId is invalid
      }

      // Trigger the mutation with the itemId
      removeFromCartMutation({ itemId });
    } catch (error) {}
  };

  useEffect(() => {
    if (cartItems) {
      setLoading(false);
    }
  }, [cartItems, loading]);

  if (loading) {
    return (
      <div className="w-full flex flex-col gap-6 py-0 ">
        <div className="w-full my-16 flex items-center justify-center">
          <InlineLoader color="eweko_green" size="60" textSize="20" />
        </div>
      </div>
    );
  }

  if (cart && cart.items.length === 0) {
    return (
      <div className="text-eweko_red flex flex-col items-center justify-center font-bold mt-12 gap-6">
        <p>Your Shopping Cart is empty!</p>
        <Link
          href="/buyers/produce"
          className="rounded-[10px] px-8 py-4 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-fit"
        >
          Visit Produce
        </Link>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col gap-12">
      <div className="w-full flex flex-col gap-8">
        <h1 className="text-xl font-bold">
          Cart {cartItems.length > 0 && <span>({cartItems.length})</span>}
        </h1>
        <div className="flex flex-col lg:flex-row flex-wrap gap-8">
          <div className="bg-white border border-[#d9d9d9] rounded-[10px] w-full lg:w-[65%] p-6">
            <div className="overflow-x-auto">
              <Table className="min-w-[640px]">
                <TableHeader>
                  <TableRow className="text-[#636363]/70">
                    <TableHead className="pb-4 pt-5 w-[300px]">
                      Product Details
                    </TableHead>
                    <TableHead className="pb-4 pt-5 w-[150px]">
                      Price/Kg (NGN)
                    </TableHead>
                    <TableHead className="pb-4 pt-5 w-[150px]">
                      Quantity (Kg)
                    </TableHead>
                    <TableHead className="pb-4 pt-5 w-[100px] text-right">
                      Total (NGN)
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {cartItems.map((item: ShoppingCartItem, idx: number) => (
                    <TableRow key={idx}>
                      <TableCell className="flex flex-col gap-1">
                        <div className="flex gap-4 items-center">
                          <span>
                            <img
                              src={item.images[0] || undefined} // Use the first image in the array
                              alt={item.name}
                              width={120}
                              height={68}
                              className="w-[68px] xl:w-[120px] h-[60px] object-cover rounded-lg"
                            />
                          </span>
                          <div className="flex flex-col gap-1">
                            <p className="text-[#6b6b6b] text-[12px]">
                              {item.category} {/* Display the category */}
                            </p>
                            <p className="text-eweko_green_dark text-[16px]">
                              {item.name} {/* Display the produce name */}
                            </p>
                            <p className="text-[#6b6b6b] text-[12px]">
                              In Stock ({item.stock}) {/* Display the stock */}
                            </p>
                          </div>
                        </div>
                        <div
                          onClick={() => handleRemoveItem(item)}
                          className="flex gap-2 items-center text-eweko_green_light cursor-pointer hover:font-bold"
                        >
                          <BiSolidTrashAlt />
                          <span>Remove</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-eweko_green_dark">
                        N{item.price.toLocaleString()} {/* Display the price */}
                      </TableCell>
                      <TableCell className="flex items-center gap-3">
                        {/* Decrease Button */}
                        <span
                          onClick={() =>
                            handleQuantityUpdate(
                              item.produceId,
                              item.quantity - 1,
                              item
                            )
                          }
                          className="bg-eweko_green_dark/70 hover:bg-eweko_green_dark text-white rounded-md w-[30px] h-[30px] flex items-center justify-center cursor-pointer"
                        >
                          <BiMinus size={21} />
                        </span>

                        {/* Display Quantity */}
                        <span>{item.quantity}</span>

                        {/* Increase Button */}
                        <span
                          onClick={() =>
                            handleQuantityUpdate(
                              item.produceId,
                              item.quantity + 1,
                              item
                            )
                          }
                          className="bg-eweko_green_dark/70 hover:bg-eweko_green_dark text-white rounded-md w-[30px] h-[30px] flex items-center justify-center cursor-pointer"
                        >
                          <BsPlus size={21} />
                        </span>
                      </TableCell>
                      <TableCell className="text-right text-eweko_green_dark font-[600]">
                        N{item.totalPrice.toLocaleString()}{' '}
                        {/* Display the total price */}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
          <div className="w-full lg:w-[28%] h-fit">
            <div className="bg-white border border-[#d9d9d9] rounded-[10px] p-6 w-full h-fit flex flex-col gap-8">
              <h1 className="text-eweko_green_dark text-[18px]">
                Cart Summary
              </h1>
              <div className="w-full flex flex-col gap-6">
                <div className="w-full flex justify-between items-center">
                  <p>Subtotal</p>
                  <p className="font-bold">
                    N{cart && cart.totalCost.toLocaleString()}
                    {/* Display the total cost */}
                  </p>
                </div>
                <Link
                  href="/buyers/checkout"
                  className="rounded-[10px] px-8 py-5 text-lg duration-500 transition-all bg-eweko_green_dark text-white hover:bg-black hover:font-bold leading-none w-full text-center"
                >
                  Checkout N{cart && cart.totalCost.toLocaleString()}
                </Link>
              </div>
            </div>
          </div>
        </div>

        <FreshlyHarvested />
      </div>
    </div>
  );
};
