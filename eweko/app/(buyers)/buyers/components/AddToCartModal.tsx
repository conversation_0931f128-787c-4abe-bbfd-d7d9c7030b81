'use client';

import { useSafeMutation } from '@/axios/query-client'; // Replace with your actual library or custom hook
import { useBodyScrollLock } from '@/hooks/use-body-lock';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { MdClose } from 'react-icons/md';
import { TbAlertCircle } from 'react-icons/tb';
import { ErrorDisplay } from '../../../../components/ErrorDisplay';
import { Button } from '../../../../components/ui/button';
import { Input } from '../../../../components/ui/input';
import { useGlobalState } from '../../../../globalStore';
import { toast } from 'react-toastify';

interface AddToCartProps {
  setAlertMessage?: (message: string) => void;
  isSingleProductPage?: boolean; // Flag to check if it's the single product page
}

export const AddToCartModal: React.FC<AddToCartProps> = ({
  setAlertMessage,
}) => {
  const {
    isAddToCartModalOpen,
    setIsAddToCartModalOpen,
    produceToAdd,
    setProduceToAdd,
    setCart,
    cart,
  } = useGlobalState();

  const { userLoginId } = useGlobalState();
  const ref = useRef<HTMLDivElement>(null);
  const router = useRouter();

  const handleClickOutside = (event: MouseEvent) => {
    if (ref.current && !ref.current.contains(event.target as Node)) {
      setIsAddToCartModalOpen(false);
    }
  };

  useEffect(() => {
    if (isAddToCartModalOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isAddToCartModalOpen]);

  useBodyScrollLock(isAddToCartModalOpen);

  const [qty, setQty] = useState<number>(0);
  const [qtyError, setQtyError] = useState<string[] | undefined>([]);
  const [message, setMessage] = useState<string>('');

  const handleQtyChange = (qty: string) => {
    const parsedQty = Number(qty);
    if (isNaN(parsedQty)) {
      setQtyError(['Please enter a valid quantity.']);
    } else if (parsedQty < produceToAdd.minOrderQty) {
      setQtyError([`Minimum order quantity is ${produceToAdd.minOrderQty}`]);
    } else if (parsedQty > produceToAdd.stock) {
      setQtyError([`Available quantity is ${produceToAdd.stock}`]);
    } else {
      setQtyError(undefined);
    }
    setQty(parsedQty);
  };

  // Define the mutation for adding to cart
  const userId = userLoginId; // Replace with actual user ID logic

  const apiUrl = `${process.env.NEXT_PUBLIC_APIURL}/cart/items`;

  const { mutate: addToCartMutation, isPending } = useSafeMutation<
    any,
    Error,
    any
  >(apiUrl, 'post', {
    onSuccess: data => {
      setCart(data);
      if (setAlertMessage) {
        setAlertMessage('Product successfully added to cart');
        setTimeout(() => setAlertMessage(''), 4000);
      }

      setIsAddToCartModalOpen(false);
      setProduceToAdd(undefined);
      router.refresh();
    },
    onError: (error: any) => {
      console.debug('Failed to add item to cart:', error?.response?.data);
      setMessage('Failed to add item to cart. Please try again.');
      if (error.message) {
        toast.error(error?.message);
      }
      if (typeof error === 'object' && error?.errorMessage) {
        toast.error(error?.errorMessage);
      }
    },
  });

  const handleAddToCartClick = async () => {
    if (!qtyError || qtyError.length === 0) {
      addToCartMutation({
        produceId: produceToAdd._id,
        quantity: qty,
        price: produceToAdd.price,
      });
    }
  };

  return (
    <>
      {isAddToCartModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 bg-opacity-50">
          <div
            ref={ref}
            className="bg-[#fcfcfc] rounded-lg shadow-lg w-[90%] max-w-md p-6 md:p-10 relative"
          >
            <MdClose
              onClick={() => setIsAddToCartModalOpen(false)}
              className="absolute top-3 right-3 text-[#626262] hover:text-eweko_green"
              size={27}
            />
            <div className="w-full flex flex-col gap-6">
              <h2 className="text-xl text-eweko_green_dark text-center">
                Enter Desired Quantity
              </h2>
              <div className="flex flex-col gap-2">
                <Input
                  type="text"
                  placeholder="Enter your desired quantity..."
                  className="w-full h-full px-2 py-4 rounded-[10px] outline-none border-[1px] border-[#d9d9d9] bg-transparent focus-visible:ring-transparent transition-all duration-300"
                  onChange={e => handleQtyChange(e.target.value)}
                />
                <div className="flex items-center gap-2">
                  <TbAlertCircle size={14} />
                  <small className="text-eweko_green_dark">
                    Minimum order quantity is {produceToAdd.minOrderQty}Kg
                  </small>
                </div>
                {qtyError && qtyError.length > 0 && (
                  <ErrorDisplay message={qtyError} />
                )}
              </div>
              <div className="w-full flex flex-col gap-3 text-eweko_green_dark">
                <div className="w-full flex justify-between items-center">
                  <span className="font-bold">Produce</span>
                  <span className="font-bold">{produceToAdd?.name}</span>
                </div>
                <div className="w-full flex justify-between items-center">
                  <span className="font-[500]">Desired Quantity (Kg)</span>
                  <span className="">{qty}</span>
                </div>
                <div className="w-full flex justify-between items-center">
                  <span className="font-[500]">Price / Kg (NGN)</span>
                  <span className="">
                    {produceToAdd?.price.toLocaleString()}
                  </span>
                </div>
                <div className="w-full flex justify-between items-center">
                  <span className="font-bold">Total Price (NGN)</span>
                  <span className="font-bold">
                    {(produceToAdd?.price * qty).toLocaleString()}
                  </span>
                </div>
              </div>
              <Button
                type="button"
                onClick={handleAddToCartClick}
                disabled={!!qtyError || isPending}
                className={`rounded-[10px] px-12 py-6 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-full`}
              >
                {isPending ? 'Adding...' : 'Add To Cart'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
