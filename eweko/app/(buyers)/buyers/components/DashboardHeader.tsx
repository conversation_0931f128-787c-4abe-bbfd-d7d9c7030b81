'use client';

import { useRouter } from 'next/navigation';
import { Skeleton } from '../../../../components/ui/skeleton';
import { useGlobalState } from '../../../../globalStore';
import { capitalizeFirstLetter } from '../../../../lib/utils';
import { format } from 'date-fns';

export const DashboardHeader = () => {
  const currentDate = format(new Date(), 'EEEE, do MMMM, yyyy');
  const { loggedInUser } = useGlobalState();

  return (
    <header>
      {!loggedInUser ? (
        <div className="flex items-center gap-2 w-full">
          <Skeleton className="h-6 w-[10%] bg-white rounded-lg border" />
          <Skeleton className="h-6 w-[20%] bg-white rounded-lg border" />
        </div>
      ) : (
        <h1 className="text-[min(10vw,18px)] flex flex-col sm:flex-row sm:items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="font-bold">Welcome</span>
            <span className="font-bold">
              {capitalizeFirstLetter(loggedInUser.firstName)}
            </span>
          </div>
          <span className="text-[min(10vw,16px)] leading-none mt-[2px]">
            - {currentDate}
          </span>
        </h1>
      )}
    </header>
  );
};
