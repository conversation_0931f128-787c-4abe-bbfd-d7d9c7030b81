'use client';

import { buyerSidebarItems } from '@/constants';
import { useAnimate } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
import { useGlobalState } from '../../../../globalStore';

export const Sidebar = () => {
  const pathname = usePathname();
  const { isSidebarOpen, isContentReady } = useGlobalState();

  // Initialize Framer Motion animations
  const [scope, animate] = useAnimate();

  useEffect(() => {
    if (isSidebarOpen) {
      animate(scope.current, { x: 0 }, { duration: 0.5, ease: 'easeInOut' });
    } else {
      // Slide out to the left
      animate(
        scope.current,
        { x: '-120%' },
        { duration: 0.5, ease: 'easeInOut' }
      );
    }
  }, [isSidebarOpen, isContentReady, animate, scope]);

  return (
    <aside
      ref={scope}
      className="fixed top-[111px] left-0 max-w-[120px] px-4 md:px-8 h-screen bg-white border-r-[1px] border-[#d9d9d9] flex flex-col items-center z-20"
    >
      <nav className="flex flex-col gap-8 pt-12">
        {buyerSidebarItems.map((item, i) => (
          <Link
            rel="prefetch"
            key={i}
            href={item.url}
            className="flex flex-col gap-3 items-center justify-center max-w-[100px] group transition-all duration-300 px-4 md:px-8"
          >
            <Image
              src={item.icon}
              alt={item.name}
              width={30}
              height={30}
              loading="eager"
              className={`${
                pathname === item.url
                  ? 'scale-125'
                  : 'w-[30px] h-[30px] group-hover:scale-125'
              } transition-all duration-300`}
            />

            <p
              className={`${
                pathname === item.url
                  ? 'text-[14px] font-jssembold'
                  : 'text-[13px] text-eweko_green_dark'
              } text-center group-hover:text-[14px] group-hover:font-jssembold transition-all duration-300 hidden md:flex`}
            >
              {item.name}
            </p>
          </Link>
        ))}
      </nav>
    </aside>
  );
};
