'use client';

import { FC, useEffect, useState } from 'react';
import { useSafeQuery } from '../../../../axios/query-client';
import { AllRoutes } from '../../../../routes';
import { Order } from '../types';
import { isSameMonth, subMonths, parseISO, subDays, isAfter } from 'date-fns';
import { useGlobalState } from '../../../../globalStore';
import { useRouter } from 'next/navigation';

enum OrderStatus {
  PROCESSING = 'Processing',
  SHIPPED = 'Shipped',
  DELIVERED = 'Delivered',
  CANCELLED = 'Cancelled',
}

interface Metric {
  label: string;
  value: number;
  percentageChange: number;
}

export const Metric: FC = () => {
  const [metricsData, setMetricsData] = useState<Metric[]>([]);
  const { loggedInUser } = useGlobalState();
  const router = useRouter();

  // ✅ Fetch all orders ONLY AFTER loggedInUser is available
  const { data, isSuccess, isLoading, isError } = useSafeQuery<any>(
    ['orders'],
    `${AllRoutes.orders}/buyer`,
    {
      enabled: Boolean(loggedInUser), // ✅ Only fetch when loggedInUser is available
    }
  );

  useEffect(() => {
    if (isSuccess && data?.data) {
      const buyerOrders: Order[] = data.data;

      // Dates for filtering
      const currentDate = new Date();
      const lastMonthDate = subMonths(currentDate, 1);
      const oneDayAgo = subDays(currentDate, 1);
      const oneDayAgoLastMonth = subDays(lastMonthDate, 1);

      // Filter orders
      const currentMonthOrders = buyerOrders.filter(order =>
        isSameMonth(parseISO(order.createdAt), currentDate)
      );
      const previousMonthOrders = buyerOrders.filter(order =>
        isSameMonth(parseISO(order.createdAt), lastMonthDate)
      );

      // Helper function to calculate percentage change
      const calculatePercentageChange = (
        previous: number,
        current: number
      ): number => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return Number((((current - previous) / previous) * 100).toFixed(1));
      };

      // ✅ Compute Metrics
      const metrics: Metric[] = [
        {
          label: 'New Orders',
          value: buyerOrders.filter(order =>
            isAfter(parseISO(order.createdAt), oneDayAgo)
          ).length,
          percentageChange: calculatePercentageChange(
            previousMonthOrders.filter(order =>
              isAfter(parseISO(order.createdAt), oneDayAgoLastMonth)
            ).length,
            currentMonthOrders.filter(order =>
              isAfter(parseISO(order.createdAt), oneDayAgo)
            ).length
          ),
        },
        {
          label: 'Shipped Orders',
          value: currentMonthOrders.filter(
            order => order.status === OrderStatus.SHIPPED
          ).length,
          percentageChange: calculatePercentageChange(
            previousMonthOrders.filter(
              order => order.status === OrderStatus.SHIPPED
            ).length,
            currentMonthOrders.filter(
              order => order.status === OrderStatus.SHIPPED
            ).length
          ),
        },
        {
          label: 'Cancelled Orders',
          value: currentMonthOrders.filter(
            order => order.status === OrderStatus.CANCELLED
          ).length,
          percentageChange: calculatePercentageChange(
            previousMonthOrders.filter(
              order => order.status === OrderStatus.CANCELLED
            ).length,
            currentMonthOrders.filter(
              order => order.status === OrderStatus.CANCELLED
            ).length
          ),
        },
        {
          label: 'Total Orders',
          value: currentMonthOrders.length,
          percentageChange: calculatePercentageChange(
            previousMonthOrders.length,
            currentMonthOrders.length
          ),
        },
      ];

      setMetricsData(metrics);
    }
  }, [data, isSuccess]);

  // ✅ Refresh page once `metricsData` is set
  useEffect(() => {
    if (!data || isError) {
      router.refresh();
    }
  }, [data, isError, router]);

  // ✅ Show loading UI while data is fetching
  if (isLoading) {
    return (
      <section className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-8">
        {Array.from({ length: 4 }).map((_, idx) => (
          <div
            key={idx}
            className="border rounded-lg p-6 bg-white animate-pulse"
          >
            <div className="h-4 bg-gray-100 rounded" />
            <div className="h-8 mt-4 bg-gray-100 rounded" />
            <div className="h-4 mt-4 bg-gray-100 rounded" />
          </div>
        ))}
      </section>
    );
  }

  // ✅ Render the metric cards
  return (
    <section className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-8">
      {metricsData.map((metric: Metric, idx: number) => (
        <div
          key={idx}
          className="border rounded-lg p-4 grid gap-2 text-[#292929] hover:text-white hover:bg-eweko_green_dark text-left pl-6 bg-white shadow-sm font-jsregular"
        >
          <p className="text-[min(10vw,16px)]">{metric.label}</p>
          <h2 className="text-[min(10vw,42px)] font-bold">
            {metric.value.toLocaleString('en-GB')}
          </h2>

          <p
            className={`text-[min(10vw,14px)] text-[#868686]  ${
              metric.percentageChange === 0
                ? 'text-gray-500'
                : metric.percentageChange < 0
                  ? 'text-red-500'
                  : 'text-green-500'
            }`}
          >
            {metric.percentageChange === 0
              ? '—'
              : metric.percentageChange < 0
                ? '↓'
                : '↑'}{' '}
            {Math.abs(metric.percentageChange).toFixed(1)}%{' '}
            <span className="text-[#868686] ml-2">since last month</span>
          </p>
        </div>
      ))}
    </section>
  );
};
