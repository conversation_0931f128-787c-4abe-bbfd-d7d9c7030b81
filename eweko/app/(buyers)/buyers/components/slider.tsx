'use client';
import Image from 'next/image';
import { Autoplay, Navigation, Pagination } from 'swiper/modules'; // Correct module imports for Swiper v8+
import { Swiper, SwiperSlide } from 'swiper/react';

import farmer from '@/public/farmer.png';
import variety from '@/public/varity.png';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const BdSlider = () => {
  return (
    <section className=" w-full  justify-center items-center   pb-10">
      <Swiper
        modules={[Autoplay, Navigation, Pagination]}
        spaceBetween={20}
        slidesPerView={2}
        autoplay={{
          delay: 2000,
          disableOnInteraction: false,
        }}
        breakpoints={{
          300: {
            slidesPerView: 1, // Small screens (up to 640px)
          },
          568: {
            slidesPerView: 1, // Medium screens (568px to 767px)
          },
          768: {
            slidesPerView: 1, // Tablets (768px and above)
          },
          1000: {
            slidesPerView: 1, // Desktops (1024px and above)
          },
          1280: {
            slidesPerView: 2, // Large screens (1280px and above)
          },
        }}
        // navigation
        //  pagination={{ clickable: true }}
        loop={true}
        grabCursor={true}
        speed={1000} // Smooth transition duration (ms)
        className="rounded-lg overflow-hidden"
      >
        <SwiperSlide>
          <div className=" pt-5">
            <div className="bg-[#85B04C] text-white px-6 py-1 rounded-lg flex flex-col md:flex-row items-center gap-2 overflow-y-visible relative">
              <div className="flex-1">
                <h2 className="text-[15px]  text-eweko_green_dark md:text-[19px] font-bold">
                  Secure Your Future <br /> with{' '}
                  <span className="text-white">Contract Farming!</span>
                </h2>
                <p className="mt-2 text-[10px] xl:text-[7px] text-eweko_green_dark">
                  Grow with Confidence – Let’s Build Sustainable Agriculture
                  Together!
                </p>
              </div>
              <div className="flex-1">
                <Image
                  src={farmer} // Replace with your image path
                  alt="Farmer"
                  width={130}
                  height={300}
                  className=" mx-auto xl:size-[120px] lg:mt-[-10px]   "
                />
              </div>
              <button className="mt-6 text-[11px] lg:text-[12px] bg-eweko_green_dark text-white px-2 lg:px-4 py-2 rounded-lg hover:bg-eweko_green_light transition">
                Learn More
              </button>
            </div>
          </div>
        </SwiperSlide>

        <SwiperSlide>
          <div className=" pt-4">
            <div className="bg-white text-white px-6 py-2  rounded-lg flex flex-col md:flex-row items-center ">
              <div className="flex-1">
                <Image
                  src={variety} // Replace with your image path
                  alt="Farmer"
                  width={200}
                  height={300}
                  className=" "
                />
              </div>
              <div className="flex-1">
                <h2 className="text-[20px] text-eweko_green_dark md:text-[20px] font-bold">
                  Empowering Your <br />
                  <span className="text-eweko_green_light">
                    {' '}
                    Agribusiness
                  </span>{' '}
                  Journey!
                </h2>
                <p className="mt-4 text-[10px] text-eweko_green_dark">
                  Farm Management | Input Supply | Market Access
                </p>
              </div>
              <button className="mt-6 11 lg:text-[12px] bg-eweko_green_dark text-white px-2 lg:px-4 py-2 rounded-lg hover:bg-eweko_green_light transition">
                Learn More
              </button>
            </div>
          </div>
        </SwiperSlide>

        <SwiperSlide>
          <div className=" pt-5">
            <div className="bg-[#85B04C] text-white px-6 py-1 rounded-lg flex flex-col md:flex-row items-center gap-2 overflow-y-visible relative">
              <div className="flex-1">
                <h2 className="text-[15px]  text-eweko_green_dark md:text-[19px] font-bold">
                  Secure Your Future <br /> with{' '}
                  <span className="text-white">Contract Farming!</span>
                </h2>
                <p className="mt-2 text-[10px] xl:text-[7px] text-eweko_green_dark">
                  Grow with Confidence – Let’s Build Sustainable Agriculture
                  Together!
                </p>
              </div>
              <div className="flex-1">
                <Image
                  src={farmer} // Replace with your image path
                  alt="Farmer"
                  width={130}
                  height={300}
                  className=" mx-auto xl:size-[120px] lg:mt-[-10px]   "
                />
              </div>
              <button className="mt-6 text-[11px] lg:text-[12px] bg-eweko_green_dark text-white px-2 lg:px-4 py-2 rounded-lg hover:bg-eweko_green_light transition">
                Learn More
              </button>
            </div>
          </div>
        </SwiperSlide>

        <SwiperSlide>
          <div className=" pt-4">
            <div className="bg-white text-white px-6 py-2  rounded-lg flex flex-col md:flex-row items-center ">
              <div className="flex-1">
                <Image
                  src={variety} // Replace with your image path
                  alt="Farmer"
                  width={200}
                  height={300}
                  className=" "
                />
              </div>
              <div className="flex-1">
                <h2 className="text-[20px] text-eweko_green_dark md:text-[20px] font-bold">
                  Empowering Your <br />
                  <span className="text-eweko_green_light">
                    {' '}
                    Agribusiness
                  </span>{' '}
                  Journey!
                </h2>
                <p className="mt-4 text-[10px] text-eweko_green_dark">
                  Farm Management | Input Supply | Market Access
                </p>
              </div>
              <button className="mt-6 11 lg:text-[12px] bg-eweko_green_dark text-white px-2 lg:px-4 py-2 rounded-lg hover:bg-eweko_green_light transition">
                Learn More
              </button>
            </div>
          </div>
        </SwiperSlide>
      </Swiper>
    </section>
  );
};
export default BdSlider;
