'use client';

import React from 'react';
import { Header } from './components/Header';
import { Sidebar } from './components/Sidebar';
import { useGlobalState } from '../../../globalStore';

const Layout = ({ children }: { children: React.ReactNode }) => {
  const { isSidebarOpen } = useGlobalState();

  return (
    <div className="min-h-screen bg-[#f9f9f9] relative overflow-hidden">
      <Sidebar />
      <Header />
      <main
        className={`w-full h-full pt-[111px] ${!isSidebarOpen ? 'pl-0' : 'sm3:pl-[100px] md:pl-[120px]'} transition-all duration-300`}
      >
        {children}
      </main>
    </div>
  );
};

export default Layout;
