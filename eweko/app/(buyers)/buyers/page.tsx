import type { Metadata } from 'next';
import { AddToCartModal } from './components/AddToCartModal';
import { DashboardHeader } from './components/DashboardHeader';
import { Metric } from './components/metric';
import { Products } from './components/product';
import BdSlider from './components/slider';

export const metadata: Metadata = {
  title: {
    default: 'Dashboard | EwekoAggregate',
    template: '%s | EwekoAggregate',
  },
  description: '',
};

const BuyerDashboard = async () => {
  return (
    <div className="w-full p-[20px] md:p-[30px] xl:p-[40px] ">
      <AddToCartModal />
      <div className="w-full flex flex-col gap-6 py-0 ">
        <DashboardHeader />
        <Metric />
        <Products />
        <BdSlider />
      </div>
    </div>
  );
};

export default BuyerDashboard;
