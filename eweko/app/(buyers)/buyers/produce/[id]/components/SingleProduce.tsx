'use client';

import React from 'react';
import { IProduce, Produce } from '../../../types';
import { useRouter } from 'next/navigation';
import { Button } from '../../../../../../components/ui/button';
import { TbTruckDelivery } from 'react-icons/tb';
import ReviewCard from './reviewCard';
import { useGlobalState } from '../../../../../../globalStore';

const reviews = [
  {
    name: '<PERSON>',
    date: '10 Nov, 2024',
    text: 'Lorem ipsum dolor sit amet consectetur. Aliquam ullamcorper malesuada facilisi sit tellus diam. Sed ac diam neque enim nulla enim vitae interdum.',
    stars: 4,
  },
  {
    name: '<PERSON>',
    date: '12 Nov, 2024',
    text: 'Great service! The quality of the produce exceeded my expectations.',
    stars: 5,
  },
  {
    name: 'Sophia',
    date: '15 Nov, 2024',
    text: 'The delivery was a bit late, but the produce was worth the wait.',
    stars: 3,
  },
];

export const SingleProduce = ({ produce }: { produce: IProduce }) => {
  const router = useRouter();
  const { setIsAddToCartModalOpen, setProduceToAdd } = useGlobalState();

  function updateMainImage(image: string) {
    const mainImageElement = document.getElementById('main-produce-image');
    if (mainImageElement) {
      mainImageElement.setAttribute('src', image);
    }
  }

  return (
    <div className="flex flex-col gap-4 items-start">
      <Button
        onClick={() => router.back()}
        className="cursor-pointer flex items-center text-eweko_green_light hover:text-white hover:bg-eweko_green_light bg-transparent transition-all duration-300 p-0 hover:px-4"
        variant="ghost"
      >
        <span className="mr-1 text-lg">&lt; </span> Back
      </Button>

      <div className="flex gap-8 bg-white rounded-[12px] p-6 w-full flex-col md:flex-row md:justify-between">
        <div className="flex flex-col-reverse md:flex-row gap-6 md:gap-4 md3:gap-6 xl:gap-8 w-full md:w-[45%]">
          <div className="md:w-[60px] w-full ">
            <div className="flex flex-row md:flex-col gap-4 md:gap-2">
              {produce.images.map((image: string, idx: number) => (
                <div
                  key={idx}
                  className="relative cursor-pointer w-[60px] h-[60px] overflow-hidden"
                  onMouseEnter={e => {
                    // Update the main image on hover
                    const imgElement = e.currentTarget.querySelector('img');
                    if (imgElement) {
                      imgElement.setAttribute('data-main-image', image);
                      updateMainImage(image); // Call the update function
                    }
                  }}
                  onClick={e => {
                    // Update the main image on click
                    const imgElement = e.currentTarget.querySelector('img');
                    if (imgElement) {
                      imgElement.setAttribute('data-main-image', image);
                      updateMainImage(image); // Call the update function
                    }
                  }}
                >
                  <img
                    src={image}
                    alt={`${produce.name} ${idx + 1}`}
                    className="w-[60px] h-[40px] object-cover rounded-md"
                    width={60}
                    height={40}
                    data-main-image={idx === 0 ? image : ''} // Set initial main image
                  />
                </div>
              ))}
            </div>
          </div>

          <div className="overflow-hidden rounded-lg md:h-[250px] md2:h-[300px] md3:h-[350px] lg:h-[400px]">
            <img
              id="main-produce-image" // Add an ID to the main image for easy access
              src={produce.images[0]} // Default to the first image
              alt="main Image"
              className="w-full h-full object-cover"
              width={550}
              height={400}
            />
          </div>
        </div>

        <div className="flex flex-col gap-6 w-full md:w-[45%]">
          <div className="flex flex-col gap-2">
            <h1 className="text-[min(10vw,24px)] font-bold mb-2 text-eweko_green_dark">
              {produce.name}
            </h1>
            <p className="text-eweko_green_dark mb-2">
              {'★'.repeat(5)} <span className="text-gray-600">(100)</span>
            </p>
            <p className="text-[min(10vw,17px)] font-semibold text-eweko_green_dark">
              Price/Kg N{produce.price}
            </p>
            <p className="text-[min(10vw,17px)] text-eweko_green_dark">
              Negotiable Price N{produce.negotiablePrice}
            </p>
            <p className="flex gap-2 text-[min(10vw,15px)] items-center text-eweko_green_dark">
              <TbTruckDelivery /> Shipping calculated at checkout
            </p>
            <div className="text-gray-700 text-[min(10vw,15px)] items-center flex flex-row justify-between  border-b-2 py-2">
              <p>Category: </p>
              <p> {produce.category?.name}</p>
            </div>
            <div className="text-gray-700 text-[13px] md:text-[15px] flex flex-row justify-between border-b-2 py-2">
              <p>Minimum Order Quantity:</p>
              <p>{produce.minOrderQty}kg</p>
            </div>
            <div className="text-gray-700 text-[min(10vw,15px)] items-center flex flex-row justify-between ">
              <p> Available Quantity: </p>
              <p> {produce.stock}kg</p>
            </div>
          </div>

          <div className="flex flex-col gap-4">
            <Button
              className="cursor-pointer bg-eweko_green_dark w-full text-white text-[min(10vw,15px)] px-4 py-2 rounded-lg"
              onClick={() => {
                setIsAddToCartModalOpen(true);
                setProduceToAdd(produce);
              }}
            >
              Add To Cart
            </Button>
            <Button
              className="cursor-pointer bg-eweko_green_light text-white text-[min(10vw,15px)] px-4 py-2 rounded-lg"
              onClick={() => alert('Negotiation feature is coming soon!')}
            >
              Negotiate
            </Button>
          </div>

          <div className="mt-8">
            <h2 className="text-lg md:text-xl font-bold mb-2 text-eweko_green_dark">
              Description
            </h2>
            <p className="text-gray-700 text-[min(10vw,15px)]">
              {produce.description}
            </p>
          </div>

          <div className="mt-8">
            <h2 className="text-lg md:text-xl font-bold mb-2 text-eweko_green_dark">
              Negotiation History
            </h2>
            <p className="text-gray-700 text-[min(10vw,15px)]">
              No activity yet.
            </p>
          </div>
        </div>
      </div>

      <div className="flex gap-8 bg-white rounded-[12px] p-6 w-full flex-col">
        <div className="flex items-center">
          <h2 className="text-2xl font-bold mb-2 text-eweko_green_light">
            Reviews
          </h2>
          <p className="text-eweko_green_dark lg:text-[22px] ml-3">
            {'★'.repeat(5)}
            <span className="text-gray-200">{'★ '}</span>
            <span className="text-gray-600 lg:text-[14px]">(100)</span>
          </p>
        </div>

        <div className="grid grid-cols-1 sm3:grid-cols-2 lg:grid-cols-3">
          {reviews.map((review, index) => (
            <ReviewCard
              key={index}
              name={review.name}
              date={review.date}
              text={review.text}
              stars={review.stars}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
