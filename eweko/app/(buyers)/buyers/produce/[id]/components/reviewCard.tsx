export default function ReviewCard({
  name,
  date,
  text,
  stars,
}: {
  name: string;
  date: string;
  text: string;
  stars: number;
}) {
  return (
    <div className=" rounded-lg p-4 mb-4 ">
      {/* Reviewer Name */}
      <h3 className="text-eweko_green_light text-lg font-semibold">{name}</h3>
      <div className=" flex">
        {/* Star Rating */}
        <div className="flex items-center ">
          {Array.from({ length: 5 }, (_, index) => (
            <span
              key={index}
              className={`text-[15px] ${index < stars ? 'text-eweko_green_dark' : 'text-gray-300'}`}
            >
              ★
            </span>
          ))}
        </div>
        {/* Review Date */}
        <p className="text-[12px] mt-1 text-gray-700 ml-2">{date}</p>
      </div>

      {/* Review Text */}
      <p className="text-gray-700 text-sm mt-4 ">{text}</p>
    </div>
  );
}
