

// components/ChatBox.tsx
import React, { useState, useEffect } from 'react';
import io from 'socket.io-client';
import send from "@/public/send.png";
import Image from "next/image";
import defaultProfilePic from "@/public/user.png";  // Import your default profile image

interface ChatBoxProps {
  isOpen: boolean;
  onClose: () => void;
  role: 'buyer' | 'farmer'; // Role prop to differentiate between buyer and farmer

}

const socket = io();

const ChatBox: React.FC<ChatBoxProps> = ({ isOpen, onClose, role }) => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<{ sender: string; text: string; time: string; profilePic?: string }[]>([]);

  useEffect(() => {
    socket.on('receiveMessage', (msg: { sender: string; text: string; time: string; profilePic?: string }) => {
      setMessages((prev) => [...prev, msg]);
    });

    return () => {
      socket.off('receiveMessage');
    };
  }, []);

  const handleSendMessage = () => {
    if (message.trim() === '') return;

    const newMessage = { sender: role, text: message, time: new Date().toLocaleTimeString() };

    //const newMessage = { sender: 'buyer', text: message, time: new Date().toLocaleTimeString() };
    socket.emit('sendMessage', newMessage);
    setMessages([...messages, newMessage]);
    setMessage('');
  };

  if (!isOpen) return null;

  return (
    <div
      className={`fixed bg-white md:w-96 lg:w-[500px] w-auto max-w-md rounded-lg shadow-lg z-50
                  bottom-auto
                  right-3 left-3 top-36  sm:transform sm3:right-10
                  sm3:bottom-auto justify-end sm3:left-auto`}
      style={{ maxHeight: 'calc(100vh - 1rem)' }}
    >
      {/* Header */}
      <div className="bg-eweko_green_light text-white flex gap-10 items-center p-4 rounded-t-lg">
        <button onClick={onClose} className="text-xl font-extrabold">&#x2190;</button>
        <h2 className="font-bold text-[13px] md:text-[16px]">Negotiation Room</h2>
      </div>

      {/* Instructions */}
      <div className="p-4 text-[11px] md:text-[15px]  text-center text-gray-600">
        Negotiate produce price with the farmer. You will be notified once the farmer responds. All chats are saved during the negotiation process.
      </div>

      {/* Chat Area */}
      <div className="p-4 h-64 overflow-y-auto space-y-4">
        {messages.map((msg, index) => (
          <div key={index} className={`flex ${msg.sender === 'buyer' ? 'justify-end' : 'justify-start'}`}>
            {/* User's profile picture or default icon */}
            <div className="flex items-center space-x-2">

              <div
                className={`${msg.sender === 'buyer' ? 'bg-gray-100' : 'bg-gray-100'} p-2 rounded-lg max-w-xs shadow-md`}
              >
                <p className="md:text-[15px] text-[10px]">{msg.text}</p>
                <p className="md:text-[10px] text-[8px] text-gray-500 text-right">{msg.time}</p>
              </div>
              <div className="w-8 h-8">
                <Image
                  src={msg.profilePic || defaultProfilePic}  // Use the profile picture or default icon
                  alt="Profile"
                  width={25}
                  height={25}
                  className="rounded-full size-4 md:size-6"
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Input Area */}
      <div className="px-4 py-2 flex border-t">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Write your message"
          className="flex-1 rounded-l-lg p-2 focus:outline-none"
        />
        <button
          onClick={handleSendMessage}
          className=" text-white my-2 w-6 h-6 rounded-r-lg bg-[url('../public/send.png')] bg-contain repeat-0"
        >
        </button>
      </div>
    </div>
  );
};

export default ChatBox;
