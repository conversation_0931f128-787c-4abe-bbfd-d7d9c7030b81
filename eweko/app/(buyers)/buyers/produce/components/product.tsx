'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { useGlobalState } from '../../../../../globalStore';
import { IProduce } from '../../types';
import { AllRoutes } from '../../../../../routes';
import { useSafeQuery } from '../../../../../axios/query-client';
import { Button } from '../../../../../components/ui/button';

export const Products = () => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false); // For dialog box
  const [selectedProduceId, setSelectedProduceId] = useState<string | null>(
    null
  );

  const [fetchedProduces, setFetchedProduces] = useState<IProduce[] | null>(
    null
  );
  const [filteredProduces, setFilteredProduces] = useState<IProduce[] | null>(
    null
  );

  const router = useRouter();
  const { setIsAddToCartModalOpen, setProduceToAdd } = useGlobalState();

  const [uniqueCats, setUniqueCats] = useState<string[] | null>(null);
  function getUniqueCategoryNames(produces: IProduce[]): string[] {
    const categorySet = new Set(produces.map(produce => produce.category.name));
    return Array.from(categorySet);
  }

  const {
    data: produces,
    isSuccess,
    isLoading,
  } = useSafeQuery<any>(['produces'], `${AllRoutes.produce}`, {
    enabled: true,
  });

  useEffect(() => {
    if (isSuccess) {
      setFetchedProduces(produces.data);
    }
  }, [isSuccess]);

  useEffect(() => {
    if (fetchedProduces) {
      const cats = getUniqueCategoryNames(fetchedProduces);
      setUniqueCats(cats);
    }
  }, [fetchedProduces]);

  useEffect(() => {
    if (fetchedProduces) {
      const produces = fetchedProduces.filter((item: IProduce) =>
        selectedCategory ? item.category.name === selectedCategory : true
      );

      setFilteredProduces(produces);
    }
  }, [selectedCategory, fetchedProduces]);

  if (isLoading) {
    return (
      <>
        <section className="flex flex-row justify-between mt-2">
          <article className="flex flex-col xl:flex-row">
            <div className="flex flex-row gap-4 justify-between">
              {Array.from({ length: 3 }).map((_, idx) => (
                <article
                  key={idx}
                  className="border w-[150px] rounded-lg p-6 bg-white animate-pulse"
                ></article>
              ))}
            </div>
          </article>
          <div className="border w-[150px] rounded-lg p-6 bg-white animate-pulse"></div>
        </section>

        <section className="grid grid-cols-1 sm:grid-cols-2 md3:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, idx) => (
            <article
              key={idx}
              className="border rounded-lg p-6 bg-white animate-pulse"
            >
              <div className="h-[100px] bg-gray-100 rounded" />
              <div className="h-4 mt-4 bg-gray-100 rounded" />
              <div className="h-4 mt-4 bg-gray-100 rounded" />
              <div className="h-4 mt-4 bg-gray-100 rounded items-right" />
            </article>
          ))}
        </section>
      </>
    );
  }

  return (
    <>
      <section className="flex flex-col-reverse sm2:flex-row justify-between mt-6">
        <div className="flex flex-col xl:flex-row w-full gap-6 xl:gap-16 xl:items-center">
          <h2 className="font-bold w-fit text-[min(10vw,20px)]">Top Sellers</h2>
          <div className="flex flex-wrap w-fit gap-3">
            <Button
              onClick={() => setSelectedCategory(null)}
              className={`px-4 py-1 w-fit text-[min(10vw,14px)] rounded-lg bg-eweko_green_dark text-white shadow-md transition-all duration-300`}
            >
              All Produce
            </Button>
            {uniqueCats?.map((category, idx) => (
              <Button
                key={idx}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-1 w-fit text-[min(10vw,14px)] rounded-lg bg-eweko_green_dark text-white shadow-md transition-all duration-300 ${
                  selectedCategory === category
                    ? 'bg-[#394532] text-white hover:bg-white hover:text-eweko_green_dark'
                    : 'bg-white text-[#394532] hover:bg-eweko_green_dark hover:text-white'
                }`}
              >
                {category || 'All Produce'}
              </Button>
            ))}
          </div>
        </div>
        <div className="w-full flex justify-end sm2:mb-0 mb-4">
          <Button
            className="px-4 py-1 w-fit text-[min(10vw,14px)] rounded-lg bg-eweko_green_light text-white shadow-md transition-all duration-300"
            onClick={() => router.push(`/buyers/produce`)}
          >
            All Produce
          </Button>
        </div>
      </section>

      <section className="mt-6 grid grid-cols-1 sm:grid-cols-2 md3:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredProduces?.map((item, idx) => (
          <article
            key={idx}
            className="border rounded-lg flex flex-col bg-white shadow-sm"
          >
            <Image
              src={item.images[0]}
              alt={item.name}
              width={140}
              height={40}
              className="w-full h-40 object-cover rounded-b-none rounded-md cursor-pointer"
              onClick={() => router.push(`/buyers/produce/${item._id}`)} // Navigate on click
            />
            <div className="p-6 flex flex-col gap-5">
              <div>
                <h3 className="font-bold text-[17px] md:text-[19px] xl:text-[21px] mb-2 text-[background: #394532]">
                  {item.name}
                </h3>
                <p className="text-[12px] sm3:text-[13px] xl:text-[15px] text-[#626262] font-sans font-semibold">
                  Available Qty: {item.stock}kg
                </p>
                <p className="text-[10px] md:text-[12px] text-[#626262] font-sans font-semibold">
                  (NB) Minimum Order Quantity (MOQ): {item.minOrderQty}kg
                </p>
              </div>
              <div className="flex flex-row justify-between">
                <div className="text-base justify-end">
                  <p className="text-[#626262] text-[12px] sm3:text-[13px] xl:text-[14px] font-sans font-semibold">
                    Price{' '}
                  </p>
                  <span className="text-[14px] md:text-[16px] font-bold">
                    {item.price}/kg
                  </span>
                </div>

                <button
                  type="button"
                  className="mt-2 bg-[#394532] text-[11px] md:text-[15px] text-white px-4 py-2 rounded-lg"
                  onClick={() => {
                    setIsAddToCartModalOpen(true);
                    setProduceToAdd(item);
                  }}
                >
                  Add To Cart
                </button>
              </div>
            </div>
          </article>
        ))}
      </section>
    </>
  );
};
