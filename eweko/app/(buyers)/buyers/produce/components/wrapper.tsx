'use client';

import { useEffect, useState } from 'react';
import { FullscreenLoader } from '../../../../components/FullscreenLoader';
import { Products } from './product';
import ProductSlider from './slider';

export const Wrapper = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  return (
    <div className="w-full flex flex-col gap-2">
      <h1 className="font-semibold text-[min(10vw,25px)]">Produce</h1>
      <ProductSlider />
      <Products />
    </div>
  );
};
