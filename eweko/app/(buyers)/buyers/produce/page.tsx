import type { Metadata } from 'next';

import { IsContentReady } from '../../../components/IsContentReady';
import { AddToCartModal } from '../components/AddToCartModal';
import { Wrapper } from './components/wrapper';

export const metadata: Metadata = {
  title: {
    default: 'Produce | EwekoAggregate',
    template: '%s | EwekoAggregate',
  },
  description: '',
};

const ProductPage = async () => {
  return (
    <div className="w-full p-[20px] md:p-[30px] xl:p-[40px] ">
      <AddToCartModal />
      <Wrapper />
    </div>
  );
};

export default ProductPage;
