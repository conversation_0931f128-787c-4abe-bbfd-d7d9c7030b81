'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { <PERSON>unce, toast } from 'react-toastify';
import { z } from 'zod';
import { useSafeMutation } from '../../../../../axios/query-client';
import { ErrorDisplay } from '../../../../../components/ErrorDisplay';
import { Button } from '../../../../../components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from '../../../../../components/ui/form';
import { Input } from '../../../../../components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../components/ui/select';
import { statesLgas } from '../../../../../constants';
import { useGlobalState } from '../../../../../globalStore';
import { AllRoutes } from '../../../../../routes';
import { InlineLoader } from '../../../../components/InlineLoader';
import { Address } from '../../../../(buyers)/buyers/types';

const editAddressFormSchema = z.object({
  houseNumber: z.string(),
  streetName: z.string(),
  community: z.string(),
  lga: z.string(),
  state: z.string(),
});

interface EditAddressFormProps {
  address: Address | null;
}

export const EditAddressForm = ({ address }: EditAddressFormProps) => {
  const { setSettingsAddressStep, settingsAddrEdited, setSettingsAddrEdited } =
    useGlobalState();
  const [formErrors, setFormErrors] = useState<(string | undefined)[]>([]);
  const [stateLgas, setStateLgas] = useState<string[] | null>(null);

  const form = useForm<z.infer<typeof editAddressFormSchema>>({
    resolver: zodResolver(editAddressFormSchema),
    defaultValues: {
      houseNumber: '',
      streetName: '',
      community: '',
      lga: '',
      state: '',
    },
  });

  const statePicked = form.watch('state');

  useEffect(() => {
    if (statePicked) {
      const selectedState = statesLgas.find(
        state => state.name === statePicked
      );
      const lgas = selectedState ? selectedState.lgas : [];
      setStateLgas(lgas);
    }
  }, [statePicked]);

  const { mutate, isPending } = useSafeMutation<
    Address,
    Error,
    {
      houseNumber: string;
      streetName: string;
      lga: string;
      state: string;
    }
  >(`${AllRoutes.addresses}/${address?.id}`, 'patch', {
    onSuccess: data => {
      toast.success('Address edited successfully');
      setSettingsAddrEdited(true);
      setTimeout(() => {
        setSettingsAddressStep(1);
      }, 2000);
    },
    onError: (error: any) => {
      if (typeof error === 'object') {
        if (Array.isArray(error?.errorMessage)) {
          // Set form errors from array
          setFormErrors(error.errorMessage);

          // Show each error as a toast
          error.errorMessage.forEach((msg: string) => {
            toast.error(msg);
          });
        } else if (typeof error?.errorMessage === 'string') {
          setFormErrors([error.errorMessage]);
          toast.error(error.errorMessage);
        } else if (error?.message) {
          setFormErrors([error.message]);
          toast.error(error.message);
        } else {
          setFormErrors(['An unexpected error occurred.']);
          toast.error('An unexpected error occurred.');
        }
      } else {
        setFormErrors(['An unknown error occurred.']);
        toast.error('An unknown error occurred.');
      }
    },
  });

  const onSubmit = (data: z.infer<typeof editAddressFormSchema>) => {
    if (address) {
      mutate(data);
    }
  };

  useEffect(() => {
    const errors = form.formState.errors;
    if (errors) {
      const errorMessages = Object.values(errors).map(error => error.message);
      setFormErrors(errorMessages);
    }
  }, [form.formState.errors]);

  useEffect(() => {
    if (address) {
      form.reset({
        houseNumber: address.houseNumber || '',
        streetName: address.streetName || '',
        community: address.community || '',
        state: address.state || '',
        lga: address.lga || '',
      });
    }
  }, [address, form]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8 w-full">
        {formErrors.length > 0 && <ErrorDisplay message={formErrors} />}

        <div className="w-full flex flex-col sm2:flex-row gap-6 sm2:gap-0 items-center justify-center sm2:justify-between">
          <FormField
            control={form.control}
            name="houseNumber"
            render={({ field }) => (
              <FormItem className="w-full sm2:w-[25%] sm3:w-[20%] md2:w-[12%]">
                <FormControl>
                  <Input
                    placeholder="House No"
                    {...field}
                    className="outline-none border border-[#d9d9d9] py-5 focus-within:ring-0 focus-visible:ring-0 placeholder:text-[#545454] text-eweko_green_dark bg-[#f5f5f5] rounded-[7px]"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="streetName"
            render={({ field }) => (
              <FormItem className="w-full sm2:w-[40%]">
                <FormControl>
                  <Input
                    placeholder="Street Name"
                    {...field}
                    className="outline-none border border-[#d9d9d9] py-5 focus-within:ring-0 focus-visible:ring-0 placeholder:text-[#545454] text-eweko_green_dark bg-[#f5f5f5] rounded-[7px]"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="community"
            render={({ field }) => (
              <FormItem className="w-full sm2:w-[40%]">
                <FormControl>
                  <Input
                    placeholder="Community"
                    {...field}
                    className="outline-none border border-[#d9d9d9] py-5 focus-within:ring-0 focus-visible:ring-0 placeholder:text-[#545454] text-eweko_green_dark bg-[#f5f5f5] rounded-[7px]"
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        <div className="w-full flex flex-col sm2:flex-row gap-6 sm2:gap-0 items-center justify-center sm2:justify-between">
          <FormField
            control={form.control}
            name="state"
            render={({ field }) => (
              <FormItem className="w-full sm2:w-[48%]">
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="State" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {statesLgas.map(state => (
                      <SelectItem key={state.name} value={state.name}>
                        {state.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lga"
            render={({ field }) => (
              <FormItem className="w-full sm2:w-[48%]">
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="LGA" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {stateLgas &&
                      stateLgas.map(lga => (
                        <SelectItem key={lga} value={lga}>
                          {lga}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
        </div>

        <Button
          type="submit"
          className="rounded-[10px] px-12 py-6 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-full"
        >
          {isPending ? (
            <InlineLoader color="white" size="30" textSize="10" />
          ) : (
            'Save'
          )}
        </Button>
      </form>
    </Form>
  );
};
