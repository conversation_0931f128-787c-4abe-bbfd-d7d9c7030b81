import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { DialogFooter } from '../../../../../components/ui/dialog';

interface AddCardFormProps {
  onSave: (card: {
    name: string;
    number: string;
    bank: string;
    branch: string;
  }) => void;
  existingCards: string[]; // Array of existing card numbers
}

export default function AddCardForm({
  onSave,
  existingCards,
}: AddCardFormProps) {
  const [cardNumber, setCardNumber] = useState('');
  const [accountName, setAccountName] = useState('');
  const [bankName, setBankName] = useState('');
  const [branchName, setBankBranch] = useState('');
  const [errors, setErrors] = useState<{
    accountName?: string;
    cardNumber?: string;
    bankName?: string;
    branchName?: string;
  }>({});
  const [message, setMessage] = useState<{
    type: 'success' | 'error';
    text: string;
  } | null>(null);

  const validate = () => {
    const newErrors: {
      accountName?: string;
      cardNumber?: string;
      bankName?: string;
      branchName?: string;
    } = {};
    if (!accountName) newErrors.accountName = 'Account name is required.';
    if (!cardNumber || cardNumber.toString().length! >= 11)
      newErrors.cardNumber = 'Account number must be 16 digits.';
    if (existingCards.includes(cardNumber))
      newErrors.cardNumber = 'This Account number already exists.';
    if (!bankName) newErrors.bankName = 'include your bank Name .';
    if (!branchName) newErrors.branchName = 'bank branch is needed';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validate()) {
      setMessage({ type: 'error', text: 'invalid card number' });
      return;
    }
    setMessage({ type: 'success', text: 'Card has been added successfully ' });
    setTimeout(() => {
      onSave({
        name: accountName,
        number: cardNumber,
        bank: bankName,
        branch: branchName,
      });
      setMessage(null); // Clear the message after successful submission
    }, 1500); // Delay for displaying success message
  };

  return (
    <div className="space-y-4  px-4 md3:px-6 ">
      {/* Success or Error Message */}
      {message && (
        <div
          className={`w-full p-3 text-center rounded-md text-[13px] ${
            message.type === 'success'
              ? 'bg-eweko_green_light text-white'
              : 'bg-red-300 text-red-600 '
          }`}
        >
          {message.text}
        </div>
      )}

      {/* Form Fields */}
      <div>
        <Input
          id="accountNumber"
          placeholder="Account Number"
          value={cardNumber}
          onChange={e => {
            setCardNumber(e.target.value);
            setErrors(prev => ({ ...prev, cardNumber: '' }));
            setMessage(null);
          }}
          className="h-[40px]"
        />
        {errors.cardNumber && (
          <p className=" text-[10px] text-red-500 ">{errors.cardNumber}</p>
        )}
      </div>
      <div>
        <Input
          id="accountName"
          placeholder="Account Holder's Name"
          value={accountName}
          onChange={e => {
            setAccountName(e.target.value);
            setErrors(prev => ({ ...prev, accountName: '' })); // Clear specific error
            setMessage(null);
          }}
          className="h-[40px]"
        />
        {errors.accountName && (
          <p className="text-red-500 text-[10px]">{errors.accountName}</p>
        )}
      </div>

      <div className="w-full">
        <Input
          id="bankName"
          placeholder="Bank Name"
          value={bankName}
          onChange={e => {
            setBankName(e.target.value);
            setErrors(prev => ({ ...prev, expiryDate: '' }));
            setMessage(null);
          }}
          className="h-[40px]"
        />
        {errors.bankName && (
          <p className=" text-[10px] text-red-500 ">{errors.bankName}</p>
        )}
      </div>
      <div className="w-full">
        <Input
          id="BranchName"
          placeholder="Branch Name"
          value={branchName}
          onChange={e => {
            setBankBranch(e.target.value);
            setErrors(prev => ({ ...prev, cvv: '' }));
            setMessage(null);
          }}
          className="h-[40px]"
        />
        {errors.branchName && (
          <p className=" text-[10px] text-red-500 ">{errors.branchName}</p>
        )}
      </div>

      <DialogFooter>
        <Button
          variant="default"
          onClick={handleSubmit}
          className="w-full bg-eweko_green_dark"
        >
          Add Card
        </Button>
      </DialogFooter>
    </div>
  );
}
