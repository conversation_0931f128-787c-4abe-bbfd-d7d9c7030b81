import { useEffect, useState } from 'react';
import {
  useSafeMutation,
  useSafeQuery,
} from '../../../../../axios/query-client';
import { useGlobalState } from '../../../../../globalStore';
import { AllRoutes } from '../../../../../routes';
import { toast } from 'react-toastify';

export default function Notification() {
  const { loggedInUser } = useGlobalState();
  const [preferences, setPreferences] = useState<any>([]);

  // Fetch user preferences
  const { data: userPreferences, refetch } = useSafeQuery<any>(
    ['preferences'],
    `${AllRoutes.preferences}/${loggedInUser?.id}`,
    {
      enabled: true,
      refetchOnWindowFocus: true,
    }
  );

  // Mutation hook for updating preferences
  const { mutateAsync: updatePreferences } = useSafeMutation<
    any,
    Error,
    Record<string, any>
  >(`${AllRoutes.preferences}/${loggedInUser?.id}`, 'patch', {
    onSuccess: () => {
      toast.success('Preferences updated successfully');
      refetch(); // Refresh preferences after update
    },
    onError: (error: any) => {
      console.error('Error updating preferences:', error);
      toast.error(
        error?.response?.data?.message || 'Failed to update preferences'
      );
    },
  });

  useEffect(() => {
    if (userPreferences) {
      setPreferences([
        {
          title: 'Receive Promotions',
          description: 'Receive promotional emails or messages',
          label: 'receivePromotions',
          value: userPreferences.receivePromotions,
          type: 'boolean',
        },
        {
          title: 'General Updates',
          description: 'Notifications about general updates and announcements',
          label: 'generalUpdates',
          value: userPreferences.generalUpdates,
          type: 'boolean',
        },
        {
          title: 'Order Updates',
          description: 'Notifications related to order status and tracking',
          label: 'orderUpdates',
          value: userPreferences.orderUpdates,
          type: 'boolean',
        },
        {
          title: 'Transaction Updates',
          description: 'Notifications about financial transactions',
          label: 'transactionUpdates',
          value: userPreferences.transactionUpdates,
          type: 'boolean',
        },
        {
          title: 'Payment Updates',
          description: 'Alerts regarding payment status and issues',
          label: 'paymentUpdates',
          value: userPreferences.paymentUpdates,
          type: 'boolean',
        },
        {
          title: 'Delivery Updates',
          description: 'Real-time updates about order delivery status',
          label: 'deliveryUpdates',
          value: userPreferences.deliveryUpdates,
          type: 'boolean',
        },
      ]);
    }
  }, [userPreferences]);

  // Handler to update preferences based on type
  const handleToggle = async (
    label: string,
    currentValue: any,
    type: string
  ) => {
    let updatedValue;

    if (type === 'boolean') {
      updatedValue = !currentValue; // Toggle boolean value
    } else {
      return; // Skip if it's not a boolean type
    }

    await updatePreferences({ [label]: updatedValue });
  };

  return (
    <section className="w-full mx-auto p-6 rounded-lg">
      <ul className="">
        {preferences.map((item: any, i: number) => (
          <li
            key={i}
            className="flex justify-between md:gap-0 gap-10 items-center py-4"
          >
            <div>
              <h3 className="md:text-[16px] text-[14px] md3:text-lg font-medium text-gray-900">
                {item.title}
              </h3>
              <p className="md:text-[13px] text-[10px] md3:text-sm text-gray-500">
                {item.description}
              </p>
            </div>

            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={item.value}
                className="sr-only peer"
                onChange={() => handleToggle(item.label, item.value, item.type)}
              />
              <div
                className="md:w-11 md:h-6 w-8 h-4 bg-gray-300 rounded-full flex items-center px-1
                peer-checked:bg-eweko_green_light peer-checked:justify-end"
              >
                <div className="md:w-4 md:h-4 w-2 h-2 bg-white rounded-full"></div>
              </div>
            </label>
          </li>
        ))}
      </ul>
    </section>
  );
}
