'use client';
import edit from '@/public/icons/edit.png';
import Image from 'next/image';
import { uploadImageToCloudinary } from '@/app/utils/cloudinary';
import { useSafeMutation, useSafeQuery } from '@/axios/query-client';
import { useEffect, useState } from 'react';
import axiosInstance from '../../../../../axios/axios.instance';
import { useGlobalState } from '../../../../../globalStore';
import { InlineLoader } from '../../../../components/InlineLoader';
import { AllRoutes } from '../../../../../routes';
import { Separator } from '../../../../../components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../components/ui/select';
import { toast } from 'react-toastify';

interface UserAddress {
  id: string;
  userId: string;
  houseNumber: string;
  streetName: string;
  lga: string;
  state: string;
  community: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export default function Settings() {
  const { loggedInUser } = useGlobalState(); // Get the logged-in user ID
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<any>({});
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(true); // Loading state for fetching user data
  const [addresses, setAddresses] = useState<any[]>([]); // State to store fetched addresses
  const [defaultAddress, setDefaultAddress] = useState<UserAddress | null>(
    null
  ); // State to store the default address
  const [uploading, setUploading] = useState(false);
  // const [changedFields, setChangedFields] = useState<Record<string, any>>({});
  const [changedFields, setChangedFields] = useState<
    { name: string; value: any }[]
  >([]);

  // Fetch user data
  const { data: userData } = useSafeQuery<any>(
    ['user'],
    `${AllRoutes.users}/buyers/${loggedInUser?.id}`,
    {
      enabled: !!loggedInUser?.id,
      refetchOnWindowFocus: true,
    }
  );

  useEffect(() => {
    if (userData) {
      if (userData.profile?.profilePicture) {
        setPreviewImage(userData.profile.profilePicture);
      }

      setFormData({
        // Basic account fields
        username: userData.username || '',

        // Profile info
        firstName: userData.profile?.firstName || '',
        lastName: userData.profile?.lastName || '',
        middleName: userData.profile?.middleName || '',
        prefix: userData.profile?.prefix || '',
        gender: userData.profile?.gender || '',
        dateOfBirth: userData.profile?.dateOfBirth || '',

        // Contact info
        email: userData.contact?.email || '',
        primaryPhone: userData.contact?.primaryPhone || '',
        secondaryPhone: userData.contact?.secondaryPhone || '',

        // Business info
        businessName: userData.business?.businessName || '',
      });

      setLoading(false);
    }
  }, [userData]);

  const contactFields = [
    { label: 'Email Address', name: 'email' },
    { label: 'Primary Phone Number', name: 'primaryPhone' },
    { label: 'Secondary Phone Number', name: 'secondaryPhone' },
  ];

  const businessFields = [{ label: 'Business Name', name: 'businessName' }];

  const { data: userAddresses, refetch } = useSafeQuery<UserAddress[]>(
    ['addresses'],
    `${AllRoutes.addresses}?userId=${loggedInUser?.id}`,
    {
      enabled: !!loggedInUser?.id,
      refetchOnWindowFocus: true,
    }
  );

  useEffect(() => {
    if (userAddresses && userAddresses?.length) {
      const defaultAddress = userAddresses.find(address => address.isDefault);
      if (defaultAddress) {
        setDefaultAddress(defaultAddress);
      }
    }
  }, [userAddresses]);

  useEffect(() => {
    const fetchAndSetDefault = async () => {
      const { data, isSuccess } = await refetch();

      if (isSuccess && data && data.length > 0) {
        const defaultAddress = data.find(address => address.isDefault);
        if (defaultAddress) {
          setDefaultAddress(defaultAddress);
        }
      }
    };

    if (loggedInUser?.id) {
      fetchAndSetDefault();
    }
  }, [loggedInUser?.id]);

  // Mutation to update user data on the backend
  const { mutate: updateUserMutation } = useSafeMutation<any, Error, any>(
    `${AllRoutes.users}/buyers/${loggedInUser?.id}`,
    'patch',
    {
      onSuccess: data => {
        toast.success('User updated successfully:', data);
      },
      onError: error => {
        console.error('Failed to update user:', (error as any).response?.data);
      },
    }
  );

  // Handle edit mode
  const handleEdit = () => setIsEditing(true);

  const handleCancel = () => {
    setIsEditing(false);
    setFormData({ ...formData }); // Reset form data to the original state
    setPreviewImage(null); // Clear the preview image
  };

  const handleSave = () => {
    setIsEditing(false);

    const updatedUserData: Record<string, any> = {};

    const phoneFields = ['primaryPhone', 'secondaryPhone'];
    for (const field of changedFields) {
      if (phoneFields.includes(field.name)) {
        const value = field.value;
        const isValid =
          value.startsWith('+234') && value.length >= 11 && value.length <= 14;

        if (!isValid) {
          toast.error(
            `${field.name} must start with +234 and be between 11 to 14 characters.`
          );

          setIsEditing(true);
          return;
        }
      }
    }

    changedFields.forEach(field => {
      const { name, value } = field;

      // Top-level fields
      if (
        [
          'username',
        ].includes(name)
      ) {
        updatedUserData[name] = value;
      }

      // Profile nested fields
      if (
        [
          'firstName',
          'lastName',
          'middleName',
          'prefix',
          'gender',
          'dateOfBirth',
        ].includes(name)
      ) {
        updatedUserData.profile = updatedUserData.profile || {};
        updatedUserData.profile[name] = value;
      }

      // Contact nested fields
      if (['primaryPhone', 'secondaryPhone', 'email'].includes(name)) {
        updatedUserData.contact = updatedUserData.contact || {};
        updatedUserData.contact[name] = value;
      }

      // Business nested fields
      if (['businessName'].includes(name)) {
        updatedUserData.business = updatedUserData.business || {};
        updatedUserData.business[name] = value;
      }
    });

    // Include profile picture if updated
    if (previewImage) {
      updatedUserData.profile = updatedUserData.profile || {};
      updatedUserData.profile.profilePicture = previewImage;
    }

    // Trigger the mutation to update the user data on the backend
    updateUserMutation(updatedUserData);

    // Reset changedFields after saving
    setChangedFields([]);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    // Update formData
    setFormData((prevData: Record<string, any>) => ({
      ...prevData,
      [name]: value,
    }));

    // Update changedFields with only modified fields (avoid duplicates)
    setChangedFields(prev => {
      const exists = prev.find(field => field.name === name);
      if (exists) {
        return prev.map(field =>
          field.name === name ? { ...field, value } : field
        );
      } else {
        return [...prev, { name, value }];
      }
    });
  };

  const setFieldValue = (name: string, value: string) => {
    // Update formData
    setFormData((prevData: any) => ({
      ...prevData,
      [name]: value,
    }));

    // Update changedFields
    setChangedFields(prev => {
      const exists = prev.find(field => field.name === name);
      if (exists) {
        return prev.map(field =>
          field.name === name ? { ...field, value } : field
        );
      } else {
        return [...prev, { name, value }];
      }
    });
  };

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setUploading(true); // Show loading state while uploading

      // Upload the image to Cloudinary
      const cloudinaryUrl = await uploadImageToCloudinary(file);

      // Update the preview image with the Cloudinary URL
      setPreviewImage(cloudinaryUrl);
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload your profile picture. Please try again.');
    } finally {
      setUploading(false); // Hide loading state after upload completes
    }
  };

  if (loading) {
    return (
      <div className="w-full h-[50vh] flex flex-col items-center justify-center">
        <InlineLoader color="eweko_green" size="100" textSize="30" />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg">
      {/* Header */}
      <header className="flex md3:flex-row flex-col justify-between items-center">
        <h1 className="text-xl md3:text-2xl font-bold mx-4 md:mx-8 my-6">
          {isEditing ? 'Editing Profile' : ''}
        </h1>
      </header>

      <div
        className={`${isEditing ? 'flex flex-col md3:w-auto w-full gap-6' : ''}`}
      >
        {/* Profile Picture Section */}
        <section
          className={`flex md3:flex-row flex-col justify-between items-start ${
            isEditing ? 'ml-8' : 'px-4 md3:px-14'
          }`}
        >
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative w-20 h-20 rounded-full bg-gray-200">
              <img
                src={previewImage || formData?.profile?.profilePicture}
                alt="Profile"
                className="w-full h-full object-cover rounded-full"
              />
              {isEditing && (
                <label className="absolute bottom-0 right-0 bg-gray-600 text-white text-xs px-2 py-1 rounded cursor-pointer">
                  Upload
                  <input
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageChange}
                  />
                </label>
              )}
            </div>

            {!isEditing && (
              <div className="my-4">
                <h2 className="text-[14px] md3:text-lg font-semibold">
                  {formData.firstName || 'No first name'}{' '}
                  {formData.lastName || 'No last name'}
                </h2>
                {defaultAddress ? (
                  <p className="text-[14px] text-gray-500">
                    {defaultAddress?.lga}, {defaultAddress?.state}
                  </p>
                ) : (
                  <p className="text-[14px] text-gray-500">
                    No default address
                  </p>
                )}
              </div>
            )}
          </div>

          {!isEditing && (
            <div
              onClick={handleEdit}
              className="px-2 md3:px-4 py-1 text-[13px] md:text-[16px] mb-5 md3:mb-0 md3:py-2 flex gap-2 bg-transparent border border-eweko_green_dark text-eweko_green_dark rounded cursor-pointer"
            >
              <p>Edit</p>
              <Image
                src={edit}
                width={110}
                height={10}
                alt="edit"
                className="w-[14px] h-[17px]"
              />
            </div>
          )}
        </section>

        {/* Editable Form */}
        <form className={`${isEditing ? 'w-full mx-[-10px]' : ''}`}>
          <section className="grid grid-cols-1 mb-6">
            {/* Personal Info */}
            <div className="px-4 md3:px-14 py-4">
              <h2 className="text-[14px] font-semibold md:text-[17px] md3:text-[20px] text-eweko_green_dark mb-4">
                Personal Information
              </h2>
              <div className="grid sm3:grid-cols-2 gap-4 md:gap-8 md3:gap-6">
                {/* Prefix */}
                <div>
                  <label className="text-sm font-medium mb-1 block text-gray-700">
                    Prefix
                  </label>
                  {isEditing ? (
                    <Select
                      value={formData.prefix}
                      onValueChange={value => setFieldValue('prefix', value)}
                    >
                      <SelectTrigger className="w-full sm:w-[50%] sm3:w-[90%] lg2:w-[60%]">
                        <SelectValue placeholder="Select prefix" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Mr">Mr</SelectItem>
                        <SelectItem value="Mrs">Mrs</SelectItem>
                        <SelectItem value="Miss">Miss</SelectItem>
                        <SelectItem value="Dr">Dr</SelectItem>
                        <SelectItem value="Chief">Chief</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-800">
                      {formData.prefix || 'No prefix set'}
                    </p>
                  )}
                </div>

                {/* First Name */}
                <InputField
                  label="First Name"
                  name="firstName"
                  value={formData.firstName || 'No first name set'}
                  isEditing={isEditing}
                  onChange={handleChange}
                />

                {/* Middle Name */}
                <InputField
                  label="Middle Name"
                  name="middleName"
                  value={formData.middleName || 'No middle name set'}
                  isEditing={isEditing}
                  onChange={handleChange}
                />

                {/* Last Name */}
                <InputField
                  label="Last Name"
                  name="lastName"
                  value={formData.lastName || 'No last name set'}
                  isEditing={isEditing}
                  onChange={handleChange}
                />

                {/* Gender */}
                <div>
                  <label className="text-sm font-medium mb-1 block text-gray-700">
                    Gender
                  </label>
                  {isEditing ? (
                    <Select
                      value={formData.gender}
                      onValueChange={value => setFieldValue('gender', value)}
                    >
                      <SelectTrigger className="w-full sm:w-[50%] sm3:w-[90%] lg2:w-[60%]">
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-800">
                      {formData.gender || 'No gender set'}
                    </p>
                  )}
                </div>

                {/* Date of Birth */}
                <div>
                  <label className="text-sm font-medium mb-1 block text-gray-700">
                    Date of Birth
                  </label>
                  {isEditing ? (
                    <>
                      <input
                        type="text"
                        name="dateOfBirth"
                        value={formData.dateOfBirth || ''}
                        onChange={handleChange}
                        placeholder="YYYY-MM-DD"
                        className="w-full sm:w-[50%] sm3:w-[90%] lg2:w-[60%] mt-1 px-3 py-2 border rounded-md text-sm focus:outline-none"
                      />
                      {!/^\d{4}-\d{2}-\d{2}$/.test(
                        formData.dateOfBirth || ''
                      ) &&
                        formData.dateOfBirth && (
                          <p className="text-red-500 text-sm mt-1">
                            Date must be in format YYYY-MM-DD
                          </p>
                        )}
                    </>
                  ) : (
                    <p className="text-sm text-gray-800 mt-1">
                      {formData.dateOfBirth
                        ? formData.dateOfBirth
                        : 'No date of birth set'}
                    </p>
                  )}
                </div>
              </div>

              <Separator className="mt-6" />
            </div>

            {/* Contact Info */}
            <div className="px-4 md3:px-14 py-4">
              <h2 className="text-[14px] font-semibold md:text-[17px] md3:text-[20px] text-eweko_green_dark mb-4">
                Contact Information
              </h2>
              <div className="grid sm3:grid-cols-2 gap-4 md:gap-8 md3:gap-4">
                {contactFields.map(field => (
                  <InputField
                    key={field.name}
                    label={field.label}
                    name={field.name}
                    value={
                      formData[field.name] ||
                      `No ${field.label.toLowerCase()} set`
                    }
                    isEditing={isEditing}
                    onChange={handleChange}
                  />
                ))}
              </div>
              <Separator className="mt-6" />
            </div>

            {/* Business Info */}
            <div className="px-4 md3:px-14 py-4">
              <h2 className="text-[14px] font-semibold md:text-[17px] md3:text-[20px] text-eweko_green_dark mb-4">
                Business Information
              </h2>
              <div className="grid sm3:grid-cols-2 gap-4 md:gap-8 md3:gap-4">
                {businessFields.map(field => (
                  <InputField
                    key={field.name}
                    label={field.label}
                    name={field.name}
                    value={
                      formData[field.name] ||
                      `No ${field.label.toLowerCase()} set`
                    }
                    isEditing={isEditing}
                    onChange={handleChange}
                  />
                ))}
              </div>
              <Separator className="mt-6" />
            </div>

            {/* Farm Info */}
            {/* <div className="px-4 md3:px-14 py-4">
              <h2 className="text-[14px] font-semibold md:text-[17px] md3:text-[20px] text-eweko_green_dark mb-4">
                Farm Details
              </h2>
              <div className="grid sm3:grid-cols-2 gap-4 md:gap-8 md3:gap-4">
                {farmFields.map(field => (
                  <InputField
                    key={field.name}
                    label={field.label}
                    name={field.name}
                    value={
                      formData[field.name] ||
                      `No ${field.label.toLowerCase()} set`
                    }
                    isEditing={isEditing}
                    onChange={handleChange}
                  />
                ))}
              </div>
            </div> */}
          </section>

          {/* Action Buttons */}
          {isEditing && (
            <footer className="flex justify-end mr-14 my-6 space-x-4">
              <button
                type="button"
                onClick={handleCancel}
                className="text-eweko_green_dark hover:underline"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSave}
                className="px-4 py-2 bg-eweko_green_dark text-white rounded-lg hover:bg-eweko_green_light"
              >
                Save Changes
              </button>
            </footer>
          )}
        </form>

        <div className="flex flex-col gap-2 px-4 md3:px-14">
          <h2 className="text-[14px] font-semibold md:text-[17px] md3:text-[20px] text-eweko_green_dark">
            Default Address
          </h2>

          {defaultAddress ? (
            <div className="grid grid-rows-1 sm3:grid-cols-2 gap-4 md:gap-8 md3:gap-4 py-2">
              <div className="flex flex-col gap-2">
                <h4 className="font-semibold text-eweko_green_dark">Street</h4>
                <p className="text-gray-500 text-[14px]">
                  {defaultAddress.houseNumber} {defaultAddress.streetName}
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <h4 className="font-semibold text-eweko_green_dark">
                  Community
                </h4>
                <p className="text-gray-500 text-[14px]">
                  {defaultAddress.community}
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <h4 className="font-semibold text-eweko_green_dark">LGA</h4>
                <p className="text-gray-500 text-[14px]">
                  {defaultAddress.lga}
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <h4 className="font-semibold text-eweko_green_dark">State</h4>
                <p className="text-gray-500 text-[14px]">
                  {defaultAddress.state}
                </p>
              </div>
            </div>
          ) : (
            <p className="text-[14px] text-gray-500">No default address</p>
          )}
        </div>
      </div>
    </div>
  );
}

interface InputFieldProps {
  label: string; // Ensures the label prop is a string
  name: string; // Input field name attribute
  value: string; // Current value of the field
  isEditing: boolean; // Flag to determine edit mode
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void; // Change handler
}
// Reusable InputField Component
function InputField({
  label,
  name,
  value,
  isEditing,
  onChange,
}: InputFieldProps) {
  return (
    <div>
      <label
        htmlFor={name}
        className="block text-[14px] md:text-[15px] md3:text-[16px] md3:[19px] text-eweko_green_dark"
      >
        {label}
      </label>
      {isEditing ? (
        <input
          id={name}
          type="text"
          name={name}
          value={value}
          onChange={onChange}
          className="w-full mt-1 p-2 border rounded"
        />
      ) : (
        <p className="mt-1 text-[12px] md:text-[13px] md3:text-[14px] text-gray-500">
          {value}
        </p>
      )}
    </div>
  );
}
