'use client';
import { RadioGroup } from '@radix-ui/react-radio-group';
// components/AccountSecurity.js
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useEffect, useState } from 'react';
import { Button } from '../../../../../components/ui/button';
import { Input } from '../../../../../components/ui/input';
import { Label } from '../../../../../components/ui/label';
import { RadioGroupItem } from '../../../../../components/ui/radio-group';
import {
  useSafeMutation,
  useSafeQuery,
} from '../../../../../axios/query-client';
import { AllRoutes } from '../../../../../routes';
import { toast } from 'react-toastify';
import { useGlobalState } from '../../../../../globalStore';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../components/ui/select';

interface Errors {
  password?: string;
  confirmPassword?: string;
}

export default function AccountSecurity() {
  const [isEditing, setIsEditing] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<Errors>({});
  const { loggedInUser } = useGlobalState();
  const [preferences, setPreferences] = useState<any>([]);

  const { mutate: updatePasword } = useSafeMutation<
    any,
    Error,
    {
      newPassword: string;
      confirmNewPassword: string;
    }
  >(`${AllRoutes.auth}/change-password`, 'post', {
    onSuccess: data => {
      toast.success('Password changed successfully');
      setPassword('');
      setConfirmPassword('');
      setIsEditing(false);
      setErrors({});
    },
    onError: (error: any) => {
      if (typeof error === 'object') {
        if (Array.isArray(error?.errorMessage)) {
          // Show each error as a toast
          error.errorMessage.forEach((msg: string) => {
            toast.error(msg);
          });
        } else if (typeof error?.errorMessage === 'string') {
          toast.error(error.errorMessage);
        } else if (error?.message) {
          toast.error(error.message);
        } else {
          toast.error('An unexpected error occurred.');
        }
      } else {
        toast.error('An unknown error occurred.');
      }
    },
  });

  const handleSave = () => {
    const validationErrors = validatePassword(password, confirmPassword);

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    setErrors({});
    // Proceed with password update
    updatePasword({
      newPassword: password,
      confirmNewPassword: confirmPassword,
    });
  };

  const [emails, setEmails] = useState<string[]>(['<EMAIL>']);
  const [selectedEmail, setSelectedEmail] = useState<string>(
    '<EMAIL>'
  );

  const { data: userPreferences, refetch } = useSafeQuery<any>(
    ['preferences'],
    `${AllRoutes.preferences}/${loggedInUser?.id}`,
    {
      enabled: true,
      refetchOnWindowFocus: true,
    }
  );

  const { mutateAsync: updatePreferences } = useSafeMutation<
    any,
    Error,
    Record<string, any>
  >(`${AllRoutes.preferences}/${loggedInUser?.id}`, 'patch', {
    onSuccess: () => {
      toast.success('Preferences updated successfully');
      refetch(); // Refresh preferences after update
    },
    onError: (error: any) => {
      console.error('Error updating preferences:', error);
      toast.error(
        error?.response?.data?.message || 'Failed to update preferences'
      );
    },
  });

  const handleToggle = async (
    label: string,
    currentValue: any,
    type: string,
    newValue?: any // Only needed for 'select'
  ) => {
    let updatedValue;

    if (type === 'boolean') {
      updatedValue = !currentValue;
    } else if (type === 'nested') {
      updatedValue = {
        enabled: !currentValue,
      };
    } else if (type === 'select') {
      if (!newValue) return; // Ensure newValue is provided
      updatedValue = newValue;
    } else {
      return; // Unsupported type
    }

    await updatePreferences({ [label]: updatedValue });
  };

  useEffect(() => {
    if (userPreferences) {
      setPreferences([
        {
          title: 'Enable 2FA',
          description: 'Enable two-factor authentication for added security',
          label: 'enable2fa',
          value: userPreferences.enable2fa,
          type: 'boolean', // Correct
        },
        {
          title: 'OTP/Notification Channel',
          description: 'Where should OTP and other notifications be sent?',
          label: 'otpDestination',
          value: userPreferences.otpDestination,
          type: 'select', // Changed from boolean to select
          options: ['EMAIL', 'SMS', 'BOTH'], // Provide options for dropdown/select
        },
      ]);
    }
  }, [userPreferences]);

  return (
    <div className="w-full mx-auto grid gap-8">
      {/* Account Password Section */}
      <div className=" bg-white px-4 lg:px-14 pt-4 rounded-lg border-b">
        <h2 className=" text-[14px]  sm3:text-[14px] md3:text-xl font-semibold mb-4">
          Account Password
        </h2>
        <div className="mb-4 bg-white pb-4 flex flex-col lg:flex-row justify-between ">
          <div className="space-y-4 w-72">
            {/* Password Input */}
            <div>
              <label className="block text-[13px] mb-2 md:text-sm text-gray-600">
                Password
              </label>
              <input
                type="password"
                value={!isEditing ? '**************' : password}
                disabled={!isEditing}
                onChange={e => setPassword(e.target.value)}
                className={`w-full px-4 py-2 border rounded ${
                  isEditing ? 'bg-white' : ' bg-transparent'
                }`}
              />

              {errors.password && isEditing && (
                <p className="text-red-500 text-sm mt-1">{errors.password}</p>
              )}
            </div>

            {/* Confirm Password Input */}
            {isEditing && (
              <div>
                <label className="block text-sm mb-2 text-gray-600">
                  Confirm Password
                </label>
                <input
                  type="password"
                  value={confirmPassword}
                  onChange={e => setConfirmPassword(e.target.value)}
                  className="w-full px-4 py-2 border rounded bg-white"
                />
                {errors.confirmPassword && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.confirmPassword}
                  </p>
                )}
              </div>
            )}

            {/* Change Password Link */}
            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="text-eweko_green_light hover:underline"
              >
                Change Password
              </button>
            ) : (
              <div className="flex gap-4 ">
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setPassword('');
                    setConfirmPassword('');
                    setErrors({});
                  }}
                  className="px-4 py-2 rounded hover:bg-gray-100"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  className="px-4 py-2 bg-eweko_green_dark text-white rounded-lg hover:bg-eweko_green_light"
                >
                  Save password
                </button>
              </div>
            )}
          </div>

          {/* Password Requirements */}

          <div className="w-[50%] bg-gray-100 rounded border p-6">
            <h3 className=" text-[15px] md3:text-[19px] font-medium">
              Password Requirements
            </h3>
            <ul className="list-disc ml-5 text-[13px] md3:text-sm text-gray-600 mt-2 space-y-1">
              <li>At least 4 characters</li>
              <li>At most 40 characters</li>
              <li>One uppercase letter</li>
              <li>One lowercase letter</li>
              <li>One number</li>
              <li>
                One special character
                <br />
                <span className="break-all">
                  (!@#$%^&amp;*()_-+=[]{};:&apos;&quot;,.&lt;&gt;?/\\|`~)
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Two-Factor Authentication */}
      <div className="px-4 lg:px-14 flex flex-col gap-4 rounded-lg ">
        <div className="">
          <div className="">
            {/* {preferences.map((item: any, i: number) => (
              <li
                key={i}
                className="flex justify-between md:gap-0 gap-10 items-center py-4"
              >
                <div>
                  <h3 className="md:text-[16px] text-[14px] md3:text-lg font-medium text-gray-900">
                    {item.title}
                  </h3>
                  <p className="md:text-[13px] text-[10px] md3:text-sm text-gray-500">
                    {item.description}
                  </p>
                </div>

                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={item.value}
                    className="sr-only peer"
                    onChange={() =>
                      handleToggle(item.label, item.value, item.type)
                    }
                  />
                  <div
                    className="md:w-11 md:h-6 w-8 h-4 bg-gray-300 rounded-full flex items-center px-1
                peer-checked:bg-eweko_green_light peer-checked:justify-end"
                  >
                    <div className="md:w-4 md:h-4 w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </label>
              </li>
            ))} */}

            {preferences.map((item: any, i: number) => (
              <li
                key={i}
                className="flex justify-between md:gap-0 gap-10 items-center py-4"
              >
                <div>
                  <h3 className="md:text-[16px] text-[14px] md3:text-lg font-medium text-gray-900">
                    {item.title}
                  </h3>
                  <p className="md:text-[13px] text-[10px] md3:text-sm text-gray-500">
                    {item.description}
                  </p>
                </div>

                {item.type === 'boolean' ? (
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={item.value}
                      className="sr-only peer"
                      onChange={() =>
                        handleToggle(item.label, item.value, item.type)
                      }
                    />
                    <div
                      className="md:w-11 md:h-6 w-8 h-4 bg-gray-300 rounded-full flex items-center px-1
          peer-checked:bg-eweko_green_light peer-checked:justify-end"
                    >
                      <div className="md:w-4 md:h-4 w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  </label>
                ) : item.type === 'select' ? (
                  <Select
                    defaultValue={item.value}
                    onValueChange={value =>
                      handleToggle(item.label, item.value, item.type, value)
                    }
                  >
                    <SelectTrigger className="w-[90px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {item.options?.map((opt: string, index: number) => (
                        <SelectItem key={index} value={opt}>
                          {opt}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : null}
              </li>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

type PasswordErrors = {
  password?: string;
  confirmPassword?: string;
};

const validatePassword = (
  password: string,
  confirmPassword: string
): PasswordErrors => {
  const errors: PasswordErrors = {};

  const specialCharRegex = /[!@#$%^&*()_\-+=\[\]{};:'",.<>?/\\|`~]/;
  const uppercaseRegex = /[A-Z]/;
  const lowercaseRegex = /[a-z]/;
  const numberRegex = /[0-9]/;

  if (!password) {
    errors.password = 'Password is required.';
  } else {
    if (password.length < 4) {
      errors.password = 'Password must be at least 4 characters.';
    } else if (password.length > 40) {
      errors.password = 'Password must be at most 40 characters.';
    } else if (!uppercaseRegex.test(password)) {
      errors.password = 'Must include at least one uppercase letter.';
    } else if (!lowercaseRegex.test(password)) {
      errors.password = 'Must include at least one lowercase letter.';
    } else if (!numberRegex.test(password)) {
      errors.password = 'Must include at least one number.';
    } else if (!specialCharRegex.test(password)) {
      errors.password =
        'Must include at least one special character (!@#$%^&*...).';
    }
  }

  if (!confirmPassword) {
    errors.confirmPassword = 'Please confirm your password.';
  } else if (confirmPassword !== password) {
    errors.confirmPassword = 'Passwords do not match.';
  }

  return errors;
};
