import address from '@/public/icons/address.png';
import billing from '@/public/icons/billing.png';
import notification from '@/public/icons/notification.png';
import profile from '@/public/icons/profile.png';
import security from '@/public/icons/security.png';
import Image from 'next/image';
import React, { Dispatch, SetStateAction } from 'react';
import { Separator } from '../../../../../components/ui/separator';

// Define the type for the activeSection
interface SidebarProps {
  activeSection: string;
  setActiveSection: Dispatch<SetStateAction<string>>;
}

const Sidebar = ({ activeSection, setActiveSection }: SidebarProps) => {
  // Define the sections in an array
  const sections = [
    { name: 'Profile', icon: profile, label: 'My Profile' },
    { name: 'Notifications', icon: notification, label: 'Notifications' },
    { name: 'Addresses', icon: address, label: 'Addresses' },
    { name: 'Security', icon: security, label: 'Security' },
  ];

  return (
    <nav className="w-fit h-fit rounded-lg p-2 border bg-white">
      <ul className="space-y-2">
        {sections.map((section, index) => (
          <React.Fragment key={section.name}>
            <li
              onClick={() => setActiveSection(section.name)}
              className={`cursor-pointer p-2 md:pl-6 md:pr-8 ${
                activeSection === section.name
                  ? 'font-bold text-eweko_green_light'
                  : ''
              }`}
            >
              <button
                aria-label={section.label}
                className="flex items-center text-[12px] md:[15px] md3:text-[16px]"
              >
                <Image
                  src={section.icon}
                  alt={section.label}
                  width={15}
                  height={15}
                />
                <span className="ml-2 hidden md:flex duration-300 transition-all">
                  {section.label}
                </span>
              </button>
            </li>

            {/* Render Separator only if it's not the last item */}
            {index < sections.length - 1 && <Separator />}
          </React.Fragment>
        ))}
      </ul>
    </nav>
  );
};

export default Sidebar;
