'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Dialog } from '@/components/ui/dialog';
import { zodResolver } from '@hookform/resolvers/zod';
import { format, isEqual } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { RiSearchLine } from 'react-icons/ri';
import { z } from 'zod';
import { Calendar } from '../../../../../components/ui/calendar';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from '../../../../../components/ui/form';
import { Input } from '../../../../../components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../../../../components/ui/popover';
import { cn } from '../../../../../lib/utils';
import { isAfter, isBefore } from 'date-fns';

import { StaticImport } from 'next/dist/shared/lib/get-img-props';
import Image from 'next/image';
import {
  ITransaction,
  TransactionTableRow,
} from '../../../../(buyers)/buyers/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../components/ui/select';
import { DataTable } from './data-table';
import { columns } from './columns';
import { TableSkeleton } from './TableSkeleton';
import { useRouter } from 'next/navigation';

const DateFilterSchema = z.object({
  startDate: z.date(),
  endDate: z.date(),
});

type Transaction = {
  status: string;
  date: string;
  type: string;
  time: string;
  order: number;
  produce: string;
  value: number;
  invoice: string;
  image: string | StaticImport;
  duration: string;
};

export const TransactionsTable = ({
  transactions,
}: {
  transactions: ITransaction[];
}) => {
  const [fetchedTransactions, setFetchedTransactions] = useState<
    ITransaction[] | null
  >(null);
  const [filteredTransactions, setFilteredTransactions] = useState<
    TransactionTableRow[] | null
  >(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [filter, setFilter] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] =
    useState<Transaction | null>(null);

  const [selectedStatus, setSelectedStatus] = useState('');

  const router = useRouter();

  useEffect(() => {
    if (transactions) {
      setFetchedTransactions(transactions);

      const tableData = transactions.map((transaction, i) => {
        return {
          sn: i + 1,
          id: transaction.id,
          status: transaction.status,
          date: new Date(transaction.createdAt).toLocaleDateString('en-GB'),
          method: transaction.paymentMethod,
          reference: transaction.reference,
          orderValue: transaction.totalAmount,
        };
      });

      setFilteredTransactions(tableData);
    }
  }, [transactions]);

  const handleRowClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
    setSelectedTransaction(null);
  };
  const form = useForm<z.infer<typeof DateFilterSchema>>({
    resolver: zodResolver(DateFilterSchema),
  });

  const sdw = form.watch('startDate');
  const edw = form.watch('endDate');

  useEffect(() => {
    if (sdw || edw || filter) {
      setStartDate(sdw);
      setEndDate(edw);
    }
  }, [sdw, edw]);

  useEffect(() => {
    if (fetchedTransactions) {
      if (searchTerm || (startDate && endDate) || selectedStatus) {
        const filtered = fetchedTransactions.filter(transaction => {
          const transactionDate = new Date(transaction.createdAt);

          const matchesSearchTerm =
            searchTerm === '' ||
            transaction.reference
              .toLowerCase()
              .includes(searchTerm.toLowerCase()) ||
            transaction.status.toLowerCase().includes(searchTerm.toLowerCase());

          const matchesStatus =
            selectedStatus === '' ||
            transaction.status
              .toLowerCase()
              .includes(selectedStatus.toLowerCase());

          const matchesDateFilter =
            (!startDate ||
              isAfter(transactionDate, startDate) ||
              isEqual(transactionDate, startDate)) &&
            (!endDate ||
              isBefore(transactionDate, endDate) ||
              isEqual(transactionDate, endDate));

          return matchesSearchTerm && matchesStatus && matchesDateFilter;
        });

        if (filtered) {
          const tableData = filtered.map((transaction, i) => {
            return {
              sn: i + 1,
              id: transaction.id,
              status: transaction.status,
              date: new Date(transaction.createdAt).toLocaleDateString('en-GB'),
              method: transaction.paymentMethod,
              reference: transaction.reference,
              orderValue: transaction.totalAmount,
            };
          });

          setFilteredTransactions(tableData);
        }
      } else {
        setFetchedTransactions(transactions);
      }
    }
  }, [searchTerm, startDate, endDate, selectedStatus, fetchedTransactions]);

  // Create a function to transform transactions to table format
  const transformToTableData = (data: ITransaction[]) => {
    return data.map((transaction, i) => {
      return {
        sn: i + 1,
        id: transaction.id,
        status: transaction.status,
        date: new Date(transaction.createdAt).toLocaleDateString('en-GB'),
        method: transaction.paymentMethod,
        reference: transaction.reference,
        orderValue: transaction.totalAmount,
      };
    });
  };

  // Function to clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setStartDate(null);
    setEndDate(null);
    setSelectedStatus('');
    form.reset({
      startDate: undefined,
      endDate: undefined,
    });

    // Reset to original data
    if (fetchedTransactions) {
      const tableData = transformToTableData(fetchedTransactions);
      setFilteredTransactions(tableData);
    }

    router.refresh();
  };

  return (
    <div className="w-full flex flex-col gap-4 py-0 ">
      <div className="w-full flex flex-col sm:flex-row items-center justify-center sm:justify-between flex-wrap gap-3 md2:gap-0">
        <div className="flex items-center justify-between text-[14px] text-[#636363]/70 bg-white rounded-[10px] pr-4 pt-[2px] pb-[2px] pl-[3px] border border-[#d9d9d9] w-full sm:w-[70%] md2:w-[30%] md3:w-[30%] lg2:w-[30%] xl:w-[28%] order-2 md2:order-1">
          <Input
            type="text"
            placeholder="Search reference"
            className="w-full outline-none border-none bg-white focus-visible:ring-transparent ring-0 transition-all duration-300 focus-visible:ring-0 focus-within:ring-0 shadow-none h-[45px] px-3 my-0"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
          <RiSearchLine size={21} />
        </div>
        <div className="w-full md2:w-[45%] md3:w-[48%] lg:w-[50%] xl:w-[58%] order-1 md2:order-2">
          <Form {...form}>
            <form className="flex gap-3 w-full md2:w-[90%] lg:w-[60%] lg2:w-[65%] xl:w-[48%]">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col w-[60%]">
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            className={cn(
                              'bg-white w-full text-left h-[45px] flex justify-between font-normal text-[#636363]/70 relative overflow-hidden hover:bg-white focus:bg-transparent focus-within:bg-transparent '
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>From</span>
                            )}
                            <span className="bg-[#f2f2f2] h-full w-[30px] absolute right-0 flex items-center justify-center">
                              <CalendarIcon className="h-[18px] w-[18px] text-[#999999] opacity-50 " />
                            </span>
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          className="border border-eweko_green rounded-[7px]"
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={date =>
                            date > new Date() || date < new Date('1900-01-01')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col w-[60%]">
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            className={cn(
                              'bg-white w-full text-left h-[45px] flex justify-between font-normal text-[#636363]/70 relative overflow-hidden hover:bg-white focus:bg-transparent focus-within:bg-transparent '
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>To</span>
                            )}
                            <span className="bg-[#f2f2f2] h-full w-[30px] absolute right-0 flex items-center justify-center">
                              <CalendarIcon className="h-[18px] w-[18px] text-[#999999] opacity-50 " />
                            </span>
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={date =>
                            date > new Date() || date < new Date('1900-01-01')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>
        <div className="w-full sm:w-[27%] md2:w-[18%] md3:w-[15%] lg:w-[13%] xl:w-[10%] order-2 md2:order-3">
          <Select
            value={selectedStatus}
            onValueChange={value => setSelectedStatus(value)}
          >
            <SelectTrigger className="flex items-center justify-between text-[14px] text-[#636363]/70 bg-white rounded-[10px] focus:ring-0 border border-[#d9d9d9] w-full">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
              <SelectItem value="Refunded">Refunded</SelectItem>
              <SelectItem value="Failed">Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      {(searchTerm || startDate || endDate || selectedStatus) && (
        <span
          className="text-red-600 ml-1 w-fit cursor-pointer"
          onClick={clearFilters}
        >
          Clear Filter
        </span>
      )}
      <div className="w-full mt-6">
        <section className="w-full">
          {filteredTransactions ? (
            <DataTable columns={columns} data={filteredTransactions} />
          ) : (
            <TableSkeleton columnCount={7} rowCount={5} />
          )}
        </section>

        {isDialogOpen && selectedTransaction && (
          <Dialog open={isDialogOpen}>
            <div className="fixed inset-0 px-10  bg-gray-300 bg-opacity-50 flex justify-center items-center z-50">
              <div className="relative w p-4 px-8 bg-gray-50 rounded-lg shadow-xl  mx-auto w-[550px] ">
                <h1 className="w-full text-center text-eweko_green_dark text-[16px]">
                  Transaction Details
                </h1>
                <button
                  className="absolute top-3 right-2 text-gray-600 hover:text-black"
                  onClick={closeDialog}
                >
                  ✕
                </button>
                <div className="mt-4">
                  <p className="text-eweko_green_dark text-[15px]">
                    Order ID: {selectedTransaction.invoice}
                  </p>
                  <p className="text-gray-600 text-[12px]">
                    Placed on: {selectedTransaction.date}
                  </p>
                  <p className="text-gray-600 text-[12px]">
                    Total: {selectedTransaction.value} NGN
                  </p>

                  <div className="mt-4">
                    <h3 className="text-eweko_green_dark text-[15px]">
                      Items in Your Order
                    </h3>
                    <div className="w-full rounded-lg  px-6 py-4 border bg-white flex gap-4 ">
                      <Image
                        src={selectedTransaction.image}
                        alt={selectedTransaction.produce}
                        width={100}
                        height={100}
                        className="w-[60px] h-[50px]"
                      />
                      <div className="">
                        <p className="text-gray-600 text-[11px]">
                          {selectedTransaction.produce}
                        </p>
                        <p className="text-gray-600 text-[11px]">
                          KG: {selectedTransaction.order}
                        </p>
                        <p className="text-gray-600 text-[11px]">
                          Price/KG: N/A
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2 w-full ">
                    <div className=" w-full">
                      <h3 className="mt-4 text-[15px]">Payment Information</h3>
                      <div className="bg-white border w-full p-4 rounded-lg">
                        <p className=" text-[14px] text-eweko_green_dark">
                          Payment Method
                        </p>
                        <p className="mt-1 text-[11px]">Paid with centiv</p>

                        <p className="mt-4 text-[16px]">Payment Details</p>
                        <p className="mt-1 text-[11px]">
                          item&apos;s total: N{selectedTransaction.value}
                        </p>

                        <p className="mt-1 text-[11px]">Delivery fees: N9000</p>
                        <p className="mt-1 text-[11px]">Total: N409,000 </p>
                      </div>
                    </div>

                    <div className="w-full">
                      <p className="mt-4 text-[15px]">Delivery information</p>
                      <div className="bg-white border w-full p-4 rounded-lg">
                        <h1 className=" t-1 text-[11px]">
                          <p className=" text-[14px] text-eweko_green_dark">
                            Delivery Details:
                          </p>
                          <p className=" t-1 text-[11px]">Door delivery</p>
                        </h1>
                        <h1 className="mt-4 text-[11px] ">
                          <p className=" text-[14px] text-eweko_green_dark">
                            {' '}
                            Shipping Address:
                          </p>
                          <p className=" t-1 text-[11px]">
                            2972 Westheimer Rd Santa Ana, Illinois
                          </p>
                        </h1>
                        <h1 className="mt-4 text-[11px]">
                          <p className=" text-[14px] text-eweko_green_dark">
                            Delivery between:
                          </p>
                          <p className=" t-1 text-[11px]">
                            14th November - 16th November
                          </p>
                        </h1>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Dialog>
        )}
      </div>
    </div>
  );
};
