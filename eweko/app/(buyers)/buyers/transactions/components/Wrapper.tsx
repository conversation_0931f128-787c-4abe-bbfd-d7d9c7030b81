'use client';

import { TransactionsSummary } from './TransactionsSummary';
import { TransactionsTable } from './TransactionsTable';
import {
  useSafeMutation,
  useSafeQuery,
} from '../../../../../axios/query-client';
import { AllRoutes } from '../../../../../routes';
import { useEffect } from 'react';
import { useGlobalState } from '../../../../../globalStore';
import axios from 'axios';
import { Order, PaymentStatus, Transaction } from '../../types';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';

type PaymentSource = {
  associateId: string;
  completedAt: string;
  paymentDetails: {
    accountNumber: string;
    bankCode: string;
    accountId: string;
    accountName: string;
    createdAt: string;
  };
  customerDetails: null | {
    id: string;
    email: string;
    name: string;
  };
  isLiveResource: boolean;
  createdAt: string;
  amount: number;
  note: string;
  status: string;
};

type TransformedPayment = {
  transactionId: string;
  amount: number;
  currency: string;
  status: string;
  paymentMethod: string;
  customer: null | {
    id: string;
    email: string;
    name: string;
  };
  metadata: {
    orderId: string;
    transactionId: string;
    buyerId: string;
  };
};

export const Wrapper = () => {
  const { cplId, cpoId, cptId, setCplId, setCpoId, setCptId, loggedInUser } =
    useGlobalState();

  function transformToPaymentPayload(
    source: PaymentSource
  ): TransformedPayment {
    return {
      transactionId: source.associateId,
      amount: source.amount,
      currency: 'NGN',
      status: source.status === 'paid' ? 'successful' : 'failed',
      paymentMethod: 'bank_transfer',
      customer: null,
      metadata: {
        orderId: cpoId,
        transactionId: cptId,
        buyerId: loggedInUser ? loggedInUser?.id : '',
      },
    };
  }

  const { data, isSuccess } = useSafeQuery<any>(
    ['transactions'],
    `${AllRoutes.transactions}/buyer`,
    {
      enabled: true,
    }
  );

  const { mutate: sendWebhook } = useSafeMutation<
    any,
    Error,
    TransformedPayment
  >(`${AllRoutes.payments}/webhooks/centiiv`, 'post', {
    onSuccess: webhookSent => {
      if (webhookSent.message === 'Payment processed successfully') {
        setTimeout(() => {
          setCplId('');
          setCpoId('');
          setCptId('');
          window.location.reload();
        }, 3000);
      }
    },
    onError: (error: any) => {
      if (typeof error === 'object') {
        if (Array.isArray(error?.errorMessage)) {
          error.errorMessage.forEach((msg: string) => {
            toast.error(msg);
          });
        } else if (typeof error?.errorMessage === 'string') {
          toast.error(error.errorMessage);
        } else if (error?.message) {
          toast.error(error.message);
        } else {
          toast.error('An unexpected error occurred.');
        }
      } else {
        toast.error('An unknown error occurred.');
      }
    },
  });

  useEffect(() => {
    const verifyCentiivPayment = async () => {
      const headers = {
        accept: 'application/json',
        'content-type': 'application/json',
        authorization: `Bearer ${process.env.NEXT_PUBLIC_CENTIIV_TEST_APIKEY}`,
      };

      const response = await axios.get(
        `https://api.centiiv.com/api/v1/direct-pay/${cplId}`,

        {
          headers: headers,
        }
      );

      if (response.data.data.status && response.data.data.status === 'paid') {
        const paymentPayload = transformToPaymentPayload(response.data.data);
        sendWebhook(paymentPayload);
      }
    };

    if (cplId && cpoId && cptId) {
      verifyCentiivPayment();
    }
  }, [data, cplId, cpoId, cptId]);

  return (
    <div className="w-full flex flex-col gap-12">
      {isSuccess && (
        <>
          <TransactionsSummary transactions={data.data} />
          <TransactionsTable transactions={data.data} />
        </>
      )}
    </div>
  );
};
