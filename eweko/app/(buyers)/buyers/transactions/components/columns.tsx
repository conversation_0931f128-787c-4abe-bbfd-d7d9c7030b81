'use client';

import { ColumnDef } from '@tanstack/react-table';
import { IoTrash } from 'react-icons/io5';
import { TbEdit } from 'react-icons/tb';
import {
  ProduceTableRow,
  TransactionTableRow,
} from '../../../../(buyers)/buyers/types';
import Image from 'next/image';
import Link from 'next/link';
import { BsFillEyeFill } from 'react-icons/bs';

export const columns: ColumnDef<TransactionTableRow>[] = [
  {
    accessorKey: 'sn',
    header: 'S/N',
    cell: ({ row }) => {
      return (
        <div className="min-w-[60px]">
          <p>{row.getValue('sn')}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      return (
        <div
          className={`w-[120px] flex items-center justify-center py-1 rounded-[4px] ${row.getValue('status') === 'Pending' && 'bg-[#E5940059] text-[#E59400]'} ${row.getValue('status') === 'Completed' && 'bg-[#B4ECC9] text-[#0B5730]'} ${row.getValue('status') === 'Failed' && 'bg-[#FF00001A] text-[#BD0000]'} ${row.getValue('status') === 'Refunded' && 'bg-gray-200 text-gray-600'}`}
        >
          <p>{row.getValue('status')}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => {
      return (
        <div className="">
          <p>{row.getValue('date')}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'method',
    header: 'Method',
    cell: ({ row }) => {
      return (
        <div className="">
          <p>{row.getValue('method')}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'reference',
    header: 'Reference',
    cell: ({ row }) => {
      return (
        <div className="min-w-[180px]">
          <p>{row.getValue('reference')}Kg</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'orderValue',
    header: 'Order Value (NGN)',
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue('orderValue'));
      const formatted = new Intl.NumberFormat('en-NG', {
        style: 'currency',
        currency: 'NGN',
      }).format(amount);

      return <div className="">{formatted}</div>;
    },
  },
];
