'use client';

import { uploadImageToCloudinary } from '@/app/utils/cloudinary';
import { useSafeMutation, useSafeQuery } from '@/axios/query-client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

import { useGlobalState } from '@/globalStore';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { Button } from '../../../../../components/ui/button';
import { AllRoutes } from '../../../../../routes';

interface Category {
  _id: string;
  name: string;
}

const produceFormSchema = z
  .object({
    name: z.string().min(1, { message: 'Produce name is required' }),
    description: z.string().min(1, { message: 'Description is required' }),
    harvestDate: z.date({
      required_error: 'Harvest date is required',
    }),
    category: z.string().min(1, { message: 'Category is required' }),
    price: z.number().positive({ message: 'Price must be a positive number' }),
    negotiablePrice: z.number().optional(),
    stock: z.number().positive({ message: 'Stock must be a positive number' }),
    minOrderQty: z.number().positive({
      message: 'Minimum order quantity must be a positive number',
    }),
  })
  .refine(
    data => {
      // Only validate if both price and negotiablePrice exist
      if (data.price !== undefined && data.negotiablePrice !== undefined) {
        return data.negotiablePrice < data.price;
      }
      // If one is missing, consider validation passed
      return true;
    },
    {
      message: 'Negotiable price must be less than the regular price',
      path: ['negotiablePrice'], // This targets the error to the negotiablePrice field
    }
  );

export default function AddProduce() {
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imageError, setImageError] = useState('');
  const [uploading, setUploading] = useState(false);

  const { loggedInUser, setProduceUpdated } = useGlobalState();
  const router = useRouter();

  // Initialize the form with React Hook Form and Zod resolver
  const form = useForm<z.infer<typeof produceFormSchema>>({
    resolver: zodResolver(produceFormSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      negotiablePrice: 0,
      stock: 0,
      minOrderQty: 0,
      category: '',
    },
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    if (files.length > 3) {
      setImageError('You can only select up to 3 images.');
      return;
    }

    const selectedFiles = Array.from(files);
    setSelectedImages(selectedFiles);
    setImageError('');
  };

  const handleReplaceImage = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.target.files && e.target.files[0]) {
      const updatedImages = [...selectedImages];
      updatedImages[index] = e.target.files[0]; // Replace the image at the given index
      setSelectedImages(updatedImages);
      setImageError('');
    }
  };

  // Mutation for form submission
  const { mutateAsync, isPending } = useSafeMutation<
    any,
    Error,
    {
      farmer: string | undefined;
      name: string;
      description: string;
      harvestDate: Date | undefined;
      category: string | undefined;
      price: number | undefined;
      negotiablePrice: number | undefined;
      stock: number | undefined;
      minOrderQty: number | undefined;
      images: string[];
    }
  >(`${process.env.NEXT_PUBLIC_APIURL}/produce`, 'post', {
    onSuccess: data => {
      toast.success('Product added successfully!', {
        position: 'top-center',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: false,
        pauseOnHover: true,
        draggable: true,
        theme: 'light',
      });
      setProduceUpdated(true);
      setTimeout(() => {
        router.push('/farmers/produce');
      }, 3000);
    },
    onError: (error: any) => {
      if (typeof error === 'object') {
        if (Array.isArray(error?.errorMessage)) {
          // Set form errors from array
          // setFormErrors(error.errorMessage);

          // Show each error as a toast
          error.errorMessage.forEach((msg: string) => {
            toast.error(msg);
          });
        } else if (typeof error?.errorMessage === 'string') {
          // setFormErrors([error.errorMessage]);
          toast.error(error.errorMessage);
        } else if (error?.message) {
          // setFormErrors([error.message]);
          toast.error(error.message);
        } else {
          // setFormErrors(['An unexpected error occurred.']);
          toast.error('An unexpected error occurred.');
        }
      } else {
        // setFormErrors(['An unknown error occurred.']);
        toast.error('An unknown error occurred.');
      }
    },
  });

  // Form submission handler
  const onSubmit = async (formData: z.infer<typeof produceFormSchema>) => {
    // Validate images before proceeding
    if (selectedImages.length === 0) {
      setImageError('At least one image is required.');
      return;
    }

    setImageError('');

    try {
      setUploading(true);

      // Upload all images in parallel
      const uploadedURLs = await Promise.all(
        selectedImages.map(file => uploadImageToCloudinary(file))
      );

      // Verify at least one image URL is available
      if (!uploadedURLs || uploadedURLs.length === 0) {
        setImageError('Failed to upload images. Please try again.');
        return;
      }

      const submitData = {
        farmer: loggedInUser?.id,
        name: formData.name,
        description: formData.description,
        harvestDate: formData.harvestDate,
        category: formData.category,
        price: formData.price,
        negotiablePrice: formData.negotiablePrice || undefined,
        stock: formData.stock,
        minOrderQty: formData.minOrderQty,
        images: uploadedURLs,
      };

      await mutateAsync(submitData);
    } catch (error: any) {
    } finally {
      setUploading(false);
    }
  };

  // Fetch categories using React Query
  const { data: categories = [], isError } = useSafeQuery<[]>(
    ['categories'],
    `${AllRoutes.category}`,
    {
      enabled: true,
    }
  );

  // Handle loading and error states for categories
  if (isError) {
    console.error('Error fetching categories:', isError);
    return <p>Error loading categories. Please try again.</p>;
  }

  return (
    <div className="md:px-10 pb-5 text-eweko_green_dark">
      <Button
        onClick={() => router.back()}
        className="cursor-pointer flex items-center text-eweko_green_light hover:text-white hover:bg-eweko_green_light bg-transparent transition-all duration-300 p-0 hover:px-4"
        variant="ghost"
      >
        <span className="mr-1 text-lg">&lt; </span> Back
      </Button>

      <h1 className="text-[min(10vw,19px)] font-bold mb-8">Add Produce</h1>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Image Upload Section */}
            <div className="bg-white border w-full lg:w-[50%] xl:w-[50%] h-full rounded-lg">
              <div>
                <p className="w-full text-[min(10vw,17px)] text-left p-5 border-b">
                  Produce Image
                </p>
                <div className="flex flex-col h-[70%] p-4 items-center">
                  <label
                    htmlFor="image-upload"
                    className="border-2 border-dashed border-eweko_green_light bg-gray-100 rounded-lg w-full h-[250px] flex flex-col justify-center items-center cursor-pointer hover:bg-green-50 transition"
                  >
                    {selectedImages.length > 0 && (
                      <div className="relative w-full h-full">
                        <img
                          src={URL.createObjectURL(selectedImages[0])}
                          alt="Uploaded preview"
                          className="w-full h-full object-cover rounded-lg"
                        />
                        {/* Buttons Overlay */}
                        <div className="absolute inset-0 flex flex-col items-center justify-center gap-4 bg-black/20 rounded-lg">
                          <button
                            type="button"
                            onClick={() =>
                              document.getElementById('replaceInput')?.click()
                            }
                            className="px-6 py-2 text-sm text-eweko_green_light bg-white rounded-lg hover:bg-gray-100"
                          >
                            Replace
                          </button>
                          <button
                            type="button"
                            onClick={() => setSelectedImages([])}
                            className="px-6 py-2 text-sm text-red-600 bg-white rounded-lg hover:bg-gray-100"
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    )}
                    {selectedImages.length === 0 && (
                      <div
                        className="flex flex-row gap-1 items-center cursor-pointer"
                        onClick={() =>
                          document.getElementById('imageInput')?.click()
                        }
                      >
                        <span className="text-sm text-eweko_green_light">
                          Click to upload
                        </span>
                        <span className="text-xs text-gray-400">
                          or drag and drop
                        </span>
                      </div>
                    )}
                  </label>
                  {/* Display image error message */}
                  {imageError && (
                    <p className="text-red-500 text-sm mt-2">{imageError}</p>
                  )}
                  {/* Hidden File Inputs */}
                  <input
                    id="imageInput"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    multiple
                    onChange={handleFileChange}
                  />
                  <input
                    id="replaceInput"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={e => handleReplaceImage(e, 0)}
                  />
                </div>
                {selectedImages.length > 1 && (
                  <div className="flex gap-4 ml-5 mt-4 mb-5">
                    {selectedImages.slice(1, 3).map((image, index) => (
                      <div key={index} className="relative w-[80px] h-[80px]">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Remaining preview ${index + 1}`}
                          className="w-full h-full object-cover rounded-lg"
                        />
                        <div className="absolute inset-0 flex flex-col items-center justify-center gap-2 bg-black/20 rounded-lg">
                          <button
                            type="button"
                            onClick={() =>
                              document
                                .getElementById(
                                  `replaceOtherInput-${index + 1}`
                                )
                                ?.click()
                            }
                            className="px-4 py-1 text-[9px] text-eweko_green_light bg-white rounded-lg hover:bg-gray-100"
                          >
                            Replace
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setSelectedImages(prev =>
                                prev.filter((_, i) => i !== index + 1)
                              );
                            }}
                            className="px-4 py-1 text-[9px] text-red-600 bg-white rounded-lg hover:bg-gray-100"
                          >
                            Remove
                          </button>
                        </div>
                        <input
                          id={`replaceOtherInput-${index + 1}`}
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={e => handleReplaceImage(e, index + 1)}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Form Fields Section */}
            <div className="bg-transparent flex flex-col gap-6 w-full">
              {/* General Information */}
              <div className="bg-white border w-full rounded-lg">
                <p className="w-full text-[min(10vw,17px)] text-left p-5 px-8 border-b">
                  General Information
                </p>

                <div className="px-8 py-5 w-full">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[min(10vw,15px)] text-gray-600">
                          Produce Name
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className="w-full xl:w-[80%] px-4 py-2 border rounded bg-white mt-1"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem className="mt-10">
                        <FormLabel className="text-[min(10vw,15px)] text-gray-600">
                          Produce Description
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            className="w-full xl:w-[80%] h-[250px] px-4 py-2 border rounded bg-white mt-1"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Category */}
              <div className="bg-white border w-full h-auto rounded-lg">
                <p className="w-full text-[min(10vw,17px)] text-left p-5 border-b">
                  Category
                </p>
                <div className="px-8 py-5 gap-4 flex sm3:flex-row flex-col w-full xl:w-[80%]">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select Category" />
                            </SelectTrigger>
                            <SelectContent>
                              {categories.map((category: Category) => (
                                <SelectItem
                                  key={category._id}
                                  value={category._id}
                                >
                                  {category.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Pricing */}
              <div className="bg-white border w-full h-auto rounded-lg">
                <p className="w-full text-[min(10vw,17px)] text-left p-5 border-b">
                  Pricing
                </p>

                <div className="flex sm3:flex-row flex-col w-full xl:w-[80%] gap-8 px-8 py-5">
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel className="text-[min(10vw,15px)] text-gray-600">
                          Price/Kg (NGN)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={e =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                            className="w-full px-4 mt-1 py-2 border rounded bg-white"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="negotiablePrice"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel className="text-[min(10vw,15px)] text-gray-600">
                          Negotiable Price (NGN)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={e =>
                              field.onChange(
                                parseFloat(e.target.value) || undefined
                              )
                            }
                            className="w-full mt-1 px-4 py-2 border rounded bg-white"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Other Details */}
              <div className="bg-white border w-full h-auto rounded-lg">
                <p className="w-full text-[min(10vw,17px)] text-left p-5 border-b">
                  Other Details
                </p>
                <div className="grid gri-cols-1 sm3:grid-cols-2 gap-8 px-8 py-5 w-full xl:w-[80%]">
                  <FormField
                    control={form.control}
                    name="stock"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel className="text-[min(10vw,15px)] text-gray-600">
                          Stock (Kg)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={e =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                            className="w-full px-4 mt-1 py-2 border rounded bg-white"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="minOrderQty"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel className="text-[min(10vw,15px)] text-gray-600">
                          Minimum Order Quantity (Kg)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={e =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                            className="w-full px-4 mt-1 py-2 border rounded bg-white"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="harvestDate"
                    render={({ field }) => (
                      <FormItem className="">
                        <FormLabel className="text-[min(10vw,15px)] text-gray-600">
                          Harvest Date
                        </FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className="w-full mt-1 justify-start text-left"
                              >
                                {field.value
                                  ? format(field.value, 'PPP')
                                  : 'Pick a date'}
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Form Action Buttons */}
              <div className="mt-6 text-right">
                <Button
                  type="button"
                  onClick={() => router.back()}
                  className="md:text-md mb-2 items-center mr-4 text-eweko_green_light bg-transparent shadow-none hover:bg-transparent text-sm"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isPending || uploading}
                  className="bg-eweko_green_dark text-white text-[17px] px-6 rounded-lg"
                >
                  {isPending || uploading ? 'Adding...' : 'Add Produce'}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
