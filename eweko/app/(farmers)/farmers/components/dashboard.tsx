'use client';

import { useRouter } from 'next/navigation';
import { useGlobalState } from '../../../../globalStore';
import { capitalizeFirstLetter } from '../../../../lib/utils';
import { format } from 'date-fns';
import { useSafeQuery } from '../../../../axios/query-client';
import { AllRoutes } from '../../../../routes';
import { useEffect, useState } from 'react';
import { FarmerStatItem } from '../../../(buyers)/buyers/types';
import { Skeleton } from '../../../../components/ui/skeleton';
import PriceTicker from './priceTicker';

export default function Dashboard() {
  const router = useRouter();
  const currentDate = format(new Date(), 'EEEE, do MMMM, yyyy');
  const { loggedInUser } = useGlobalState();
  const [farmerStats, setFarmerStats] = useState<FarmerStatItem[] | null>(null);

  const {
    data = [],
    isSuccess,
    refetch,
    isLoading,
  } = useSafeQuery<FarmerStatItem[]>(
    ['summary', loggedInUser?.id], // use id in key for cache uniqueness
    `${AllRoutes.users}/farmer/dashboard`,
    {
      enabled: !!loggedInUser?.id && !farmerStats, // only run if ID is available
    }
  );

  useEffect(() => {
    if (isSuccess) {
      setFarmerStats(data);
    }
  }, [isSuccess, data]);

  useEffect(() => {
    if (typeof window !== undefined) {
      if (loggedInUser && !farmerStats) {
        window.location.reload();
      }
    }
  }, []);

  return (
    <div className="w-full flex flex-col gap-8">
              {/* <PriceTicker /> */}

      {!loggedInUser ? (
        <div className="flex items-center gap-2 w-full">
          <Skeleton className="h-6 w-[10%] bg-white rounded-lg border" />
          <Skeleton className="h-6 w-[20%] bg-white rounded-lg border" />
        </div>
      ) : (
        <h1 className="text-[min(10vw,18px)] flex flex-col sm:flex-row sm:items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="font-bold">Welcome</span>
            <span className="font-bold">
              {capitalizeFirstLetter(loggedInUser.firstName)}
            </span>
          </div>
          <span className="text-[min(10vw,16px)] leading-none mt-[2px]">
            - {currentDate}
          </span>
        </h1>
      )}

      {isLoading ? (
        <section className="grid grid-cols-1 sm2:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-8 ">
          <Skeleton className="h-[125px] w-[100%] rounded-xl bg-white border flex flex-col gap-3 justify-center p-4">
            <Skeleton className="h-6 w-[40%] bg-gray-100 rounded-lg" />
            <Skeleton className="h-9 w-[80%] bg-gray-100 rounded-lg" />
          </Skeleton>

          <Skeleton className="h-[125px] w-[100%] rounded-xl bg-white border flex flex-col gap-3 justify-center p-4">
            <Skeleton className="h-6 w-[40%] bg-gray-100 rounded-lg" />
            <Skeleton className="h-9 w-[80%] bg-gray-100 rounded-lg" />
          </Skeleton>

          <Skeleton className="h-[125px] w-[100%] rounded-xl bg-white border flex flex-col gap-3 justify-center p-4">
            <Skeleton className="h-6 w-[40%] bg-gray-100 rounded-lg" />
            <Skeleton className="h-9 w-[80%] bg-gray-100 rounded-lg" />
          </Skeleton>

          <Skeleton className="h-[125px] w-[100%] rounded-xl bg-white border flex flex-col gap-3 justify-center p-4">
            <Skeleton className="h-6 w-[40%] bg-gray-100 rounded-lg" />
            <Skeleton className="h-9 w-[80%] bg-gray-100 rounded-lg" />
          </Skeleton>
        </section>
      ) : (
        <section className="grid grid-cols-1 sm2:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-8 ">
          {data &&
            data.map((stat, i) => (
              <div
                key={i}
                className="w-[100%] rounded-xl bg-white border flex flex-col gap-2 justify-center p-6"
              >
                <p className="text-[#292929] text-[min(10vw,15px)]">
                  {stat.label}
                </p>
                <h1 className="text-[#292929] text-[min(10vw,24px)] font-semibold">
                  {stat.value}
                </h1>
              </div>
            ))}
        </section>
      )}
    </div>
  );
}
