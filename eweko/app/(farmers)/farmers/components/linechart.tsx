'use client';

import {
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js';
import { Line } from 'react-chartjs-2';

ChartJS.register(
  LineElement,
  PointElement,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend
);

export default function MonthlySalesChart({
  farmersProduce,
}: {
  farmersProduce: any[];
}) {
  // Group data by month and calculate total sales for each month
  const monthlySales = Array(12).fill(0); // Initialize an array for 12 months

  // Assuming 'soldDate' is a date in ISO format (e.g., "2024-01-15")
  farmersProduce.forEach(item => {
    const month = new Date(item.soldDate).getMonth(); // Get month index (0 = January)
    monthlySales[month] += item.qtySold; // Add to the corresponding month
  });

  // Prepare data for the line chart
  const chartData = {
    labels: [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ], // Months of the year
    datasets: [
      {
        label: 'Sales (Kg)',
        data: monthlySales, // Monthly sales data
        fill: true, // Fill the area under the line (hill-like effect)
        borderColor: '#FF5733', // Line color
        backgroundColor: 'rgba(255, 87, 51, 0.2)', // Line fill color
        tension: 0.4, // Makes the line smooth with curves
        borderWidth: 2, // Line thickness
      },
    ],
  };

  // Customize chart options
  const chartOptions = {
    responsive: true, // Make the chart responsive
    maintainAspectRatio: false, // Allow resizing
    plugins: {
      tooltip: {
        callbacks: {
          label: function (tooltipItem: any) {
            return `${tooltipItem.label}: ${tooltipItem.raw} kg`; // Show the value in kilograms
          },
        },
      },
      legend: {
        position: 'top' as const, // Position of the legend (can be 'top', 'bottom', 'left', 'right')
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Months', // Label for x-axis (Months)
        },
      },
      y: {
        title: {
          display: true,
          text: 'Sales (Kg)', // Label for y-axis (Sales in kg)
        },
        beginAtZero: true, // Ensure y-axis starts at 0
      },
    },
    animation: {
      duration: 1000, // Animation duration
    },
  };

  return (
    <div className="w-full max-w-md mx-auto mt-10">
      <h2 className="text-center text-[15px] sm2:text-[17px] lg:text-xl font-bold mb-4">
        Monthly Sales Trend
      </h2>
      <div className="w-full h-[400px]">
        {' '}
        {/* Adjust the height as needed */}
        <Line data={chartData} options={chartOptions} />
      </div>
    </div>
  );
}
