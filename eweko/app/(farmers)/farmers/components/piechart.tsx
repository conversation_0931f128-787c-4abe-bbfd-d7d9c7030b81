'use client';

import { ArcElement, Chart as ChartJ<PERSON>, Legend, Tooltip } from 'chart.js';
import { Pie } from 'react-chartjs-2';

ChartJS.register(ArcElement, Tooltip, Legend);

export default function CategoryPieChart({
  farmersProduce,
}: {
  farmersProduce: any[];
}) {
  // Group data by category and calculate total sold for each
  const categoryTotals: { [category: string]: number } = farmersProduce.reduce(
    (acc, item) => {
      acc[item.category] = (acc[item.category] || 0) + item.qtySold;
      return acc;
    },
    {} as { [category: string]: number }
  );

  // Calculate total kilograms sold across all categories
  const totalKgSold = Object.values(categoryTotals).reduce(
    (sum, value) => sum + value,
    0
  );

  // Prepare data for the chart
  const chartData = {
    labels: Object.keys(categoryTotals), // Category names
    datasets: [
      {
        data: Object.values(categoryTotals).map(value =>
          ((value / totalKgSold) * 100).toFixed(2)
        ), // Percentages
        backgroundColor: [
          '#394532',
          '#587240',
          '#D64847',
          '#FFCE56',
          '#85B04C',
          '#9966FF',
          '#FF9F40',
        ], // Example colors
      },
    ],
  };

  const chartOptions = {
    responsive: true, // Make the chart responsive
    maintainAspectRatio: false, // Allow resizing
    plugins: {
      tooltip: {
        callbacks: {
          label: function (tooltipItem: any) {
            return `${tooltipItem.label}: ${tooltipItem.raw}%`; // Custom tooltip label
          },
        },
      },
      legend: {
        position: 'right' as const, // Position of the legend (can be 'top', 'bottom', 'left', 'right')
        labels: {
          boxWidth: 30, // Width of the legend box
        },
      },
    },
    animation: {
      animateRotate: true, // Enable rotation animation
      animateScale: true, // Enable scale animation
    },
  };

  return (
    <div className="w-full  max-w-md mx-auto mt-10">
      <h2 className="text-center text-[15px] sm2:text-[17px] lg:text-xl font-bold ">
        Category Sales Distribution
      </h2>
      <div className="w-full h-[300px] sm3:h-[450px]  ">
        {' '}
        <Pie data={chartData} options={chartOptions} />
      </div>
    </div>
  );
}
