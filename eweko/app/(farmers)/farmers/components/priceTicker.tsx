// priceTicker.tsx
'use client';

import { useSafeQuery } from '@/axios/query-client';
import { AllRoutes } from '@/routes';
import { Skeleton } from '@/components/ui/skeleton';

interface PriceTrend {
  categoryName: string;
  currentPrice: number;
  previousPrice: number;
}

const PriceTicker = () => {
  const { data: responseData, isLoading, error } = useSafeQuery<{ data: PriceTrend[] }>(
    ['price-trends'],
    `${AllRoutes.market}/price-trends/latest`
  );

  // Extract the price trends from the response data
  const priceTrends = responseData?.data || [];
  
  // Duplicate items for seamless infinite scroll effect
  const tickerItems = [...priceTrends, ...priceTrends];

  // Format price with Naira symbol and thousand separators, no decimals
  const formatPrice = (price: number): string => {
    return `₦${Math.round(price).toLocaleString('en-US')}`;
  };

  // if (isLoading) {
  //   return (
  //     <div className="bg-eweko_green_light py-2 overflow-hidden w-full">
  //       <div className="whitespace-nowrap">
  //         {/* {[...Array(3)].map((_, i) => (
  //           <Skeleton key={i} className="h-6 w-60 inline-block mx-8" />
  //         ))} */}
  //       </div>
  //     </div>
  //   );
  // }

  // if (error) {
  //   console.error('Error loading price trends:', error);
  //   return (
  //     <div className="bg-eweko_green_light py-2 text-center text-white">
  //       Error loading price trends. Please try again later.
  //     </div>
  //   );
  // }

  // if (!priceTrends.length) {
  //   return (
  //     <div className="bg-eweko_green_light py-2 text-center text-white">
  //       No price trends available. Check back later.
  //     </div>
  //   );
  // }

  return (
    <div className="bg-eweko_green_light text-white py-2 overflow-hidden w-full">
      <div className="whitespace-nowrap animate-scroll">
        {tickerItems.map((item, index) => (
          <div
            key={`${item.categoryName}-${index}`}
            className="inline-flex items-center mx-4 font-bold text-eweko_green_dark md:mx-8 text-xs sm:text-sm md:text-base lg:text-lg"
            aria-label={`${item.categoryName} price ticker`}
          >
            <span className='text-white'>{item.categoryName}:</span>
            {/* <span className='mx-1'>:</span> */}
            <span className='ml-1'>Current Price = {formatPrice(item.currentPrice)}</span>
            <span className='text-white mx-2'>|</span>
            <span>Previous Week = {formatPrice(item.previousPrice || 0)}</span>
          </div>
        ))}
      </div>
      <style jsx>{`
        @keyframes scroll {
          0% { transform: translateX(0); }
          100% { transform: translateX(-50%); }
        }
        .animate-scroll {
          display: inline-block;
          white-space: nowrap;
          animation: scroll 60s linear infinite;
        }
        .animate-scroll:hover {
          animation-play-state: paused;
        }
      `}</style>
    </div>
  );
};

export default PriceTicker;