'use client';

import { useEffect, useState } from 'react';
import { FullscreenLoader } from '../../../components/FullscreenLoader';
import { IsContentReady } from '../../../components/IsContentReady';
import Dashboard from '../components/dashboard';

export const Wrapper = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!mounted) return <FullscreenLoader />;

  return (
    <div className="w-full flex flex-col gap-12">
      <div className="w-full p-[20px] md:p-[30px] xl:p-[40px] ">
        <IsContentReady />
        <Dashboard />
      </div>
    </div>
  );
};
