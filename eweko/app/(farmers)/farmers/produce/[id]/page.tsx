import { Metadata, ResolvingMetadata } from 'next';
import { AllRoutes } from '../../../../../routes';
import { IProduce, Produce } from '../../../../(buyers)/buyers/types';
import { SingleProduce } from './components/SingleProduce';

export const revalidate = 60;
export const dynamicParams = true; // or false, to 404 on unknown paths

type Props = {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  // read route params
  const { id } = await params;

  // fetch data
  const produce: Produce = await fetch(`${AllRoutes.produce}/${id}`).then(res =>
    res.json()
  );

  // optionally access and extend (rather than replace) parent metadata
  const previousImages = (await parent).openGraph?.images || [];

  return {
    title: `${produce.name} | EwekoAggregate`,
    openGraph: {
      images: ['/some-specific-page-image.jpg', ...previousImages],
    },
  };
}

export async function generateStaticParams() {
  try {

    if (!AllRoutes.produce) {
      throw new Error('API route is undefined');
    }

    const response = await fetch(AllRoutes.produce);

    if (!response.ok) {
      throw new Error(
        `Failed to fetch: ${response.status} ${response.statusText}`
      );
    }

    const produces: any = await response.json();
    const { data }: { data: IProduce[] } = produces;

    return data.map(produce => ({
      id: String(produce.id),
    }));
  } catch (error) {
    console.error('Error in generateStaticParams:', error);
    return []; // Return empty array to prevent build failure
  }
}

export default async function SingleProduct({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const produce: IProduce = await fetch(`${AllRoutes.produce}/${id}`).then(
    res => res.json()
  );

  return (
    <main className="w-full p-[20px] md:p-[30px] xl:p-[40px] ">
      <SingleProduce produce={produce} />
    </main>
  );
}
