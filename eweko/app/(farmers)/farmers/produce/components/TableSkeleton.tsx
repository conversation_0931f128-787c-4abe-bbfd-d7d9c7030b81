import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

export function TableSkeleton({ columnCount = 6, rowCount = 5 }) {
  return (
    <div className="rounded-md border bg-white p-4 min-w-[500px]">
      <Table>
        {/* Table Header Skeleton */}
        <TableHeader>
          <TableRow>
            {Array.from({ length: columnCount }).map((_, index) => (
              <TableHead
                key={index}
                className="text-eweko_green_dark font-bold"
              >
                <Skeleton className="h-6 w-20" />
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>

        {/* Table Body Skeleton */}
        <TableBody>
          {Array.from({ length: rowCount }).map((_, rowIndex) => (
            <TableRow key={rowIndex} className="animate-pulse">
              {Array.from({ length: columnCount }).map((_, colIndex) => (
                <TableCell key={colIndex}>
                  <Skeleton className="h-6 w-full" />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
