'use client';

import { ColumnDef } from '@tanstack/react-table';
import { IoTrash } from 'react-icons/io5';
import { TbEdit } from 'react-icons/tb';
import { ProduceTableRow } from '../../../../(buyers)/buyers/types';
import Image from 'next/image';
import Link from 'next/link';
import { BsFillEyeFill } from 'react-icons/bs';

export const columns: ColumnDef<ProduceTableRow>[] = [
  {
    accessorKey: 'sn',
    header: 'S/N',
    cell: ({ row }) => {
      return (
        <div className="min-w-[60px]">
          <p>{row.getValue('sn')}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'image',
    header: 'Image',
    cell: ({ row }) => {
      return (
        <div className="min-w-[50px] rounded-[4px]">
          <Image
            src={row.getValue('image')}
            alt=""
            width={50}
            height={50}
            className="rounded-[4px]"
          />
        </div>
      );
    },
  },
  {
    accessorKey: 'produce',
    header: 'Produce',
    cell: ({ row }) => {
      return (
        <div className="min-w-[150px]">
          <p>{row.getValue('produce')}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'availableQty',
    header: 'Available Qty',
    cell: ({ row }) => {
      return (
        <div className="min-w-[100px]">
          <p>{row.getValue('availableQty')}Kg</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'pricePerKg',
    header: 'Price/Kg',
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue('pricePerKg'));
      const formatted = new Intl.NumberFormat('en-NG', {
        style: 'currency',
        currency: 'NGN',
      }).format(amount);

      return <div className="">{formatted}</div>;
    },
  },
  {
    accessorKey: 'harvestDate',
    header: 'Harvest Date',
    cell: ({ row }) => {
      return (
        <div className="min-w-[100px]">
          <p>{row.getValue('harvestDate')}</p>
        </div>
      );
    },
  },
  {
    id: 'action', // No need for accessorKey since we're rendering custom content
    header: 'Action',
    cell: ({ row }) => {
      const product = row.original; // Get the row data

      const handleDelete = () => {};

      return (
        <div className="flex gap-4 min-w-[80px]">
          <Link href={`/farmers/produce/${product.id}`}>
            <BsFillEyeFill className="text-[#636363] cursor-pointer hover:text-eweko_green_dark text-lg group-hover:text-white" />
          </Link>

          <Link href={`/farmers/editProduce?productId=${product.id}`}>
            <TbEdit className="text-[#636363] cursor-pointer hover:text-eweko_green_dark text-lg group-hover:text-white" />
          </Link>

          <IoTrash
            className="text-red-500 cursor-pointer hover:text-red-700 text-lg"
            onClick={handleDelete}
          />
        </div>
      );
    },
  },
];
