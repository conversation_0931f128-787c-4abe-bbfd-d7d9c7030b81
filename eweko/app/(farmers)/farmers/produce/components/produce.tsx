'use client';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import delete2 from '@/public/icons/delete2.png';
import edit2 from '@/public/icons/edit2.png';
import trash from '@/public/icons/trash.png';
import axiosInstance from '../../../../../axios/axios.instance';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { AllRoutes } from '../../../../../routes';
import { useSafeQuery } from '../../../../../axios/query-client';
import {
  IProduce,
  Produce,
  ProduceTableRow,
} from '../../../../(buyers)/buyers/types';
import { Button } from '../../../../../components/ui/button';
import { DataTable } from './data-table';
import { columns } from './columns';
import { TableSkeleton } from './TableSkeleton';
import { useGlobalState } from '../../../../../globalStore';

export default function Products() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false); // For dialog box
  const [selectedProduceId, setSelectedProduceId] = useState<string | null>(
    null
  );
  const [successMessage, setSuccessMessage] = useState('');
  const router = useRouter();
  const queryClient = useQueryClient();
  const { produceUpdated } = useGlobalState();
  const [fetchedProduces, setFetchedProduces] = useState<IProduce[] | null>(
    null
  );
  const [filteredProduces, setFilteredProduces] = useState<
    ProduceTableRow[] | null
  >(null);

  const [uniqueCats, setUniqueCats] = useState<string[] | null>(null);
  function getUniqueCategoryNames(produces: IProduce[]): string[] {
    const categorySet = new Set(produces.map(produce => produce.category.name));
    return Array.from(categorySet);
  }

  const {
    data: produces,
    isSuccess,
  } = useSafeQuery<any>(['produces'], `${AllRoutes.produce}/farmer`, {
    enabled: true,
  });

  useEffect(() => {
    router.refresh();
  }, [produceUpdated]);

  useEffect(() => {
    if (isSuccess) {
      setFetchedProduces(produces.data);
      router.push('/farmers/produce');
    }
  }, [isSuccess]);

  useEffect(() => {
    if (fetchedProduces) {
      const cats = getUniqueCategoryNames(fetchedProduces);
      setUniqueCats(cats);
    }
  }, [fetchedProduces]);

  useEffect(() => {
    if (fetchedProduces) {
      const produces: ProduceTableRow[] = fetchedProduces
        .filter((item: IProduce) =>
          selectedCategory ? item.category.name === selectedCategory : true
        )
        .map((item: IProduce, i) => ({
          sn: i + 1,
          id: item.id,
          image: item.images[0] || '',
          produce: item.name,
          availableQty: item.stock,
          pricePerKg: item.price,
          harvestDate: new Date(item.harvestDate).toLocaleDateString('en-GB'),
        }));

      setFilteredProduces(produces);
    }
  }, [selectedCategory, fetchedProduces]);

  const handleDeleteClick = (produceId: string) => {
    setSelectedProduceId(produceId); // Set the produce ID for deletion
    setIsDialogOpen(true); // Open the confirmation dialog
  };

  const confirmDelete = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_APIURL}/produce/${selectedProduceId}`,
        {
          method: 'DELETE',
        }
      );
      // Invalidate and refetch the produce data after deletion
      queryClient.invalidateQueries({ queryKey: ['produce'] });
      setSuccessMessage('Product removed successfully!');
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (error) {
      console.error('Error deleting produce:', error);
    } finally {
      setIsDialogOpen(false);
    }
  };

  const cancelDelete = () => {
    setIsDialogOpen(false); // Close the dialog without deleting
  };


  return (
    <>
      <section className="flex flex-col-reverse sm2:flex-row justify-between mt-3">
        <div className="flex flex-col xl:flex-row w-full">
          <div className="flex flex-wrap w-full gap-3">
            <Button
              onClick={() => setSelectedCategory(null)}
              className={`px-4 py-1 w-fit text-[min(10vw,14px)] rounded-lg bg-eweko_green_dark text-white shadow-md transition-all duration-300`}
            >
              All Produce
            </Button>
            {uniqueCats?.map((category, idx) => (
              <Button
                key={idx}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-1 w-fit text-[min(10vw,14px)] rounded-lg bg-eweko_green_dark text-white shadow-md transition-all duration-300 ${
                  selectedCategory === category
                    ? 'bg-[#394532] text-white hover:bg-white hover:text-eweko_green_dark'
                    : 'bg-white text-[#394532] hover:bg-eweko_green_dark hover:text-white'
                }`}
              >
                {category || 'All Produce'}
              </Button>
            ))}
          </div>
        </div>
        <div className="w-full flex justify-end sm2:mb-0 mb-4">
          <Button
            className="px-4 py-1 w-fit text-[min(10vw,14px)] rounded-lg bg-eweko_green_dark text-white shadow-md transition-all duration-300"
            onClick={() => router.push(`/farmers/addProduce`)}
          >
            Add Produce
          </Button>
        </div>
      </section>

      <div className="mt-3 mb-8">
        {filteredProduces ? (
          <DataTable columns={columns} data={filteredProduces} />
        ) : (
          <TableSkeleton columnCount={7} rowCount={5} />
        )}
      </div>

      {isDialogOpen && (
        <div className="fixed inset-0 bg-gray-900 bg-opacity-10 shadow-[500px] flex items-center justify-center z-30">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-full">
              <Image
                src={trash}
                width={24}
                height={15}
                alt="delete"
                className="ml-[46%]"
              />
            </div>

            <p className="text-[18px] text-eweko_green_dark text-center mt-4">
              Delete Produce
            </p>
            <p className="text-[12px] text-eweko_green_dark mt-5 text-center mb-4">
              Do you want to delete this produce, deleted produce
              <br /> can’t be restored
            </p>
            <div className="flex justify-end gap-6">
              <button
                onClick={cancelDelete}
                className="bg-gray-100 text-eweko_green_dark px-10 py-2 rounded-md"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="text-white bg-white px-4 py-2 rounded-md"
              >
                Delete Produce
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
