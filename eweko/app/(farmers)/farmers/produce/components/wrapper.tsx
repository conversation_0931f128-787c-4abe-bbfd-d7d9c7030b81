'use client';

import Products from './produce';
import { AllRoutes } from '../../../../../routes';
import { useSafeQuery } from '../../../../../axios/query-client';
import { FarmerStatItem } from '../../../../(buyers)/buyers/types';
import { Skeleton } from '../../../../../components/ui/skeleton';
import { useEffect } from 'react';
import { useGlobalState } from '../../../../../globalStore';

export const Wrapper = () => {
  const {
    data = [],
    isSuccess,
    isLoading,
  } = useSafeQuery<FarmerStatItem[]>(
    ['summary'],
    `${AllRoutes.users}/farmer/dashboard`,
    {
      enabled: true,
    }
  );

  const { loggedInUser } = useGlobalState();

  useEffect(() => {
    if (loggedInUser?.id) {
      window.location.reload();
    }
  }, []);

  return (
    <div className="w-full flex flex-col gap-5">
      <p className=" text-[min(10vw,25px)]">Produce</p>

      {isLoading ? (
        <section className="grid grid-cols-1 sm2:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-8 ">
          <Skeleton className="h-[125px] w-[100%] rounded-xl bg-white border flex flex-col gap-3 justify-center p-4">
            <Skeleton className="h-6 w-[40%] bg-gray-100 rounded-lg" />
            <Skeleton className="h-9 w-[80%] bg-gray-100 rounded-lg" />
          </Skeleton>

          <Skeleton className="h-[125px] w-[100%] rounded-xl bg-white border flex flex-col gap-3 justify-center p-4">
            <Skeleton className="h-6 w-[40%] bg-gray-100 rounded-lg" />
            <Skeleton className="h-9 w-[80%] bg-gray-100 rounded-lg" />
          </Skeleton>

          <Skeleton className="h-[125px] w-[100%] rounded-xl bg-white border flex flex-col gap-3 justify-center p-4">
            <Skeleton className="h-6 w-[40%] bg-gray-100 rounded-lg" />
            <Skeleton className="h-9 w-[80%] bg-gray-100 rounded-lg" />
          </Skeleton>

          <Skeleton className="h-[125px] w-[100%] rounded-xl bg-white border flex flex-col gap-3 justify-center p-4">
            <Skeleton className="h-6 w-[40%] bg-gray-100 rounded-lg" />
            <Skeleton className="h-9 w-[80%] bg-gray-100 rounded-lg" />
          </Skeleton>
        </section>
      ) : (
        <section className="grid grid-cols-1 sm2:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-8 ">
          {data.map((stat, i) => (
            <div
              key={i}
              className="w-[100%] rounded-xl bg-white border flex flex-col gap-2 justify-center p-6"
            >
              <p className="text-[#292929] text-[min(10vw,15px)]">
                {stat.label}
              </p>
              <h1 className="text-[#292929] text-[min(10vw,24px)] font-semibold">
                {stat.value}
              </h1>
            </div>
          ))}
        </section>
      )}
      <Products />
    </div>
  );
};
