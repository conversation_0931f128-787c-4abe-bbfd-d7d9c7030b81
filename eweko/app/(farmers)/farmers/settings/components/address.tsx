'use client';

import { useSafeMutation, useSafeQuery } from '@/axios/query-client';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { FC, useEffect, useState } from 'react';
import { Button } from '../../../../../components/ui/button';
import { Label } from '../../../../../components/ui/label';
import { InlineLoader } from '../../../../components/InlineLoader';
import { AllRoutes } from '../../../../../routes';
import { Address } from '../../../../(buyers)/buyers/types';
import { useGlobalState } from '../../../../../globalStore';
import { capitalizeFirstLetter } from '../../../../../lib/utils';
import { HiPencil } from 'react-icons/hi';
import { FaPlus } from 'react-icons/fa6';
import { AddAddressForm } from './AddAddress';
import { IoTrash } from 'react-icons/io5';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import { EditAddressForm } from './EditAddress';

const AddressPage: FC = () => {
  const {
    loggedInUser,
    settingsAddressStep,
    setSettingsAddressStep,
    settingsAddrAdded,
    setSettingsAddrAdded,
    setSettingsAddrEdited,
    settingsAddrEdited,
  } = useGlobalState();
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [loading, setLoading] = useState(true);
  const [defaultAddr, setDefaultAddr] = useState<Address | null>(null);
  const [addr2edit, setAddr2edit] = useState<Address | null>(null);
  const [addr2remove, setAddr2remove] = useState<Address | null>(null);
  const [defaultAddrSet, setDefaultAddrSet] = useState(false);

  const { refetch } = useSafeQuery<Address[]>(
    ['addresses'],
    `${AllRoutes.addresses}?userId=${loggedInUser?.id}`,
    {
      enabled: !!loggedInUser?.id,
      refetchOnWindowFocus: true,
    }
  );

  const fetchAddresses = async () => {
    const { data, isError, error, isSuccess } = await refetch();

    if (isError) {
      // Handle error appropriately
    }

    if (isSuccess && data) {
      setAddresses(data);
      setLoading(false);
      if (settingsAddrAdded || settingsAddrEdited) {
        setSettingsAddrAdded(false);
        setSettingsAddrEdited(false);
      }

      const defaultOne = data.find(addr => addr.isDefault);
      if (defaultOne) {
        setDefaultAddr(defaultOne);
      } else if (data.length > 0) {
        // Fallback to first address if no default marked
        setDefaultAddr(data[0]);
      }
    }
  };

  useEffect(() => {
    fetchAddresses();
  }, []);

  useEffect(() => {
    if (settingsAddrAdded || settingsAddrEdited) {
      fetchAddresses();
    }
  }, [settingsAddrAdded, settingsAddrEdited]);

  const { mutate: removeAddress } = useSafeMutation<any, Error>(
    `${AllRoutes.addresses}/${addr2remove?.id}`,
    'delete',
    {
      onSuccess: data => {
        setAddr2remove(null);
        fetchAddresses();
        toast.success('Address removed successfully');
      },
      onError: (error: any) => {
        if (typeof error === 'object') {
          if (Array.isArray(error?.errorMessage)) {
            // Show each error as a toast
            error.errorMessage.forEach((msg: string) => {
              toast.error(msg);
            });
          } else if (typeof error?.errorMessage === 'string') {
            toast.error(error.errorMessage);
          } else if (error?.message) {
            toast.error(error.message);
          } else {
            toast.error('An unexpected error occurred.');
          }
        } else {
          toast.error('An unknown error occurred.');
        }
      },
    }
  );

  useEffect(() => {
    if (addr2remove) {
      removeAddress();
    }
  }, [addr2remove]);

  const handleSelectDefault = (id: string) => {
    const selected = addresses.find(addr => addr.id === id);
    if (selected) {
      setDefaultAddr(selected);
      setDefaultAddrSet(true);
    }
  };

  const { mutate: setDefault } = useSafeMutation<
    Address,
    Error,
    {
      isDefault: boolean;
    }
  >(`${AllRoutes.addresses}/${defaultAddr?.id}`, 'patch', {
    onSuccess: data => {
      toast.success('Default address set successfully');
      setDefaultAddrSet(false);
      fetchAddresses();
    },
    onError: (error: any) => {
      if (typeof error === 'object') {
        if (Array.isArray(error?.errorMessage)) {
          // Show each error as a toast
          error.errorMessage.forEach((msg: string) => {
            toast.error(msg);
          });
        } else if (typeof error?.errorMessage === 'string') {
          toast.error(error.errorMessage);
        } else if (error?.message) {
          toast.error(error.message);
        } else {
          toast.error('An unexpected error occurred.');
        }
      } else {
        toast.error('An unknown error occurred.');
      }
    },
  });

  useEffect(() => {
    if (defaultAddr && defaultAddrSet) {
      if (defaultAddr?.isDefault) return; // Already default

      setDefault({
        isDefault: true,
      });
    }
  }, [defaultAddr, defaultAddrSet]);

  if (loading) {
    return (
      <div className="w-full h-[50vh] flex items-center justify-center">
        <InlineLoader color="eweko_green" size="100" textSize="30" />
      </div>
    );
  }

  return (
    <section className="flex flex-col w-full gap-4 rounded-lg">
      <div className="w-full p-6 h-fit">
        {settingsAddressStep === 1 && (
          <div className="flex flex-col gap-6">
            <div className="border-b pb-3">
              <h1 className="font-bold md3:text-[20px]">Addresses</h1>
              <Button
                variant="link"
                className="px-0 hover:no-underline text-eweko_green_light font-[500] hover:text-eweko_green_dark"
                onClick={() => setSettingsAddressStep(2)}
              >
                Add Address <FaPlus />
              </Button>
            </div>

            {addresses.length > 0 ? (
              <RadioGroup
                value={defaultAddr?.id || ''}
                onValueChange={handleSelectDefault}
                className="space-y-4"
              >
                {addresses.map((address, i) => (
                  <div
                    key={address.id}
                    className={`flex flex-col gap-4 sm3:flex-row justify-between items-start pb-6 ${
                      i !== addresses.length - 1
                        ? 'border-b border-[#d9d9d9]'
                        : ''
                    }`}
                  >
                    <div className="flex gap-3 w-[90%]">
                      <RadioGroupItem
                        value={address.id}
                        id={address.id}
                        className="mt-1"
                      />
                      <Label
                        className="text-[16px] text-eweko_green_dark flex flex-col gap-1"
                        htmlFor={address.id}
                      >
                        <h3 className="font-bold">
                          {address.isDefault
                            ? 'Default Address'
                            : `Address ${i + 1}`}
                        </h3>

                        <p className="text-[#636363] text-[14px]">
                          {`${address.houseNumber}, ${address.streetName}, ${address.community}, ${address.lga}, ${address.state}`}
                        </p>
                      </Label>
                    </div>

                    <div className="w-fit flex items-center gap-3 ml-7 sm3:ml-0">
                      <div
                        className="text-[13px] text-eweko_green_dark flex gap-1 items-center cursor-pointer py-2 px-3 border rounded-[7px] border-[#d9d9d9] hover:bg-eweko_green_light hover:text-white duration-300 transition-all"
                        onClick={() => {
                          setAddr2edit(address);
                          setSettingsAddressStep(3);
                        }}
                      >
                        <span className="hidden md3:flex">EDIT</span>
                        <HiPencil size={17} />
                      </div>
                      <div
                        className="text-[13px] text-eweko_green_dark flex gap-1 items-center cursor-pointer py-2 px-3 border rounded-[7px] border-[#d9d9d9] hover:bg-eweko_green_light hover:text-white duration-300 transition-all"
                        onClick={() => setAddr2remove(address)}
                      >
                        <span className="hidden md3:flex">Remove</span>
                        <IoTrash size={17} />
                      </div>
                    </div>
                  </div>
                ))}
              </RadioGroup>
            ) : (
              <p className="text-red-600 text-center mt-4">No address found</p>
            )}
          </div>
        )}

        {settingsAddressStep === 2 && (
          <div className="flex flex-col gap-3">
            <h1 className="font-bold md3:text-[20px]">Add Address</h1>
            <AddAddressForm />
          </div>
        )}

        {settingsAddressStep === 3 && (
          <div className="flex flex-col gap-3">
            <h1 className="font-bold md3:text-[20px]">Edit Address</h1>
            <EditAddressForm address={addr2edit} />
          </div>
        )}
      </div>
    </section>
  );
};

export default AddressPage;
