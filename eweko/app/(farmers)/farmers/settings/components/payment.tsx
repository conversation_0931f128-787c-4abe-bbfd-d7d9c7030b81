'use client';

import { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useGlobalState } from '../../../../../globalStore';
import {
  useSafeMutation,
  useSafeQuery,
} from '../../../../../axios/query-client';
import { AllRoutes } from '../../../../../routes';
import { toast } from 'react-toastify';

// ✅ Zod Schema for validation
const paymentSchema = z.object({
  farmerAccountNumber: z
    .string()
    .min(10, 'Account number must be at least 10 digits'),
  farmerAccountName: z.string().min(3, 'Name must be at least 3 characters'),
  farmerBankName: z.string().min(2, 'Bank name is required'),
  farmerBankBranch: z.string().min(2, 'Branch is required'),
});

type PaymentFormValues = z.infer<typeof paymentSchema>;

export default function PaymentPage() {
  const { loggedInUser } = useGlobalState();
  const [showDialog, setShowDialog] = useState(false);

  // ✅ Form Hook Setup
  const form = useForm<PaymentFormValues>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      farmerAccountNumber: '',
      farmerAccountName: '',
      farmerBankName: '',
      farmerBankBranch: '',
    },
  });

  // ✅ Fetch User Data
  const { data: userData, isSuccess } = useSafeQuery<any>(
    ['user'],
    `${AllRoutes.users}/${loggedInUser?.id}`,
    {
      enabled: !!loggedInUser?.id,
      refetchOnWindowFocus: true,
    }
  );

  // ✅ Debugging: Log userData
  useEffect(() => {
    if (isSuccess && userData) {
      form.reset({
        farmerAccountNumber: userData.farmerAccountNumber || '',
        farmerAccountName: userData.farmerAccountName || '',
        farmerBankName: userData.farmerBankName || '',
        farmerBankBranch: userData.farmerBankBranch || '',
      });
    }
  }, [isSuccess, userData, form]);

  // ✅ Mutation for updating payment details
  const { mutateAsync: updatePayment } = useSafeMutation<
    any,
    Error,
    Record<string, any>
  >(`${AllRoutes.users}/farmers/${loggedInUser?.id}`, 'patch', {
    onSuccess: () => {
      toast.success('Farmer payment details updated successfully');
    },
    onError: (error: any) => {
      console.error('Error updating farmer payment details:', error);
      toast.error(
        error?.response?.data?.message ||
          'Failed to update farmer payment details'
      );
    },
  });

  // ✅ Form Submission Handler
  const onSubmit = async (values: PaymentFormValues) => {
    await updatePayment(values);
    // setShowDialog(true);
  };

  return (
    <section className="p-6 flex-col flex w-full gap-4 rounded-lg space-y-4">
      <div>
        <h2 className="text-[17px] font-semibold">Account Details</h2>
        <p className="text-[12px] text-muted-foreground">
          Update your payment details
        </p>
      </div>

      {/* ✅ ShadCN Form */}
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          {/* ✅ Account Number */}
          <FormField
            control={form.control}
            name="farmerAccountNumber"
            render={({ field }) => (
              <FormItem>
                <Label>Account Number</Label>
                <FormControl>
                  <Input className="shadow-none" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* ✅ Account Holder's Name */}
          <FormField
            control={form.control}
            name="farmerAccountName"
            render={({ field }) => (
              <FormItem>
                <Label>Account Holder&apos;s Name</Label>
                <FormControl>
                  <Input className="shadow-none" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* ✅ Bank Name */}
          <FormField
            control={form.control}
            name="farmerBankName"
            render={({ field }) => (
              <FormItem>
                <Label>Bank Name</Label>
                <FormControl>
                  <Input className="shadow-none" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* ✅ Branch */}
          <FormField
            control={form.control}
            name="farmerBankBranch"
            render={({ field }) => (
              <FormItem>
                <Label>Branch</Label>
                <FormControl>
                  <Input className="shadow-none" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* ✅ Submit Button */}
          <div className="col-span-1 md:col-span-2 flex">
            <Button
              type="submit"
              className="bg-eweko_green_dark hover:bg-eweko_green_light shadow-none text-[12px]"
            >
              Save Account
            </Button>
          </div>
        </form>
      </Form>

      {/* ✅ Success Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Account Updated</DialogTitle>
          </DialogHeader>
          <p className="text-sm text-gray-600">
            Your payment details have been updated successfully.
          </p>
          <DialogFooter>
            <Button onClick={() => setShowDialog(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </section>
  );
}
