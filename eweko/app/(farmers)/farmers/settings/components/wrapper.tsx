'use client';

import { useEffect, useState } from 'react';
import { FullscreenLoader } from '../../../../components/FullscreenLoader';
import AddressPage from '../components/address';
import BillingPage from './payment';
import Notification from '../components/notification';
import Settings from '../components/profile';
import AccountSecurity from '../components/security';
import Sidebar from '../components/sidebar';
import PaymentPage from './payment';
export const Wrapper = () => {
  const [mounted, setMounted] = useState(false);
  const [activeSection, setActiveSection] = useState('Profile'); // State for active content

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  const renderContent = () => {
    switch (activeSection) {
      case 'Profile':
        return <Settings />;
      case 'Notifications':
        return <Notification />;
      case 'Payment':
        return <PaymentPage />;
      case 'Addresses':
        return <AddressPage />;
      case 'Security':
        return <AccountSecurity />;
      default:
        return <Settings />;
    }
  };

  return (
    <div className="w-full flex flex-col gap-12">
      <div className="w-full p-[20px] md:p-[30px] xl:p-[40px] flex flex-row gap-6 ">
        <Sidebar
          activeSection={activeSection}
          setActiveSection={setActiveSection}
        />

        <div className="flex-1 h-fit bg-white rounded-lg border p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};
