'use client';

import { HiOutlineArrowDown, HiOutlineArrowUp } from 'react-icons/hi';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../../../components/ui/card';
import { ITransaction } from '../../../../(buyers)/buyers/types';
import { useEffect, useState } from 'react';
import { subMonths, endOfMonth, isAfter, isBefore } from 'date-fns';

interface TransactionStat {
  label: string;
  value: number;
  change: number;
}

export const TransactionsSummary = ({
  transactions,
}: {
  transactions: ITransaction[];
}) => {
  const [stats, setStats] = useState<TransactionStat[]>([]);

  useEffect(() => {
    if (transactions && transactions.length > 0) {
      const currentDate = new Date();
      const lastMonthStart = subMonths(
        new Date(currentDate.getFullYear(), currentDate.getMonth(), 1),
        1
      );
      const lastMonthEnd = endOfMonth(lastMonthStart);

      const lastMonthTransactions = transactions.filter(transaction => {
        const txnDate = new Date(transaction.createdAt);
        return (
          isAfter(txnDate, lastMonthStart) && isBefore(txnDate, lastMonthEnd)
        );
      });

      const total = transactions.length;
      const completed = transactions.filter(
        txn => txn.status === 'Completed'
      ).length;
      const pending = transactions.filter(
        txn => txn.status === 'Pending'
      ).length;
      const failed = transactions.filter(txn => txn.status === 'Failed').length;

      const lastMonthTotal = lastMonthTransactions.length;
      const lastMonthCompleted = lastMonthTransactions.filter(
        txn => txn.status === 'Completed'
      ).length;
      const lastMonthPending = lastMonthTransactions.filter(
        txn => txn.status === 'Pending'
      ).length;
      const lastMonthFailed = lastMonthTransactions.filter(
        txn => txn.status === 'Failed'
      ).length;

      const calculateChange = (current: number, previous: number) =>
        previous
          ? ((current - previous) / previous) * 100
          : current > 0
            ? 100
            : 0;

      const transactionStats: TransactionStat[] = [
        {
          label: 'Total',
          value: total,
          change: calculateChange(total, lastMonthTotal),
        },
        {
          label: 'Successful',
          value: completed,
          change: calculateChange(completed, lastMonthCompleted),
        },
        {
          label: 'Pending',
          value: pending,
          change: calculateChange(pending, lastMonthPending),
        },
        {
          label: 'Failed',
          value: failed,
          change: calculateChange(failed, lastMonthFailed),
        },
      ];

      setStats(transactionStats);
    }
  }, []);

  return (
    <div className="w-full flex flex-col gap-6 py-0">
      <h1 className="text-xl font-bold">Transactions</h1>

      {stats.length < 1 ? (
        <section className="flex items-center justify-center">
          <Card className="w-full bg-white hover:bg-eweko_green_dark hover:text-white p-6 rounded-[10px] text-[#292929] flex flex-col gap-6 border border-[#d9d9d9] items-start">
            <CardContent>
              <p className="text-red-500">No transaction yet</p>
            </CardContent>
          </Card>
        </section>
      ) : (
        <section className="grid grid-cols-1 sm3:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-8">
          {stats.map((metric, idx) => (
            <Card
              key={idx}
              className="bg-white hover:bg-eweko_green_dark hover:text-white p-6 rounded-[10px] text-[#292929] flex flex-col gap-6 border border-[#d9d9d9] items-start"
            >
              <CardHeader>
                <CardTitle className="font-normal text-[18px] md2:text-[20px]">
                  {metric.label}
                </CardTitle>
              </CardHeader>

              <CardContent>
                <h1 className="text-3xl md:text-4xl lg2:text-5xl font-bold">
                  {metric.value.toLocaleString()}
                </h1>
              </CardContent>

              <div
                className={`text-[14px] md2:text-[16px] flex items-center gap-3 lg:flex-col lg:items-start xl:flex-row xl:items-center
    ${metric.change > 0 ? 'text-green-500' : metric.change < 0 ? 'text-red-500' : 'text-gray-500'}`}
              >
                <div className="flex gap-1 items-center">
                  {metric.change > 0 && <HiOutlineArrowUp />}
                  {metric.change < 0 && <HiOutlineArrowDown />}
                  <span>{Math.abs(metric.change).toFixed(2)}%</span>
                </div>
                <span className="text-[#868686]">Since last month</span>
              </div>
            </Card>
          ))}
        </section>
      )}
    </div>
  );
};
  