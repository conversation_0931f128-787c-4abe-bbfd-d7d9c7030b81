'use client';

import { useSafeQuery } from '../../../../../axios/query-client';
import { AllRoutes } from '../../../../../routes';
import { TransactionsSummary } from './TransactionSummary';
import { TransactionsTable } from './TransactionTable';

export const Wrapper = () => {
  const { data, isSuccess } = useSafeQuery<any>(
    ['transactions'],
    `${AllRoutes.transactions}/farmer`,
    {
      enabled: true,
    }
  );

  return (
    <div className="w-full flex flex-col gap-12">
      {isSuccess && (
        <>
          <TransactionsSummary transactions={data.data} />
          <TransactionsTable transactions={data.data} />
        </>
      )}
    </div>
  );
};
