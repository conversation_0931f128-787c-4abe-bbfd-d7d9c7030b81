'use client';

import ewekoIcon from '@/app/assets/ewekoIcon.png';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { Button } from '../../../../components/ui/button';
import { useGlobalState } from '../../../../globalStore';
import { DashboardHeaderIcons } from '../../../components/DashboardHeaderIcons';
import { DashboardSearch } from '../../../components/DashboardSearch';
import Link from 'next/link';

export const Header = () => {
  const { isSidebarOpen, toggleSidebarOpen } = useGlobalState();

  useEffect(() => {

    // Check if `window` is available (avoids SSR issues)
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        if (window.innerWidth < 640) {
          toggleSidebarOpen(false);
        } else {
          toggleSidebarOpen(true);
        }
      };

      // Initial check on mount
      handleResize();

      // Add event listener for resize
      window.addEventListener('resize', handleResize);

      // Cleanup event listener on unmount
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  return (
    <aside className="fixed top-0 left-0 w-full h-[111px] bg-white border-b-[1px] border-[#d9d9d9] z-10 flex justify-between">
      <div className="w-fit h-full px-4 md:px-8 max-w-[120px] flex items-center justify-center">
        <Button
          onClick={() => toggleSidebarOpen(!isSidebarOpen)}
          className="w-[60px] h-[60px] rounded-full border-[1px] border-eweko_green_light flex items-center justify-center bg-transparent hover:bg-transparent hover:border-[2px] transition-all duration-300"
        >
          <Image
            src={ewekoIcon}
            alt="EwekoAggregate"
            width={36.64}
            height={18.32}
            loading="eager"
          />
        </Button>
      </div>

      <div className="w-fit md:w-[85%] md3:w-[90%] xl:w-full h-full flex items-center justify-end md:justify-between py-6 px-4 sm:px-6 sm2::px-8">
        <div className="hidden md:flex flex-col gap-2">
          <Link href="/">
            <h3 className="font-jsbold text-eweko_green_dark lg:text-xl xl:text-2xl flex">
              EwekoAggregate{' '}
              <span className="hidden lg2:flex mx-2">Supply Chain</span>{' '}
              Dashboard
            </h3>
          </Link>
          <p className="text-eweko_green text-[14px]">
            Connecting People , Process & Platform
          </p>
        </div>
        <DashboardSearch />
        <DashboardHeaderIcons />
      </div>
    </aside>
  );
};
