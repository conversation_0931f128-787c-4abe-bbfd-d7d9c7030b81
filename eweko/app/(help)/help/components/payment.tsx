import downarrow from '@/public/icons/darrow.png';
import uparrow from '@/public/icons/uarrow.png';
import Image from 'next/image';
import { useState } from 'react';

const Payment = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleAnswer = (index: number) => {
    setOpenIndex(openIndex === index ? null : index); // Open/close the clicked question
  };

  const faqData = [
    {
      title: 'Payment',
      question: 'How do I receive payments?',
      answer: 'Payments are transferred directly to your linked bank account.',
    },
    {
      title: 'Payment',

      question: 'Can I change my preferred payment method?',
      answer: 'Yes, you can update your payment method from the settings page.',
    },
    {
      title: 'Payment',

      question: 'What happens if my payment fails?',
      answer:
        'If a payment fails, you’ll be notified and can retry or contact support.',
    },
    {
      title: 'Payment',
      question: 'How do I receive payments?',
      answer: 'Payments are transferred directly to your linked bank account.',
    },
    {
      title: 'Payment',
      question: 'How do I receive payments?',
      answer: 'Payments are transferred directly to your linked bank account.',
    },
    {
      title: 'Payment',
      question: 'How do I receive payments?',
      answer: 'Payments are transferred directly to your linked bank account.',
    },
  ];

  return (
    <div className="">
      {faqData.map((faq, index) => (
        <div key={index} className="border-b border-gray-300 w-full  shadow-sm">
          <button
            onClick={() => toggleAnswer(index)}
            className="w-full pb-2 text-left lg:pr-[20%] p-4 md:px-10  font-medium flex rounded-lg justify-between items-center"
          >
            <div className=" flex  flex-col">
              <span className="text-grey-700 underline text-[10px] text-gray-700">
                {faq.title}
              </span>
              <span className=" text-[12px] md:text-[15px] lg:text-[18px]">
                {faq.question}
              </span>
            </div>
            <span className="text-[14px]">
              <Image
                src={openIndex === index ? uparrow : downarrow}
                alt="arrow"
                width={14}
                height={14}
              />
            </span>
          </button>
          {openIndex === index && (
            <div className="mb-2 mx-14 text-[10px] md:text-[13px] lg:text-sm text-gray-700">
              {faq.answer}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default Payment;
