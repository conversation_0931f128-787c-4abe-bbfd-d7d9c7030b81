import back from '@/public/icons/back.png';
import card from '@/public/icons/card2.png';
import phone from '@/public/icons/phone.png';
import product from '@/public/icons/product.png';
import store from '@/public/icons/store.png';
import truck from '@/public/icons/truck.png';
import Image from 'next/image';
import { Dispatch, SetStateAction } from 'react';

// Define the type for the activeSection
interface SidebarProps {
  activeSection: string;
  setActiveSection: Dispatch<SetStateAction<string>>;
}

const Sidebar = ({ activeSection, setActiveSection }: SidebarProps) => {
  // Define the sections in an array
  const sections = [
    { name: 'Payment', icon: card, label: 'Payment' },
    { name: 'Delivery', icon: truck, label: 'Delivery' },
    { name: 'Products', icon: product, label: 'Products' },
    { name: 'Returns & Refunds', icon: back, label: 'Returns & Refunds' },
    { name: 'Sell on Ewe<PERSON>', icon: store, label: 'Sell on <PERSON><PERSON><PERSON>' },
    { name: 'Call Us', icon: phone, label: 'Call Us' },
  ];

  return (
    <div className="">
      <nav className="w-52 rounded-lg  mt-4  border bg-white">
        <ul className="space-y-2">
          {sections.map(section => (
            <li
              key={section.name}
              onClick={() => setActiveSection(section.name)}
              className={`cursor-pointer border-b py-3 px-4 ${
                activeSection === section.name ? ' text-eweko_green_dark' : ''
              }`}
            >
              <button
                aria-label={section.label} // Semantic button for accessibility
                className="flex items-center text-[12px] md:[15px] md3:text-[16px]"
              >
                <Image
                  src={section.icon}
                  alt={section.label}
                  width={15} // Set desired width
                  height={15} // Set desired height
                />
                <span className="ml-2">{section.label}</span>
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar;
