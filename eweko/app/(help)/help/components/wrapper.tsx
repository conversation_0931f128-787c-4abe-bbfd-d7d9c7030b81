'use client';

import { useEffect, useState } from 'react';
import { FullscreenLoader } from '../../../components/FullscreenLoader';
import { IsContentReady } from '../../../components/IsContentReady';
import Payment from '../components/payment';
import Sidebar from '../components/sidebar';
export const Wrapper = () => {
  const [mounted, setMounted] = useState(false);
  const [activeSection, setActiveSection] = useState('Payment'); // State for active content

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  const renderContent = () => {
    switch (activeSection) {
      case 'Payment':
        return <Payment />;
    }
  };

  if (!mounted) return <FullscreenLoader />;

  return (
    <div className="w-full flex flex-col gap-12">
      <div className="w-full bg-gray-50 h-screen p-[20px] md:p-[30px] xl:p-[40px]  ">
        <IsContentReady />
        <div className="">
          {' '}
          <h1 className="font-bold text-[17px] md:text-[20px] lg:text-[23px] text-black ">
            Help Center
          </h1>
          <p className=" text-[12px] md:text-[14px] lg:text-[16px] text-black ">
            Hi, how can we help you?
          </p>
        </div>
        <div className="mt-8 flex flex-col md:flex-row gap-6">
          <Sidebar
            activeSection={activeSection}
            setActiveSection={setActiveSection}
          />

          <div className="flex-1 border  bg-white  rounded-lg md3:m-4">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};
