'use client';

import React, { useEffect, useState } from 'react';
import { useGlobalState } from '../../../globalStore';
import { Header } from './components/header';

const Layout = ({ children }: { children: React.ReactNode }) => {
  

  return (
    <div className="min-h-screen bg-[#f9f9f9] relative overflow-hidden font-jsregular">
      <Header />
      <main className={`w-full h-full pt-[111px] transition-all duration-300`}>
        {children}
      </main>
    </div>
  );
};

export default Layout;
