'use client';

import Image from 'next/image';
import { useEffect } from 'react';

import { rootNavItems } from '@/constants';
import { useGlobalState } from '@/globalStore';
import ewekologo from '@/public/ewekologo.png';
import Link from 'next/link';

import { LuMenu } from 'react-icons/lu';
import { MdClose } from 'react-icons/md';

export const Header = () => {
  const { mobileNav, toggleMobileNav } = useGlobalState();

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 640) {
        toggleMobileNav(false);
      }
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <header className="w-full bg-white text-eweko_green_dark">
      <div className="w-[90%] md:w-[85%] mx-auto flex justify-between items-center py-3 text-center transition-all duration-500">
        <Link rel="prefetch" href="/">
          <Image
            src={ewekologo}
            alt="EwekoAggregate"
            className="w-[150px] max-h-auto object-contain"
            loading="eager"
          />
        </Link>

        <nav className="hidden sm3:flex items-center">
          {rootNavItems.map((item, i) => (
            <Link
              key={i}
              href={item.url}
              className="px-4 md:px-6 py-3 uppercase font-jssembold transition-all duration-500 text-sm"
            >
              {item.name}
            </Link>
          ))}
        </nav>
        <span className="flex sm3:hidden text-eweko_green cursor-pointer transition-all duration-500">
          {mobileNav ? (
            <MdClose size={23} onClick={() => toggleMobileNav(false)} />
          ) : (
            <LuMenu size={23} onClick={() => toggleMobileNav(true)} />
          )}
        </span>
      </div>
    </header>
  );
};
