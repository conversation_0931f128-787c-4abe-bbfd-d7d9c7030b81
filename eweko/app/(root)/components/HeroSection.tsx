"use client";

import Image from "next/image";
import React, { useRef } from "react";
import smockup from "@/public/sign-up-mockup.png";
import { motion, useInView } from "framer-motion";
import Link from "next/link";

export const HeroSection = () => {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { once: true });

  const direction = "left";

  const slideVariants = {
    hidden: {
      x: direction === "left" ? "-50%" : "50%",
      opacity: 0,
    },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <div className="w-[90%] md:w-[85%] mx-auto flex flex-col transition-all duration-500">
      <div className="flex flex-col lg:flex-row justify-center lg:justify-between items-center py-20 lg:py-28 gap-12">
        <div className="w-[90%] lg:w-[47%]">
          <motion.div
            ref={ref}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={slideVariants}
            className=""
          >
            <div className="flex flex-col gap-6">
              <div className="text-2xl sm:text-3xl sm3:text-4xl md2:text-5xl lg:text-3xl xl:text-4xl 2xl:text-5xl 3xl:text-6xl font-bold">
                <h1 className="leading-[40px] sm3:leading-[50px] md2:leading-[65px] lg:leading-[50px] xl:leading-[60px] 2xl:leading-[70px] 3xl:leading-[80px]">
                  Transforming{" "}
                  <span className="text-eweko_green_light">
                    Fresh <br />
                    Produce
                  </span>{" "}
                  supply chains,
                  <br />
                  one customer at a time.
                </h1>
              </div>

              <h3 className="text-lg sm3:text-xl md2:text-2xl text-black">
                Connecting PEOPLE, PROCESS & PLATFORM.
              </h3>
              <p className=" text-black font-medium leading-[30px]">
                <span className="font-bold text-black">EwekoAggregate</span> is
                a B2B Digital Platform that leverages{" "}
                <br className="hidden md:hidden" />
                technology to deliver fresh produce on time, consistently and in
                the right quality.
              </p>

              <Link
                className="rounded-full px-12 py-4 hover:text-eweko_green_light bg-eweko_green_light text-black font-bold block w-fit hover:bg-black transition-all duration-500"
                href="/auth"
              >
                Get Started
              </Link>
            </div>
          </motion.div>
        </div>

        <div className="w-[90%] lg:w-[48%] xl:w-[45%]">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }} // Start with opacity 0 and 80% size
            animate={{ opacity: 1, scale: 1 }} // Transition to full opacity and 100% size
            transition={{
              duration: 0.8, // Total duration of the animation
              ease: "easeOut", // Smooth transition with ease-out
            }}
            className="rounded-lg bg-transparent "
          >
            <Image
              src={smockup}
              alt="EwekoAggregate"
              className="w-full max-h-auto"
            />
          </motion.div>
        </div>
      </div>
    </div>
  );
};
