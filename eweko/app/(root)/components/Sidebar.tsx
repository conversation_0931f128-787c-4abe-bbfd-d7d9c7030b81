"use client";

import React, { useRef, useEffect } from "react";
import { rootNavItems } from "@/constants";
import { useGlobalState } from "@/globalStore";
import Link from "next/link";
import { useAnimate, motion, AnimatePresence } from "framer-motion";
import useClickOutside from "@/hooks/use-click-outside";

export const Sidebar = () => {
  const { mobileNav, toggleMobileNav } = useGlobalState();

  const [scope, animate] = useAnimate();
  const handleOutsideClick = () => {
    toggleMobileNav(false);
  };

  useClickOutside(scope, handleOutsideClick);

  useEffect(() => {
    animate(scope.current, {
      right: mobileNav ? "0" : "-100%",
    });
  }, [mobileNav, animate]);

  const navItemVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: (i: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: i * 0.3, // Stagger animation by index
        duration: 0.5,
        ease: "easeOut",
      },
    }),
  };

  return (
    <div
      className="w-[180px] p-0 text-left bg-eweko_green_dark text-white absolute top-[85px] right-[-100%] flex py-5 z-50"
      ref={scope}
    >
      <nav className="flex flex-col items-left w-full  gap-4  ">
        <AnimatePresence>
          {mobileNav &&
            rootNavItems.map((item, i) => (
              <motion.div
                key={item.name}
                custom={i} // Pass index to variants
                initial="hidden"
                animate="visible"
                exit="hidden"
                variants={navItemVariants}
                className="px-0 py-1  "
              >
                <Link
                rel='prefetch'
                  href={item.url}
                  className="px-0  ml-8 w-full hover:text-eweko_green_light  text-left  md:px-6 py-3  uppercase font-jssembold transition-all duration-500 text-sm"
                >
                  {item.name}
                </Link>
                {i !== rootNavItems.length - 1 && (
                  <div className="w-full h-0.5 mt-5 bg-lime-800 la"></div>
                )}{" "}
              </motion.div>
            ))}
        </AnimatePresence>
      </nav>
    </div>
  );
};
