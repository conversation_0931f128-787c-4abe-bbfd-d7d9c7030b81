"use client";

import { motion } from "framer-motion";
import Image, { StaticImageData } from "next/image";

interface PopUpImageProps {
  src: string | StaticImageData; // Allow both URL strings and StaticImageData
  alt: string;
}

const PopUpImage: React.FC<PopUpImageProps> = ({ src, alt }) => {
  return (
    <div className="flex   ">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }} // Start with opacity 0 and 80% size
        animate={{ opacity: 1, scale: 1 }} // Transition to full opacity and 100% size
        transition={{
          duration: 0.8, // Total duration of the animation
          ease: "easeOut", // Smooth transition with ease-out
        }}
        className="rounded-lg bg-transparent "
      >
        <Image
          src={src}
          alt={alt}
          width={600}
          height={40}
          className="w-full sm:w-90% sm3:w-90% md2:w-[700px] xl:w-[1100px] xl:mt-[-100px] lg:mt-0"
        />
      </motion.div>
    </div>
  );
};

export default PopUpImage;
