"use client";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules"; // Correct module imports for Swiper v8+

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import aws from "@/public/aws-logo.jpg";
import colead from "@/public/colead.jpeg";
import ecotutu from "@/public/ecotutu.jpg";
import horti from "@/public/hoorti-logo.jpg";
import icra from "@/public/icra.jpg";
import ignitia from "@/public/ignitia.jpg";
import kamim from "@/public/kamim-tech.jpg";
import lotus from "@/public/lotus-beta.jpg";
import microsoft from "@/public/microsoft-2.png";
import pricepally from "@/public/pricepally.jpg";

const Slider = () => {
  const slides = [
    {
      image: aws,
      title: "AWS",
      description: "Amazon Web Services",
    },
    {
      image: colead,
      title: "Slide 2",
      description: "Description 2",
    },
    {
      image: ecotutu,
      title: "Slide 3",
      description: "Description 3",
    },
    {
      image: horti,
      title: "Slide 10",
      description: "Description 3",
    },
    {
      image: icra,
      title: "Slide 4",
      description: "Description 3",
    },
    {
      image: ignitia,
      title: "Slide 5",
      description: "Description 3",
    },
    {
      image: kamim,
      title: "Slide 6",
      description: "Description 1",
    },
    {
      image: lotus,
      title: "Slide 7",
      description: "Description 2",
    },
    {
      image: microsoft,
      title: "Slide 8",
      description: "Description 3",
    },
    {
      image: pricepally,
      title: "Slide 9",
      description: "Description 3",
    },
  ];

  return (
    <div className=" ssm:w-[300px] sm:w-[300px] sm2:w-full max-w-7xl justify-center items-center  mx-auto pb-10">
      <h1 className=" text-center text-xl ssm:text-2xl sm:text-3xl lg:text-3xl font-bold text-lime-600 mb-4 ">
        Our Partners
      </h1>

      <Swiper
        modules={[Autoplay, Navigation, Pagination]}
        spaceBetween={20}
        slidesPerView={1}
        autoplay={{
          delay: 2000,
          disableOnInteraction: false,
        }}
        breakpoints={{
          // Define breakpoints for responsiveness
          300: {
            slidesPerView: 1, // Small screens (up to 640px)
          },
          568: {
            slidesPerView: 2, // Medium screens (568px to 767px)
          },
          768: {
            slidesPerView: 3, // Tablets (768px and above)
          },
          1000: {
            slidesPerView: 4, // Desktops (1024px and above)
          },
          1280: {
            slidesPerView: 5, // Large screens (1280px and above)
          },
        }}
        // navigation
        // pagination={{ clickable: true }}
        loop={true}
        grabCursor={true}
        speed={1000} // Smooth transition duration (ms)
        className="rounded-lg overflow-hidden"
      >
        {slides.map((slide, index) => (
          <SwiperSlide key={index}>
            <Image
              src={slide.image}
              alt={`Slide ${index + 1}`}
              className="w-[300px] h-[100px]   object-cover"
              loading="eager"
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default Slider;
