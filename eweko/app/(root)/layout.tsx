import React from 'react';
import { Footer } from './components/Footer';
import { Header } from './components/Header';
import { Sidebar } from './components/Sidebar';

const Layout = async ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex flex-col transition-all overflow-x-hidden duration-500 relative">
      <Sidebar />
      <Header />
      <main className="w-full flex-1 bg-white">{children}</main>
      <Footer />
    </div>
  );
};
export default Layout;
