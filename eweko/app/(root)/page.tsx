import pic2 from '@/public/pic-2.png';
import pic3 from '@/public/pic-3.png';
import pic1 from '@/public/pic1.png';
import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import SlideInText from './components/textlide';
import { HeroSection } from './components/HeroSection';
import Slider from './components/slider';

export const metadata: Metadata = {
  title:
    "EwekoAggregate | Nigeria's B2B Digital Platform that leverages technology to deliver fresh produce on time, consistently and in the right quality",
  description:
    "Nigeria's B2B Digital Platform that leverages technology to deliver fresh produce on time, consistently and in the right quality",
};

const Home = async () => {
  return (
    <div className="w-full flex flex-col mb-10 transition-all duration-500">
      {/* Hero Section */}

      <HeroSection />

      {/* Quality Section */}
      <section className="w-full bg-lime-100 py-16 transition-all duration-500">
        <article className="w-[90%] md:w-[85%] mx-auto flex flex-col lg:flex-row justify-center lg:justify-between items-center py-16 lg:py-20 gap-10">
          <div className="w-[90%] lg:w-[48%] xl:w-[45%]">
            <Image
              src={pic1}
              className="w-full max-h-auto"
              alt="Farmer at Eweko"
              loading="eager"
            />
          </div>

          <div className="w-[90%] lg:w-[48%] xl:w-[45%] flex flex-col gap-6">
            <SlideInText
              text={
                <div className="text-2xl sm:text-3xl sm3:text-4xl lg:text-3xl xl:text-4xl 2xl:text-5xl font-bold text-center lg:text-left">
                  <h1 className="leading-[40px] sm3:leading-[50px] md2:leading-[65px] lg:leading-[50px] xl:leading-[60px]">
                    Quality, Safe and{' '}
                    <span className="text-eweko_green_light">
                      Nutritious Produce
                    </span>
                  </h1>
                </div>
              }
              direction="right"
            />

            <SlideInText
              text={
                <div className="text-center lg:text-left flex flex-col justify-center lg:justify-start items-center lg:items-start gap-6 leading-[35px]">
                  <p className="text-gray-700">
                    At <span className="text-black">EwekoAggregate</span>, we
                    offer quality produce by implementing Food Safety and
                    Quality Management Systems at the first mile.
                  </p>
                  <p className="text-gray-700">
                    Inclusive agribusiness approaches improve the livelihoods of
                    farmers and value chain actors, especially in rural areas.
                  </p>
                  <Link
                    rel="prefetch"
                    className="rounded-full px-12 py-4 bg-eweko_green_light text-black font-bold block w-fit hover:bg-black hover:text-eweko_green_light transition-all duration-500"
                    href="/auth/buyers"
                  >
                    Buy Now
                  </Link>
                </div>
              }
              direction="right"
            />
          </div>
        </article>
      </section>

      {/* Agribusiness Support Section */}
      <section className="w-full bg-white py-16 transition-all duration-500">
        <article className="w-[90%] md:w-[85%] mx-auto flex flex-col lg:flex-row-reverse justify-center lg:justify-between items-center py-16 lg:py-20 gap-10">
          <div className="w-[90%] lg:w-[48%] xl:w-[45%]">
            <Image
              src={pic2}
              width={500}
              height={300}
              className="w-full max-h-auto"
              alt="Agribusiness Support"
              loading="eager"
            />
          </div>
          <div className="w-[90%] lg:w-[48%] xl:w-[45%] flex flex-col gap-6">
            <SlideInText
              text={
                <div className="text-2xl sm:text-3xl sm3:text-4xl lg:text-3xl xl:text-4xl 2xl:text-5xl font-bold text-center lg:text-left">
                  <h1 className="leading-[40px] sm3:leading-[50px] md2:leading-[65px] lg:leading-[50px] xl:leading-[60px]">
                    Tailored Agribusiness Support Services{' '}
                    <span className="text-eweko_green_light">for Farmers</span>
                  </h1>
                </div>
              }
              direction="left"
            />
            <SlideInText
              text={
                <div className="text-center lg:text-left flex flex-col justify-center lg:justify-start items-center lg:items-start gap-6 leading-[35px]">
                  <p className="text-gray-700">
                    Aligning buyers’ requests with farmers’ production to
                    promote efficiency and reduce post-harvest losses.
                  </p>
                  <p className="text-gray-700 ">
                    Farmers can join{' '}
                    <span className="text-black">EwekoAggregate</span> for
                    agribusiness support services such as technical support,
                    weather advisory, market intelligence, and extension
                    services.
                  </p>
                  <Link
                    rel="prefetch"
                    className="rounded-full px-12 py-4 hover:text-eweko_green_light bg-eweko_green_light text-black font-bold block w-fit hover:bg-black transition-all duration-500"
                    href="/auth/farmers"
                  >
                    Join Us
                  </Link>
                </div>
              }
              direction="left"
            />
          </div>
        </article>
      </section>

      {/* Fresh Supply Section */}
      <section className="w-full bg-lime-100 py-16 transition-all duration-500">
        <article className="w-[90%] md:w-[85%] mx-auto flex flex-col lg:flex-row justify-center lg:justify-between items-center py-16 lg:py-20 gap-10">
          <div className="w-[90%] lg:w-[48%] xl:w-[45%]">
            <Image
              src={pic3}
              className="w-full max-h-auto"
              alt="Fresh Supply"
              loading="eager"
            />
          </div>
          <div className="w-[90%] lg:w-[48%] xl:w-[45%] flex flex-col gap-6">
            <SlideInText
              text={
                <div className="text-2xl sm:text-3xl sm3:text-4xl lg:text-3xl xl:text-4xl 2xl:text-5xl font-bold text-center lg:text-left">
                  <h1 className="leading-[40px] sm3:leading-[50px] md2:leading-[65px] lg:leading-[50px] xl:leading-[60px]">
                    Fresh Supply Delivered <br /> When You Need It
                  </h1>
                </div>
              }
              direction="right"
            />
            <SlideInText
              text={
                <div className="text-center lg:text-left flex flex-col justify-center lg:justify-start items-center lg:items-start gap-6 leading-[35px]">
                  <p className="text-gray-700">
                    We facilitate the delivery of aggregated fresh produce to
                    your location with tracking to monitor its flow from
                    aggregation centres to your destination.
                  </p>
                  <Link
                    rel="prefetch"
                    className="rounded-full px-12 py-4 hover:text-eweko_green_light bg-eweko_green_light text-black font-bold block w-fit hover:bg-black transition-all duration-500"
                    href="/auth/buyers"
                  >
                    Join Us
                  </Link>
                </div>
              }
              direction="right"
            />
          </div>
        </article>
      </section>

      {/* Trust Section */}
      <section className="bg-white text-center py-16 gap-2 mb-16">
        <h1 className="text-2xl ssm:text-3xl lg:text-4xl font-bold text-eweko_green_light mb-10 ">
          We are building TRUST within the Value Chain.
        </h1>

        <Link
          rel="prefetch"
          className="px-12 py-4 hover:text-eweko_green_light   bg-eweko_green_light text-black bg-eweko_gtext-eweko_green_light  rounded-full  hover:bg-black  p-2 w-fit font-bold transition-all duration-500"
          href="/auth"
        >
          Sign Up
        </Link>
      </section>
      {/* Slider Section */}

      <Slider />
    </div>
  );
};

export default Home;
