'use client';

import help from '@/public/icons/help.png';
import logout from '@/public/icons/logout.png';
import setting from '@/public/icons/setting.png';
import { useQuery } from '@tanstack/react-query';
import Cookies from 'js-cookie';
import debounce from 'lodash.debounce';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { IoChevronDownSharp } from 'react-icons/io5';
import { MdOutlineShoppingCart } from 'react-icons/md';
import { RiSearchLine } from 'react-icons/ri';
import { CartItem } from '../(buyers)/buyers/types';
import { Avatar, AvatarFallback } from '../../components/ui/avatar';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/ui/dropdown-menu';
import { Input } from '../../components/ui/input';
import { useGlobalState } from '../../globalStore';
import Notifications from './NotificationDropdown';
import { DashboardSearch } from './DashboardSearch';

export const DashboardHeaderIcons = () => {
  const { setAuthToken, setLoggedInUser, loggedInUser, cart } =
    useGlobalState();

  const [avatarImg, setAvatarImg] = useState<string | null>(
    'https://t4.ftcdn.net/jpg/06/30/06/81/360_F_630068155_RnZI6mC91wz7gUYFVmhzwpl4O6x00Cbh.jpg'
  );

  const router = useRouter();

  useEffect(() => {
    if (loggedInUser && loggedInUser.profilePicture) {
      setAvatarImg(loggedInUser.profilePicture); // Set the profile picture
    }
  }, [loggedInUser]);

  return (
    <div className="w-fit flex items-center gap-4 md:gap-6">
      <Notifications />

      <Link
        rel="prefetch"
        href="/buyers/cart"
        className="rounded-full w-[46px] h-[46px] flex items-center justify-center bg-[#fcfcfc] border-[1px] border-[#f2f2f2] cursor-pointer hover:text-eweko_green_light hover:border-eweko_green_light transition-all duration-300 relative"
      >
        <MdOutlineShoppingCart size={20} />

        {cart && cart.items.length > 0 && (
          <>
            {cart.items.length !== 0 && (
              <output className="bg-eweko_green text-white w-[18px] h-[18px] rounded-full flex items-center justify-center text-[10px] font-bold absolute top-[0px] right-[0px]">
                {cart.items.length}
              </output>
            )}
          </>
        )}
      </Link>

      <div className="relative">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger className="focus-visible:ring-0 focus-within:ring-0 focus-within:border-none focus-within:outline-none">
            <span className="flex items-center gap-1 cursor-pointer">
              {avatarImg ? (
                <span className="object-contain w-[46px] h-[46px] hover:text-eweko_green_light cursor-pointer border-[1px] border-transparent hover:border-eweko_green_light transition-all duration-300 rounded-full hover:p-[2px] flex items-center justify-center">
                  <Image
                    src={avatarImg}
                    alt="Profile"
                    width={46}
                    height={46}
                    loading="eager"
                    className="rounded-full object-cover w-[46px] h-[46px]"
                  />
                </span>
              ) : (
                // Fallback avatar if no image is available
                <Avatar className="border-[1px] border-transparent hover:border-eweko_green_light transition-all duration-300 rounded-full p-1 flex items-center justify-center hover:text-eweko_green_light cursor-pointer hover:p-[2px]">
                  <AvatarFallback className="bg-eweko_green_light text-white">
                    E
                  </AvatarFallback>
                </Avatar>
              )}
              {/* Dropdown arrow */}
              <IoChevronDownSharp
                size={18}
                className="text-eweko_green_light"
              />
            </span>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="absolute mt-10  -right-[50px] w-[180px]">
            <DropdownMenuItem className="focus:text-eweko_green_light focus:bg-transparent border-b">
              <Link
                rel="prefetch"
                href="/buyers/settings"
                className="w-full flex justify-between"
              >
                Settings
                <span>
                  <Image src={setting} width={16} height={16} alt="settings" />
                </span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem className="focus:text-eweko_green_light focus:bg-transparent border-b  ">
              <Link
                rel="prefetch"
                href="/help"
                className="w-full flex justify-between "
              >
                Help
                <span>
                  <Image src={help} width={16} height={16} alt="settings" />
                </span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem className="focus:text-eweko_green_light focus:bg-transparent">
              <span
                onClick={() => {
                  setAuthToken(null);
                  setLoggedInUser(null);
                  Cookies.remove('3wkAt');
                  router.push('/');
                }}
                className="w-full flex justify-between"
              >
                Logout
                <span>
                  <Image src={logout} width={16} height={16} alt="settings" />
                </span>
              </span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};
