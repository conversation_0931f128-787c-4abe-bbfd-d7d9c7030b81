'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useCallback, useEffect, useState } from 'react';
import { RiSearchLine } from 'react-icons/ri';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../components/ui/dialog';
import { Input } from '../../components/ui/input';
import { usePathname, useRouter } from 'next/navigation';
import { AllRoutes } from '../../routes';
import { useSafeQuery } from '../../axios/query-client';
import { IProduce } from '../(buyers)/buyers/types';
import debounce from 'lodash.debounce';
import { InlineLoader } from './InlineLoader';

interface ProduceData {
  data: IProduce[];
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export const DashboardSearch = () => {
  const router = useRouter();
  const path = usePathname();

  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [url, setUrl] = useState(AllRoutes.produce);

  // Create a debounced search function to avoid excessive API calls
  const debouncedSearch = useCallback(
    debounce((term: string) => {
      setSearchTerm(term);
    }, 300),
    []
  );

  // Update URL when search term changes
  useEffect(() => {
    if (searchTerm) {
      setUrl(`${AllRoutes.produce}?search=${encodeURIComponent(searchTerm)}`);
    } else {
      setUrl(AllRoutes.produce);
    }
  }, [searchTerm]);

  useEffect(() => {
    const isFarmer = path.includes('/farmers');

    if (searchTerm) {
      const encoded = encodeURIComponent(searchTerm);
      const farmerSearchUrl = `${AllRoutes.produce}/farmer?search=${encoded}`;
      const buyerSearchUrl = `${AllRoutes.produce}?search=${encoded}`;

      setUrl(isFarmer ? farmerSearchUrl : buyerSearchUrl);
    } else {
      setUrl(
        path.includes('/farmers')
          ? `${AllRoutes.produce}/farmer`
          : AllRoutes.produce
      );
    }
  }, [searchTerm, path]);

  // The main query using useSafeQuery
  const { data, isLoading, isSuccess, isError, error, refetch, isFetching } =
    useSafeQuery<ProduceData>(['produces', searchTerm], url, {
      staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes
      enabled: !!url, // Only run query when URL is set
    });

  // Navigate to produce detail and close dialog
  const handleProduceClick = (id: string) => {
    setIsDialogOpen(false);
    router.push(`${path}/produce/${id}`);
  };

  const searchResults = data?.data || [];

  return (
    <div className="flex w-[46px] md2:w-[25%] mr-4 md2:mr-0 transition-all duration-300">
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <div className="relative md2:bg-[#f2f2f2] rounded-full md2:rounded-[12px] md2:w-full w-[46px] h-[46px] group cursor-pointer bg-[#fcfcfc] border-[1px] border-[#f2f2f2] md2:border-none transition-all duration-300">
            <RiSearchLine
              size={18}
              className="absolute md2:left-4 left-3 top-1/2 -translate-y-1/2 text-[#676767]"
            />
            <Input
              type="search"
              placeholder="Search Produce..."
              className="hidden md2:flex w-full h-full pl-[43px] pr-[20px] rounded-[12px] outline-none border-none bg-transparent focus-visible:ring-eweko_green_light cursor-pointer transition-all duration-300"
              onChange={e => {
                debouncedSearch(e.target.value);
                if (!isDialogOpen) setIsDialogOpen(true);
              }}
              onClick={() => setIsDialogOpen(true)}
            />
          </div>
        </DialogTrigger>
        <DialogContent className="w-[90%] max-h-[90%] overflow-y-auto transition-all duration-300">
          <DialogHeader>
            <DialogTitle className="flex flex-col gap-2">
              <div className="flex items-center">
                <RiSearchLine />
                <Input
                  type="text"
                  placeholder="Search Produce..."
                  className="w-full h-full px-3 rounded-[12px] outline-none border-none bg-transparent focus-visible:ring-transparent transition-all duration-300 font-normal tracking-wide"
                  value={searchTerm}
                  onChange={e => debouncedSearch(e.target.value)}
                  autoFocus
                />
              </div>
              <hr />
            </DialogTitle>
            <DialogDescription>Search produce</DialogDescription>
          </DialogHeader>

          {isLoading && !data && (
            <div className="w-full my-16 flex items-center justify-center">
              <InlineLoader color="eweko_green" size="60" textSize="20" />
            </div>
          )}

          {!isLoading && isError && (
            <div className="text-center text-eweko_red p-4">
              <p>Error loading results. Please try again.</p>
              <button
                onClick={() => refetch()}
                className="mt-2 px-4 py-2 bg-eweko_green text-white rounded-md"
              >
                Retry
              </button>
            </div>
          )}

          {isSuccess && (
            <div className="w-full flex flex-col gap-2 justify-center items-center transition-all duration-300">
              {searchResults.length > 0 ? (
                searchResults.map(item => (
                  <div
                    key={item.id}
                    className="flex items-center gap-4 w-full p-4 border-b border-gray-200 transition-all duration-300 hover:bg-eweko_green_light/40 hover:rounded group cursor-pointer"
                    onClick={() => handleProduceClick(item.id)}
                  >
                    <Image
                      src={item.images[0]}
                      alt={item.name}
                      width={56}
                      height={56}
                      className="w-14 h-14 object-cover rounded"
                    />
                    <div>
                      <h3 className="font-semibold text-eweko_green group-hover:text-black">
                        {item.name} |{' '}
                        <span className="text-[14px]">
                          N{item.price.toLocaleString('en-GB')}
                        </span>
                      </h3>
                      <p className="text-sm text-gray-500">
                        {item.category.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        Available: {item.stock}Kg | Min Order:{' '}
                        {item.minOrderQty}Kg
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-eweko_red p-4">No produce found</p>
              )}

              {isFetching && (
                <div className="w-full text-center py-2">
                  <span className="text-sm text-gray-500">
                    Loading more results...
                  </span>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
