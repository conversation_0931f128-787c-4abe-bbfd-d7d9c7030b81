'use client';

import help from '@/public/icons/help.png';
import logout from '@/public/icons/logout.png';
import setting from '@/public/icons/setting.png';
import Cookies from 'js-cookie';
import Image from 'next/image';
import Link from 'next/link';
import { redirect, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { IoChevronDownSharp } from 'react-icons/io5';
import { MdOutlineShoppingCart } from 'react-icons/md';
import { RiSearchLine } from 'react-icons/ri';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '../../components/ui/avatar';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/ui/dropdown-menu';
import { Input } from '../../components/ui/input';
import { produce } from '../../constants';
import { useGlobalState } from '../../globalStore';
import Notifications from './NotificationDropdown';

export const FarmerHeader = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState(produce);
  const { setAuthToken, setLoggedInUser, loggedInUser } = useGlobalState();
  const [avatarImg, setAvatarImg] = useState<string | null>(
    'https://t4.ftcdn.net/jpg/06/30/06/81/360_F_630068155_RnZI6mC91wz7gUYFVmhzwpl4O6x00Cbh.jpg'
  );
  const router = useRouter();


  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (term.trim() === '') {
      setSearchResults(produce); // Show all produce if input is empty
    } else {
      const filtered = produce.filter(item =>
        item.name.toLowerCase().includes(term.toLowerCase())
      );
      setSearchResults(filtered);
    }
  };

  useEffect(() => {
    if (loggedInUser && loggedInUser.profilePicture) {
      setAvatarImg(loggedInUser.profilePicture); // Set the profile picture
    }
  }, [loggedInUser]);

  return (
    <div className="w-fit flex items-center gap-4 md:gap-6">
      <Notifications />
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger className="focus-visible:ring-0 focus-within:ring-0 focus-within:border-none focus-within:outline-none">
          <span className="flex items-center gap-1 cursor-pointer] ">
            {avatarImg ? (
              <span className="object-cover w-[46px] h-[46px] hover:text-eweko_green_light cursor-pointer border-[1px] border-transparent hover:border-eweko_green_light transition-all duration-300 rounded-full hover:p-[2px] flex items-center justify-center">
                <Image
                  src={avatarImg}
                  alt=""
                  width={46}
                  height={46}
                  loading="eager"
                  className="rounded-full object-cover w-[46px] h-[46px]"
                />
              </span>
            ) : (
              <Avatar className="border-[1px] border-transparent hover:border-eweko_green_light transition-all duration-300 rounded-full p-1 flex items-center justify-center hover:text-eweko_green_light cursor-pointer hover:p-[2px] ">
                <AvatarImage src="" />
                <AvatarFallback className="bg-eweko_green_light text-white">
                  E
                </AvatarFallback>
              </Avatar>
            )}

            <IoChevronDownSharp size={18} className="text-eweko_green_light" />
          </span>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="absolute mt-10 -right-[50px] w-[180px]">
          <DropdownMenuItem className="focus:text-eweko_green_light focus:bg-transparent border-b">
            <Link
              rel="prefetch"
              href="/farmers/settings"
              className="w-full flex justify-between"
            >
              Settings
              <span>
                <Image src={setting} width={16} height={16} alt="settings" />
              </span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem className="focus:text-eweko_green_light focus:bg-transparent border-b  ">
            <Link
              rel="prefetch"
              href="/help"
              className="w-full flex justify-between "
            >
              Help
              <span>
                <Image src={help} width={16} height={16} alt="settings" />
              </span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem className="focus:text-eweko_green_light focus:bg-transparent">
            <span
              onClick={() => {
                setAuthToken(null);
                setLoggedInUser(null);
                Cookies.remove('3wkAt');
                router.push('/');
              }}
              className="w-full flex justify-between"
            >
              Logout
              <span>
                <Image src={logout} width={16} height={16} alt="settings" />
              </span>
            </span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
