"use client"

import { useInteractionLock } from '../../hooks/use-interaction-lock';

export const FullscreenLoader = () => {
  useInteractionLock(true);

  return (
    <div className="fixed inset-0 z-95 flex items-center justify-center bg-black/85 pointer-events-none">
      <div className="w-16 h-16 rounded-full relative">
        <div className="w-16 h-16 border-4 border-eweko_green_light border-t-transparent rounded-full animate-spin"></div>
        <span className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 font-bold text-eweko_green_light text-[18px]">
          EA
        </span>
      </div>
    </div>
  );
};
