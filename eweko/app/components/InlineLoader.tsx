import { useInteractionLock } from '../../hooks/use-interaction-lock';

type InlineLoaderProps = {
  color: string;
  size: string;
  textSize: string;
};

export const InlineLoader = ({ color, size, textSize }: InlineLoaderProps) => {
  useInteractionLock(true);

  return (
    <div className={`w-[${size}px] h-[${size}px] rounded-full relative`}>
      <div
        className={`w-[${size}px] h-[${size}px] border-4 border-${color} border-t-transparent rounded-full animate-spin`}
      ></div>
      <span
        className={`absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 font-bold text-${color} text-[${textSize}px]`}
      >
        EA
      </span>
    </div>
  );
};
