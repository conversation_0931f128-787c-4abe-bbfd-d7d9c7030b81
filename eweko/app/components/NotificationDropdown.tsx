'use client';
import chat from '@/public/icons/chat.png';
import { Bell } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import { useGlobalState } from '../../globalStore';
import { AllRoutes } from '../../routes';
import { useSafeMutation, useSafeQuery } from '../../axios/query-client';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'react-toastify';

const NotificationDropdown = () => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { loggedInUser } = useGlobalState(); // Get the logged-in user _ID
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [hoveredNotificationId, setHoveredNotificationId] = useState<
    string | null
  >(null);
  const [notid, setNotid] = useState<string | null>(null);

  // Fetch notifications from the backend
  const user_Id = loggedInUser?.id;
  const userType = loggedInUser?.userType?.toLowerCase() + 's'; 
  
  // Define the notification type
  interface NotificationItem {
    id: string;
    message: string;
    time: Date | null;
    isRead?: boolean;
  }

  // Create a function to process notifications
  const processNotifications = (notificationsData: any[]): NotificationItem[] => {
    if (!notificationsData) return [];
    
    return notificationsData.map((item: any) => {
      // Ensure the date is a valid JavaScript Date object
      let timeValue: Date | null = null;
      try {
        // This will handle both string dates and Date objects
        timeValue = new Date(item.createdAt);

        // Validate the date is valid
        if (isNaN(timeValue.getTime())) {
          console.warn(
            `Invalid date for notification ${item._id}:`,
            item.createdAt,
          );
          timeValue = null;
        }
      } catch (error) {
        console.error(
          `Error parsing date for notification ${item._id}:`,
          error,
        );
        timeValue = null;
      }

      return {
        id: item._id,
        message: item.message,
        time: timeValue,
        isRead: item.isRead || false,
      };
    });
  };

  const { data, isSuccess, refetch, isError } = useSafeQuery<{data: any[]}>(
    ["notifications"],
    user_Id ? `${AllRoutes.notifications}/${user_Id}/unread` : '',
    {
      enabled: !!user_Id,
    },
  );

  // Handle data when it's loaded
  useEffect(() => {
    if (isSuccess && data?.data) {
      const processedNotifications = processNotifications(data.data);
      setNotifications(processedNotifications);
    }
  }, [isSuccess, data]);

  // useEffect(() => {
  //   if (isSuccess || data) {
  //     const fetchNotifications = async () => {
  //       const notifications = await refetch();
  //       if (notifications?.data) {
  // setNotifications(
  //   notifications?.data.data.map((item: any) => ({
  //     id: item._id,
  //     message: item.message,
  //     time: item.created_at,
  //   }))
  // );
  //       }
  //     };

  //     fetchNotifications();
  //   }
  // }, [data, isSuccess]);

  const { mutate } = useSafeMutation<{ data: any }, Error>(
    user_Id ? `${AllRoutes.notifications}/${user_Id}/mark-all` : '',
    "patch",
    {
      onSuccess: (data) => {
        console.log(data);
        // Update the local state directly instead of refetching
        setNotifications(prevNotifications => 
          prevNotifications.map(notification => ({
            ...notification,
            isRead: true
          }))
        );
        toast.success("Notifications marked as read");
      },
      onError: (error: any) => {
        console.error("Error marking notifications as read:", error);
        toast.error("Couldn't mark notifications as read");
      },
    }
  );

  const { mutate: mark } = useSafeMutation<{ data: any }, Error>(
    hoveredNotificationId ? `${AllRoutes.notifications}/${hoveredNotificationId}/mark` : '',
    "patch",
    {
      onSuccess: (data) => {
        console.log(data);
        // Update the local state directly instead of refetching
        setNotifications(prevNotifications => 
          prevNotifications.map(notification => 
            notification.id === hoveredNotificationId 
              ? { ...notification, isRead: true }
              : notification
          )
        );
        setNotid(null);
        setHoveredNotificationId(null);
        toast.success("Notification marked as read");
      },
      onError: (error: any) => {
        console.error("Error marking notification as read:", error);
        toast.error("Couldn't mark notification as read");
      },
    }
  );

  // Mark all as read
  const markAllAsRead = () => {
    mutate();
  };

  useEffect(() => {
    if (notid) {
      mark();
    }
  }, [notid]);

  // Toggle the dropdown
  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Count unread notifications
  // const unreadCount = notifications.filter(n => !n.isRead).length;

  if (isError) {
    return (
      <span
        className="rounded-full w-[46px] h-[46px] flex items-center justify-center bg-[#fcfcfc] border-[1px] border-[#f2f2f2] cursor-pointer hover:text-eweko_green_light hover:border-eweko_green_light transition-all duration-300"
        onClick={() => toggleDropdown()}
        aria-label="Notifications"
      >
        <Bell size={19} />
      </span>
    );
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Notification Icon */}
      <span
        className="rounded-full w-[46px] h-[46px] flex items-center justify-center bg-[#fcfcfc] border-[1px] border-[#f2f2f2] cursor-pointer hover:text-eweko_green_light hover:border-eweko_green_light transition-all duration-300"
        onClick={() => toggleDropdown()}
        aria-label="Notifications"
      >
        <Bell size={19} />
        {/* Notification count if there are unread messages */}
        {notifications.length > 0 && (
          <span className="absolute top-0 right-0 bg-eweko_green_light text-white text-xs font-semibold w-5 h-5 flex items-center justify-center rounded-full">
            {notifications.length}
          </span>
        )}
      </span>
      {/* Dropdown */}
      {dropdownOpen && (
        <div className="absolute -right-[100px] h-auto max-h-[400px] overflow-hidden overflow-y-auto px-4 mt-10 border w-[300px] md:w-[400px] lg:w-[450px] bg-white rounded-lg shadow-lg pb-6">
          <div className="p-4">
            <div className="flex justify-between items-center">
              <h2 className="text-[14px] md:text-[16px] lg:text-lg text-eweko_green_dark">
                Notifications
              </h2>
              <div className="flex items-center space-x-4">
                {notifications.length > 0 && (
                  <button
                    className="text-[11px] md:text-[14px] lg:text-sm text-eweko_green_light hover:text-eweko_green_dark"
                    onClick={() => markAllAsRead()}
                  >
                    Mark All as Read
                  </button>
                )}
                <a
                  href={userType ? `/${userType}/notifications` : "/notifications"}
                  className="text-[11px] md:text-[14px] lg:text-sm text-eweko_green_light hover:text-eweko_green_dark font-medium"
                  onClick={(e) => {
                    e.preventDefault();
                    setDropdownOpen(false);
                    window.location.href = userType ? `/${userType}/notifications` : "/notifications";
                  }}
                >
                  See All
                </a>
              </div>
            </div>
          </div>
          <ul className="divide-y divide-gray-200">
            {notifications.length > 0 ? (
              notifications.map(notification => (
                <li
                  key={notification.id}
                  className={`p-4 flex items-start relative `}
                  onMouseEnter={() => setHoveredNotificationId(notification.id)}
                  onMouseLeave={() => setHoveredNotificationId(null)}
                >
                  {/* Icon */}
                  <span className="mr-3 text-[14px] md:text-[16px] lg:text-xl bg-gray-100 p-2 rounded-full">
                    <Image
                      src={chat}
                      height={16}
                      width={16}
                      alt="notification icon"
                    />
                  </span>
                  {/* Notification Content */}
                  <div className="flex-1">
                    <p className="text-[11px] md:text-[14px] lg:text-sm">
                      {notification.message}
                    </p>
                    <span className="block text-[9px] md:text-[10px] lg:text-[12px] text-right text-gray-400">
                      {notification.time && !isNaN(new Date(notification.time).getTime())
                        ? formatDistanceToNow(new Date(notification.time), { addSuffix: true })
                        : "Unknown time"}
                    </span>
                  </div>
                  {/* Green Dot for Unread */}
                  <span className="ml-2 h-2 w-2 bg-eweko_green_light rounded-full"></span>
                  {/* Action Buttons (Pop-up on hover/click) */}
                  {hoveredNotificationId === notification.id && (
                    <div className="absolute top-2 right-2 p-2 rounded-lg shadow-lg flex bg-eweko_green_light text-white">
                      <button
                        className="text-[11px] md:text-[14px] lg:text-sm "
                        onClick={() => setNotid(notification.id)}
                      >
                        Mark as Read
                      </button>
                    </div>
                  )}
                </li>
              ))
            ) : (
              <li className="p-4 text-center text-gray-500">
                No notifications available.
              </li>
            )}
          </ul>
          

        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
