export interface NotificationHeaderProps {
  title: string;
  showMarkAll?: boolean;
  showSeeAll?: boolean;
  onMarkAll?: () => void;
  onSeeAll?: () => void;
  unreadCount?: number;
  className?: string;
}

export const NotificationHeader = ({
  title = 'Notifications',
  showMarkAll = false,
  showSeeAll = false,
  onMarkAll,
  onSeeAll,
  unreadCount = 0,
  className = '',
}: NotificationHeaderProps) => {
  return (
    <div className={`p-4 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-[14px] md:text-[16px] lg:text-lg text-eweko_green_dark font-medium">
          {title}
          {unreadCount > 0 && (
            <span className="ml-2 bg-eweko_green_light text-white text-xs font-semibold px-2 py-0.5 rounded-full">
              {unreadCount}
            </span>
          )}
        </h2>
        <div className="flex items-center space-x-4">
          {showMarkAll && unreadCount > 0 && (
            <button
              className="text-[11px] md:text-[14px] lg:text-sm text-eweko_green_light hover:text-eweko_green_dark"
              onClick={onMarkAll}
            >
              Mark All as Read
            </button>
          )}
          {showSeeAll && (
            <button
              className="text-[11px] md:text-[14px] lg:text-sm text-eweko_green_light hover:text-eweko_green_dark font-medium"
              onClick={onSeeAll}
            >
              See All
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationHeader;
