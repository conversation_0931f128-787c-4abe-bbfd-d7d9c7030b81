import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import { useState } from 'react';
import chat from '@/public/icons/chat.png';

export interface NotificationItemProps {
  id: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onClick?: () => void;
  onMarkAsRead?: (id: string) => void;
}

export const NotificationItem = ({
  id,
  message,
  isRead,
  createdAt,
  onMouseEnter,
  onMouseLeave,
  onClick,
  onMarkAsRead,
}: NotificationItemProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isButtonHovered, setIsButtonHovered] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
    onMouseEnter?.();
  };

  const handleMouseLeave = () => {
    if (!isButtonHovered) {
      setIsHovered(false);
    }
    onMouseLeave?.();
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); 
    onMarkAsRead?.(id);
  };

  const handleButtonMouseEnter = () => {
    setIsButtonHovered(true);
  };

  const handleButtonMouseLeave = () => {
    setIsButtonHovered(false);
    setIsHovered(false);
  };

  return (
    <li
      className={`p-4 flex items-start relative ${!isRead ? 'bg-eweko_green_light/5' : ''}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
    >
      {/* Unread indicator */}
      {!isRead && (
        <span className="absolute top-4 left-2 w-2 h-2 bg-eweko_green_light rounded-full"></span>
      )}
      
      {/* Icon */}
      <span className="mr-3 text-[14px] md:text-[16px] lg:text-xl bg-gray-100 p-2 rounded-full">
        <Image src={chat} height={16} width={16} alt="notification icon" />
      </span>
      
      {/* Notification Content */}
      <div className="flex-1">
        <p className="text-[11px] md:text-[14px] lg:text-sm">
          {message}
        </p>
        <p className="text-gray-500 text-[10px] md:text-[12px] mt-1">
          {formatDistanceToNow(new Date(createdAt), { addSuffix: true })}
        </p>
      </div>

      {/* Mark as Read Button (appears on hover for unread notifications) */}
      {!isRead && (isHovered || isButtonHovered) && onMarkAsRead && (
        <div 
          className="absolute top-2 right-2 p-2 rounded-lg shadow-lg flex bg-eweko_green_light text-white z-10"
          onMouseEnter={handleButtonMouseEnter}
          onMouseLeave={handleButtonMouseLeave}
        >
          <button
            className="text-[11px] md:text-[14px] lg:text-sm whitespace-nowrap hover:bg-eweko_green_dark px-2 py-1 rounded"
            onClick={handleMarkAsRead}
            type="button"
          >
            Mark as Read
          </button>
        </div>
      )}
    </li>
  );
};

export default NotificationItem;