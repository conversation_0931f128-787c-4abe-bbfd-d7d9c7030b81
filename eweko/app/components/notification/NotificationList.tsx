import { NotificationItem } from './NotificationItem';
import { Notification } from './types';

export interface NotificationListProps {
  notifications: Notification[];
  isLoading?: boolean;
  onNotificationHover?: (id: string) => void;
  onNotificationLeave?: () => void;
  onNotificationClick?: (id: string) => void;
  onMarkAsRead?: (id: string) => void;
  emptyMessage?: string;
}

export const NotificationList = ({
  notifications,
  isLoading = false,
  onNotificationHover,
  onNotificationLeave,
  onNotificationClick,
  onMarkAsRead,
  emptyMessage = 'No notifications available',
}: NotificationListProps) => {
  if (isLoading) {
    return (
      <div className="p-4 text-center">
        <p className="text-gray-500">Loading notifications...</p>
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <div className="p-4 text-center">
        <p className="text-gray-500">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <ul className="divide-y divide-gray-200">
      {notifications.map((notification, index) => (
        <NotificationItem
          key={`${notification.id}-${index}`}
          id={notification.id}
          message={notification.message}
          isRead={notification.isRead}
          createdAt={notification.createdAt}
          onMouseEnter={() => onNotificationHover?.(notification.id)}
          onMouseLeave={onNotificationLeave}
          onClick={() => onNotificationClick?.(notification.id)}
          onMarkAsRead={(id) => onMarkAsRead?.(id)}
        />
      ))}
    </ul>
  );
};

export default NotificationList;
