'use client';

import { useState, useRef, useEffect } from 'react';
import { useGlobalState } from '../../../globalStore';
import { AllRoutes } from '../../../routes';
import { useSafeQuery, useSafeMutation, queryClient } from '../../../axios/query-client';
import { NotificationList } from './NotificationList';
import { Notification as NotificationType } from './types';
import { ChevronDown } from 'lucide-react';
import { toast } from 'react-toastify';

export const NotificationsPage = () => {
  const { loggedInUser } = useGlobalState();
  const [filter, setFilter] = useState('All');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [page, setPage] = useState(1);
  const [notifications, setNotifications] = useState<NotificationType[]>([]);
  const limit = 10;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

 
  interface NotificationsResponse {
    data: NotificationType[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }

  // Get the appropriate API URL based on the selected filter
  const getNotificationsUrl = () => {
    if (!loggedInUser?.id) return '';
    const base = `${AllRoutes.notifications}/${loggedInUser.id}`;

    switch (filter) {
      case 'Unread':
        return `${base}/unread`;
      case 'Read':
        return `${base}/read`;
      default:
        return base;
    }
  };

  // Handle filter change
  const handleFilterChange = (newFilter: string) => {
    setFilter(newFilter);
    setPage(1); // Reset to first page when filter changes
  };

  // Fetch notifications with pagination and filtering
  const { data, isLoading, isError, refetch } = useSafeQuery<NotificationsResponse>(
    ['notifications', filter, page, limit],
    loggedInUser?.id ? `${getNotificationsUrl()}?page=${page}&limit=${limit}` : '',
    {
      enabled: !!loggedInUser?.id,
    },
  );

  // Transform notification data to ensure consistent id field
  const transformNotifications = (notificationsData: any[]): NotificationType[] => {
    if (!notificationsData) return [];

    return notificationsData.map((item: any) => ({
      id: item.id,
      userType: item.userType,
      userId: item.userId,
      subject: item.subject,
      message: item.message,
      trigger: item.trigger,
      isRead: item.isRead,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
    }));
  };

  // Update local state when data changes
  useEffect(() => {
    if (data?.data) {
      const transformedNotifications = transformNotifications(data.data);
      setNotifications(transformedNotifications);
    }
  }, [data]);

  // Mark all notifications as read mutation
  const { mutate: markAllAsRead } = useSafeMutation<{ data: any }, Error>(
    loggedInUser?.id ? `${AllRoutes.notifications}/${loggedInUser.id}/mark-all` : '',
    "patch",
    {
      onSuccess: () => {
        // Update local state for immediate UI feedback
        setNotifications(prevNotifications =>
          prevNotifications.map(notification => ({
            ...notification,
            isRead: true
          }))
        );

        toast.success("All notifications marked as read");

        // Invalidate all notification queries to refetch fresh data
        queryClient.invalidateQueries({
          queryKey: ['notifications'],
          exact: false
        });
      },
      onError: (error: any) => {
        console.error("Error marking all notifications as read:", error);
        toast.error("Couldn't mark all notifications as read");
      },
    },
  );

  // State to track which notification is being marked as read
  const [markingAsReadId, setMarkingAsReadId] = useState<string | null>(null);

  // Mark single notification as read mutation
  const { mutate: markSingleAsRead } = useSafeMutation<{ data: any }, Error>(
    markingAsReadId ? `${AllRoutes.notifications}/${markingAsReadId}/mark` : '',
    "patch",
    {
      onSuccess: () => {
        if (markingAsReadId) {
          // Update local state for immediate UI feedback
          setNotifications(prevNotifications =>
            prevNotifications.map(notification =>
              notification.id === markingAsReadId
                ? { ...notification, isRead: true }
                : notification
            )
          );

          toast.success("Notification marked as read");
          setMarkingAsReadId(null);

          // Invalidate all notification queries to refetch fresh data
          queryClient.invalidateQueries({
            queryKey: ['notifications'],
            exact: false
          });
        }
      },
      onError: (error: any) => {
        console.error("Error marking notification as read:", error);
        toast.error("Couldn't mark notification as read");
        setMarkingAsReadId(null);
      },
    },
  );

  // Trigger the mutation when markingAsReadId changes
  useEffect(() => {
    if (markingAsReadId) {
      markSingleAsRead();
    }
  }, [markingAsReadId, markSingleAsRead]);

  // Handle marking all notifications as read
  const handleMarkAllAsRead = () => {
    if (!loggedInUser?.id) return;
    markAllAsRead();
  };

  // Handle marking single notification as read
  const handleMarkAsRead = (notificationId: string) => {
    if (!loggedInUser?.id) {
      return;
    }

    // Set the notification ID and trigger the mutation
    setMarkingAsReadId(notificationId);
  };

  const totalNotifications = data?.total || 0;
  const hasNextPage = page < (data?.totalPages || 0);
  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (isError) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-red-500">Failed to load notifications. Please try again later.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-eweko_green_dark">
            Notifications
          </h1>

          <div className="flex items-center space-x-4">
            {/* Mark All as Read Button */}
            {unreadCount > 0 && (
              <button
                onClick={handleMarkAllAsRead}
                className="text-[11px] md:text-[14px] lg:text-sm text-eweko_green_light hover:text-eweko_green_dark font-medium"
              >
                Mark All as Read ({unreadCount})
              </button>
            )}

            {/* Filter Dropdown */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex items-center px-3 py-2 text-sm border rounded-md hover:bg-gray-50"
              >
                <span>{filter}</span>
                <ChevronDown className="w-4 h-4 ml-2" />
              </button>

              {isDropdownOpen && (
                <div className="absolute right-0 z-10 w-40 mt-1 bg-white border border-gray-200 rounded-md shadow-lg">
                  {['All', 'Read', 'Unread'].map((item) => (
                    <button
                      key={item}
                      onClick={() => {
                        handleFilterChange(item);
                        setIsDropdownOpen(false);
                      }}
                      className={`block w-full px-4 py-2 text-sm text-left hover:bg-gray-100 ${
                        filter === item ? 'bg-gray-100' : ''
                      }`}
                    >
                      {item}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Show unread count when applicable */}
        {unreadCount > 0 && filter === 'All' && (
          <p className="text-sm text-gray-600 mt-2">
            {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
          </p>
        )}
      </div>

      <div className="flex-1 overflow-y-auto">
        <NotificationList
          notifications={notifications}
          isLoading={isLoading}
          onMarkAsRead={handleMarkAsRead}
          emptyMessage={
            filter === 'All'
              ? 'No notifications yet'
              : filter === 'Read'
              ? 'No read notifications'
              : 'No unread notifications'
          }
        />
      </div>

      {/* Pagination */}
      {totalNotifications > 0 && (
        <div className="px-4 py-3 border-t border-gray-200 flex justify-between items-center">
          <button
            className="px-4 py-2 text-sm text-eweko_green_light hover:text-eweko_green_dark disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={page === 1}
            onClick={() => setPage((p) => Math.max(1, p - 1))}
          >
            Previous
          </button>
          <span className="text-sm text-gray-600">
            Page {page} of {data?.totalPages || 0}
          </span>
          <button
            className="px-4 py-2 text-sm text-eweko_green_light hover:text-eweko_green_dark disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!hasNextPage}
            onClick={() => setPage((p) => p + 1)}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default NotificationsPage;
