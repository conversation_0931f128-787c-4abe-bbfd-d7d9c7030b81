export interface Notification {
  id: string;
  userType: string;
  userId: string;
  subject: string;
  message: string;
  trigger: string;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export type NotificationTab = 'all' | 'unread';

export interface NotificationsResponse {
  data: Notification[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
