export enum AuthType {
  Login = "LOGIN",
  Signup = "SIGNUP",
  Verify = "VERIFY_ACCOUNT",
  ForgotPassword = "FORGOT_PASSWORD",
}

export enum VerificationSteps {
  SelectCVerificationType = "SELECT_VERIFICATION_TYPE",
  EnterOtp = "ENTER_OTP",
}

export enum ForgotPasswordSteps {
  SelectForgotPasswordType = "SELECT_FORGOTPASSWORD_TYPE",
  EnterEmail = "ENTER_EMAIL",
  EnterPhoneNumber = "ENTER_PHONE_NUMBER",
  EnterOtp = "ENTER_OTP",
  ResetPassword = "RESET_PASSWORD",
}
