'use client';

import { QueryClientProvider } from '@tanstack/react-query';
import localFont from 'next/font/local';
import { ToastContainer } from 'react-toastify';
import { queryClient } from '../axios/query-client';
import './globals.css';
import { useVerifyJWT } from '../hooks/use-verify-jwt';

const jsbold = localFont({
  src: './fonts/jsbold.otf',
  variable: '--jsbold',
});

const jsexbold = localFont({
  src: './fonts/jsexbold.otf',
  variable: '--jsexbold',
});

const jsexlight = localFont({
  src: './fonts/jsexlight.otf',
  variable: '--jsexlight',
});

const jslight = localFont({
  src: './fonts/jslight.otf',
  variable: '--jslight',
});

const jsmedium = localFont({
  src: './fonts/jsmedium.otf',
  variable: '--jsmedium',
});

const jsregular = localFont({
  src: './fonts/jsregular.otf',
  variable: '--jsregular',
});

const jssembold = localFont({
  src: './fonts/jssembold.otf',
  variable: '--jssembold',
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const verifyJWT = useVerifyJWT();

  const handleTokenVerification = async () => {
    await verifyJWT();
  };

  handleTokenVerification();

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${jsbold.variable} ${jsexbold.variable} ${jsexlight.variable} ${jslight.variable} ${jsmedium.variable} ${jsregular.variable} ${jssembold.variable} antialiased font-jsregular bg-white min-h-screen transition-all duration-500 text-black max-w-[1700px] mx-auto`}
      >
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
        <ToastContainer />
      </body>
    </html>
  );
}
