import Link from 'next/link';
import { Header } from './(root)/components/Header';
import { Footer } from './(root)/components/Footer';

export default function NotFound() {
  return (
    <div className="flex flex-col transition-all overflow-x-hidden duration-500 relative">
      <Header />
      <main className="w-full flex-1 bg-eweko_green_light z-10">
        <div className="w-[90%] md:w-[85%] mx-auto flex flex-col transition-all duration-500 items-center justify-center py-20 gap-10 min-h-[calc(100vh-150px)]">
          <div className="w-[150px] h-[150px] rounded-full bg-white flex items-center justify-center text-eweko_green_light text-[min(10vw,42px)] font-bold font-jsexbold">
            404
          </div>
          <p className="text-white">Oops! Could not find requested page.</p>
        </div>
      </main>
      <Footer />
    </div>
  );
}
