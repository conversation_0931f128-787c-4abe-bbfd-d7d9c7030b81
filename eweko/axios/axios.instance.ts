import { useGlobalState } from '@/globalStore';
import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import { ExtendedAxiosRequestConfig } from './types';

interface ErrorResponse {
  message: string;
  statusCode?: number;
}

const axiosInstance = axios.create({
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
  withCredentials: true,
});

// Track retry attempts to prevent infinite loops
const requestRetryMap = new Map<string, number>();
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// Helper to generate a unique key for each request
const getRequestKey = (config: AxiosRequestConfig) => {
  return `${config.method}-${config.url}-${JSON.stringify(config.data || {})}`;
};

// Add a request interceptor
axiosInstance.interceptors.request.use(
  async (config: ExtendedAxiosRequestConfig) => {
    // Get current auth token from state
    const authToken = useGlobalState.getState().authToken;

    // Check if token is available
    if (authToken) {
      config.headers.set('Authorization', `Bearer ${authToken}`);
      return config;
    } else {
      // Token is missing - check if we should retry
      const requestKey = getRequestKey(config);
      const retryCount = requestRetryMap.get(requestKey) || 0;

      if (retryCount < MAX_RETRIES) {
        // Increment retry counter
        requestRetryMap.set(requestKey, retryCount + 1);

        // Wait before retrying
        return new Promise(resolve => {
          setTimeout(async () => {
            // Try to get updated token
            const refreshedToken = useGlobalState.getState().authToken;

            if (refreshedToken) {
              config.headers.set('Authorization', `Bearer ${refreshedToken}`);
            }

            resolve(config);
          }, RETRY_DELAY);
        });
      } else {
        // Reset retry counter
        requestRetryMap.delete(requestKey);
        return config;
      }
    }
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor
axiosInstance.interceptors.response.use(
  response => {
    // Clear retry counter on successful request
    const requestKey = getRequestKey(response.config);
    requestRetryMap.delete(requestKey);
    return response;
  },
  async (error: AxiosError<ErrorResponse>) => {
    // Clear retry counter on error
    if (error.config) {
      const requestKey = getRequestKey(error.config);
      requestRetryMap.delete(requestKey);
    }

    if (error.response) {
      const errorMessage =
        error.response?.data?.message || 'An error occurred on the server';
      return Promise.reject({ errorMessage });
    }

    return Promise.reject({
      message: 'Network error or server not reachable',
      originalError: error,
    });
  }
);

export default axiosInstance;
