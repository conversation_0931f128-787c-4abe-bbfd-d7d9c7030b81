import { useState, useEffect } from 'react';
import { FaCross } from 'react-icons/fa6';

export default function FailedModal() {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setVisible(false), 5000); // Close modal after 5 seconds
    return () => clearTimeout(timer);
  }, []);

  if (!visible) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white p-6 rounded-lg shadow-md text-center">
        <h2 className="text-eweko_red font-bold text-lg mb-4">Placing order Failed</h2>
         <div className="bg-eweko_green/20 rounded-full h-[116px] w-[116px] flex items-center justify-center">
                  <FaCross className="text-[60px] text-eweko_red" />
                </div>
        <p className="text-gray-700">There was an issue processing your payment.</p>
        <p className="text-gray-700 mt-2">Please try again or contact support.</p>
      </div>
    </div>
  );
}
