import { useEffect, useState } from 'react';
import { FaCheck } from 'react-icons/fa6';
export default function SuccessModal() {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setVisible(false), 900000); // Close modal after 5 seconds
    return () => clearTimeout(timer);
  }, []);

  if (!visible) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-700 bg-opacity-50 z-50">

        <div className="bg-white p-6 rounded-lg shadow-md text-center">
          <div className="text-center px-[30%] w-full">
          <div className="bg-eweko_green/20 rounded-full h-[116px] w-[116px] flex items-center justify-center">
            <FaCheck className="text-[60px] text-eweko_green" />
          </div>
        </div>
        <h2 className="text-eweko_green_dark font-bold text-lg mb-4">
          Order placed Successful!
        </h2>


        <p className="text-gray-700">
          Your order has been placed successfully.
        </p>
        <p className="text-gray-700 mt-2">
          Redirecting you to the transaction page...
        </p>
      </div>
    </div>
  );
}
