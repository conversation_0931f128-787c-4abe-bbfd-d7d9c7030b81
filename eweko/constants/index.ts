import { GiFarmTractor, GiShoppingCart } from 'react-icons/gi';
import { LiaUsersCogSolid } from 'react-icons/lia';
import casava from '../public/casava.jpeg';
import lettus from '../public/lettus.png';
import maize from '../public/maize.jpeg';
import onions from '../public/onions.png';
import pepper from '../public/pepper.png';
import redpepper from '../public/redpepper.jpeg';
import sweetcorn from '../public/sweetcorn.jpeg';
import yam from '../public/yam.jpeg';

import contfarmIcon from '@/app/assets/icons/contract_farming.svg';
import dashIcon from '@/app/assets/icons/dash.svg';
import produceIcon from '@/app/assets/icons/produce.svg';
import reportIcon from '@/app/assets/icons/report.svg';
import trnxIcon from '@/app/assets/icons/transaction.svg';

export const rootNavItems = [
  { name: 'buyers', url: '/buyers' },
  { name: 'farmers', url: '/farmers' },
  { name: 'Produce', url: '/produce' },
  { name: 'Partners', url: '/partners' },
];

export const userCategories = [
  { name: 'Buyer', url: '/auth/buyers', icon: GiShoppingCart, isActive: true },
  { name: 'Farmer', url: '/auth/farmers', icon: GiFarmTractor, isActive: true },
  {
    name: 'Other Value Chain Actors',
    url: '/auth/',
    icon: LiaUsersCogSolid,
    isActive: false,
  },
];

export const buyerSidebarItems = [
  { name: 'Dashboard', url: '/buyers', icon: dashIcon },
  { name: 'Produce', url: '/buyers/produce', icon: produceIcon },
  { name: 'Transactions', url: '/buyers/transactions', icon: trnxIcon },
  { name: 'Reports', url: '/buyers/reports', icon: reportIcon },
  {
    name: 'Contract Farming',
    url: '/buyers/contract-farming',
    icon: contfarmIcon,
  },
];


export const farmersSidebarItems = [
  { name: 'Dashboard', url: '/farmers', icon: dashIcon },
  { name: 'Produce', url: '/farmers/produce', icon: produceIcon },
  { name: 'Transactions', url: '/farmers/transactions', icon: trnxIcon },
  { name: 'Reports', url: '/farmers/reports', icon: reportIcon },
  {
    name: 'Contract Farming',
    url: '/farmers/contract-farming',
    icon: contfarmIcon,
  },
];

export const produce = [
  {
    id: 1,
    name: 'Sweet Corn',
    qtyAvailable: 2000,
    minOrderQty: 250,
    price: 1700,
    image: sweetcorn,
    category: 'Vegetables',

    hasDiscount: true,
    discountedPrice: 1600,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 2,
    name: 'Tomatoes',
    qtyAvailable: 2000,
    minOrderQty: 310,
    price: 1700,
    image: pepper,
    category: 'Vegetables',
    hasDiscount: false,
    discountedPrice: null,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, maize, onions],
  },
  {
    id: 3,
    name: 'Lettuce',
    qtyAvailable: 2000,
    minOrderQty: 375,
    price: 1700,
    image: lettus,
    category: 'Vegetables',
    hasDiscount: true,
    discountedPrice: 1500,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 4,
    name: 'Onions',
    qtyAvailable: 2000,
    minOrderQty: 200,
    price: 1700,
    image: onions,
    category: 'Vegetables',
    hasDiscount: false,
    discountedPrice: null,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 5,
    name: 'Cassava',
    qtyAvailable: 5000,
    minOrderQty: 200,
    price: 1500,
    image: casava,
    category: 'Tubers',
    hasDiscount: false,
    discountedPrice: null,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 6,
    name: 'Yam',
    qtyAvailable: 3000,
    minOrderQty: 500,
    price: 1800,
    image: yam,
    category: 'Tubers',
    hasDiscount: true,

    discountedPrice: 1500,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 7,
    name: 'Pepper',
    qtyAvailable: 1000,
    minOrderQty: 100,
    price: 2500,
    image: redpepper,
    category: 'Peppers',
    hasDiscount: true,

    discountedPrice: 2100,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 8,
    name: 'Maize',
    qtyAvailable: 10000,
    minOrderQty: 1000,
    price: 1400,
    image: maize,
    category: 'Grains',
    hasDiscount: false,

    discountedPrice: null,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
];




export const farmersProduce = [
  {
    id: 1,
    name: 'Sweet Corn',
    qtyAvailable: 27000,
    minOrderQty: 250,
type:"tomato",
    price: 1700,
    qtySold : 20000,
    sn:1,
    image: sweetcorn,
    category: 'Vegetables',
    SKV : "EWE001",
    harvestDate: "01/01/24",
    soldDate: "2025-1-2",
    hasDiscount: true,
    discountedPrice: 1600,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 2,
    name: 'Tomatoes',
    qtyAvailable: 25000,
    minOrderQty: 31000,
    price: 1700,
    qtySold : 20000,
    sn:2,
    image: pepper,
    SKV : "EWE001",
    harvestDate: "01/01/24",
    soldDate: "2025-2-2",

    category: 'Vegetables',
    hasDiscount: false,
    discountedPrice: null,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, maize, onions],
  },
  {
    id: 3,
    name: 'Lettuce',
    qtyAvailable: 20000,
    minOrderQty: 375,
    price: 1700,
    qtySold : 20000,
    sn:3,
    image: lettus,
    category: 'Vegetables',
    SKV : "EWE001",
    harvestDate: "01/01/24",
    soldDate: "2025-2-2",

    hasDiscount: true,
    discountedPrice: 1500,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 4,
    name: 'Onions',
    qtyAvailable: 20000,
    minOrderQty: 200,
    price: 1700,
    qtySold : 20000,
    sn:4,
    image: onions,
    category: 'Vegetables',
    SKV : "EWE001",
    harvestDate: "01/01/24",
    soldDate: "2025-2-2",

    hasDiscount: false,
    discountedPrice: null,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 5,
    name: 'Cassava',
    qtyAvailable: 50000,
    qtySold : 20000,
    sn:5,
    minOrderQty: 200,
    price: 1500,
    image: casava,
    category: 'Tubers',
    SKV : "EWE001",
    harvestDate: "01/01/24",
    soldDate: "2025-4-2",

    hasDiscount: false,
    discountedPrice: null,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 6,
    name: 'Yam',
    qtySold : 20000,
    sn:6,
    qtyAvailable: 40000,
    minOrderQty: 500,
    price: 1800,
    image: yam,
    category: 'Tubers',
    SKV : "EWE001",
    harvestDate: "01/01/24",
    soldDate: "2025-4-2",

    hasDiscount: true,

    discountedPrice: 1500,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 7,
    name: 'Pepper',
    qtyAvailable: 30000,
    minOrderQty: 100,
    price: 2500,
    qtySold : 20000,
    sn:7,
    image: redpepper,
    category: 'Peppers',
    hasDiscount: true,
    SKV : "EWE001",
    harvestDate: "01/01/24",
    soldDate: "2025-3-2",

    discountedPrice: 2100,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
  {
    id: 8,
    name: 'Maize',
    qtyAvailable: 100000,
    minOrderQty: 1000,
    price: 1400,
    qtySold : 20000,
    sn:8,
    image: maize,
    category: 'Grains',
    hasDiscount: false,
    SKV : "EWE001",
    harvestDate: "01/01/24",
    soldDate: "2025-10-2",

    discountedPrice: null,
    description:
      'Lorem ipsum dolor sit amet consectetur. Tincidunt adipiscing lobortis pharetra montes congue pellentesque quis lectus. Ultrices vulputate quam imperdiet fringilla. Congue vitae egestas leo rutrum amet posuere tortor. Tempor adipiscing orci.',
    additionalImages: [pepper, yam, lettus],
  },
];



export const metrics = [
  { label: 'New Orders', value: 326, change: 3.2 },
  { label: 'Shipped Orders', value: 12458, change: -1.8 },
  { label: 'Cancelled Orders', value: 243, change: 2.4 },
  { label: 'Total Orders', value: 15987, change: 4.7 },
];



export const farmersProduceMetrics = [
  { label: 'Top Sellers', value: "Tomato"  },
  { label: 'Total Orders', value: "104k" },
  { label: 'Gross Revenue (Ngn)', value: "10.5m" },
  { label: 'Avg Customer Ratings', value: 4.5 },
];


export const farmersDashboardMetrics = [
  { label: 'Total Successful Order ', value: "104k"  },
  { label: 'Gross Revenue (Ngn)', value: "10.5m" },
  { label: 'Total Failed Order ', value: 56  },
  { label: 'Avg Customer Ratings', value: 4.5 },

];


export const statesLgas = [
  {
    name: 'Abia',
    lgas: [
      'Aba South',
      'Arochukwu',
      'Bende',
      'Ikwuano',
      'Isiala Ngwa North',
      'Isiala Ngwa South',
      'Isuikwuato',
      'Obi Ngwa',
      'Ohafia',
      'Osisioma',
      'Ugwunagbo',
      'Ukwa East',
      'Ukwa West',
      'Umuahia North',
      'Umuahia South',
      'Umu Nneochi',
    ],
  },
  {
    name: 'Adamawa',
    lgas: [
      'Fufure',
      'Ganye',
      'Gayuk',
      'Gombi',
      'Grie',
      'Hong',
      'Jada',
      'Lamurde',
      'Madagali',
      'Maiha',
      'Mayo Belwa',
      'Michika',
      'Mubi North',
      'Mubi South',
      'Numan',
      'Shelleng',
      'Song',
      'Toungo',
      'Yola North',
      'Yola South',
    ],
  },
  {
    name: 'Akwa Ibom',
    lgas: [
      'Eastern Obolo',
      'Eket',
      'Esit Eket',
      'Essien Udim',
      'Etim Ekpo',
      'Etinan',
      'Ibeno',
      'Ibesikpo Asutan',
      'Ibiono-Ibom',
      'Ika',
      'Ikono',
      'Ikot Abasi',
      'Ikot Ekpene',
      'Ini',
      'Itu',
      'Mbo',
      'Mkpat-Enin',
      'Nsit-Atai',
      'Nsit-Ibom',
      'Nsit-Ubium',
      'Obot Akara',
      'Okobo',
      'Onna',
      'Oron',
      'Oruk Anam',
      'Udung-Uko',
      'Ukanafun',
      'Uruan',
      'Urue-Offong/Oruko',
      'Uyo',
    ],
  },
  {
    name: 'Anambra',
    lgas: [
      'Anambra East',
      'Anambra West',
      'Anaocha',
      'Awka North',
      'Awka South',
      'Ayamelum',
      'Dunukofia',
      'Ekwusigo',
      'Idemili North',
      'Idemili South',
      'Ihiala',
      'Njikoka',
      'Nnewi North',
      'Nnewi South',
      'Ogbaru',
      'Onitsha North',
      'Onitsha South',
      'Orumba North',
      'Orumba South',
      'Oyi',
    ],
  },
  {
    name: 'Bauchi',
    lgas: [
      'Bauchi',
      'Bogoro',
      'Damban',
      'Darazo',
      'Dass',
      'Gamawa',
      'Ganjuwa',
      'Giade',
      'Itas/Gadau',
      "Jama'are",
      'Katagum',
      'Kirfi',
      'Misau',
      'Ningi',
      'Shira',
      'Tafawa Balewa',
      'Toro',
      'Warji',
      'Zaki',
    ],
  },
  {
    name: 'Bayelsa',
    lgas: [
      'Ekeremor',
      'Kolokuma/Opokuma',
      'Nembe',
      'Ogbia',
      'Sagbama',
      'Southern Ijaw',
      'Yenagoa',
    ],
  },
  {
    name: 'Benue',
    lgas: [
      'Apa',
      'Ado',
      'Buruku',
      'Gboko',
      'Guma',
      'Gwer East',
      'Gwer West',
      'Katsina-Ala',
      'Konshisha',
      'Kwande',
      'Logo',
      'Makurdi',
      'Obi',
      'Ogbadibo',
      'Ohimini',
      'Oju',
      'Okpokwu',
      'Oturkpo',
      'Tarka',
      'Ukum',
      'Ushongo',
      'Vandeikya',
    ],
  },
  {
    name: 'Borno',
    lgas: [
      'Askira/Uba',
      'Bama',
      'Bayo',
      'Biu',
      'Chibok',
      'Damboa',
      'Dikwa',
      'Gubio',
      'Guzamala',
      'Gwoza',
      'Hawul',
      'Jere',
      'Kaga',
      'Kala/Balge',
      'Konduga',
      'Kukawa',
      'Kwaya Kusar',
      'Mafa',
      'Magumeri',
      'Maiduguri',
      'Marte',
      'Mobbar',
      'Monguno',
      'Ngala',
      'Nganzai',
      'Shani',
    ],
  },
  {
    name: 'Cross River',
    lgas: [
      'Akamkpa',
      'Akpabuyo',
      'Bakassi',
      'Bekwarra',
      'Biase',
      'Boki',
      'Calabar Municipal',
      'Calabar South',
      'Etung',
      'Ikom',
      'Obanliku',
      'Obubra',
      'Obudu',
      'Odukpani',
      'Ogoja',
      'Yakuur',
      'Yala',
    ],
  },
  {
    name: 'Delta',
    lgas: [
      'Aniocha South',
      'Bomadi',
      'Burutu',
      'Ethiope East',
      'Ethiope West',
      'Ika North East',
      'Ika South',
      'Isoko North',
      'Isoko South',
      'Ndokwa East',
      'Ndokwa West',
      'Okpe',
      'Oshimili North',
      'Oshimili South',
      'Patani',
      'Sapele',
      'Udu',
      'Ughelli North',
      'Ughelli South',
      'Ukwuani',
      'Uvwie',
      'Warri North',
      'Warri South',
      'Warri South West',
    ],
  },
  {
    name: 'Ebonyi',
    lgas: [
      'Afikpo North',
      'Afikpo South',
      'Ebonyi',
      'Ezza North',
      'Ezza South',
      'Ikwo',
      'Ishielu',
      'Ivo',
      'Izzi',
      'Ohaozara',
      'Ohaukwu',
      'Onicha',
    ],
  },
  {
    name: 'Edo',
    lgas: [
      'Egor',
      'Esan Central',
      'Esan North-East',
      'Esan South-East',
      'Esan West',
      'Etsako Central',
      'Etsako East',
      'Etsako West',
      'Igueben',
      'Ikpoba Okha',
      'Orhionmwon',
      'Oredo',
      'Ovia North-East',
      'Ovia South-West',
      'Owan East',
      'Owan West',
      'Uhunmwonde',
    ],
  },
  {
    name: 'Ekiti',
    lgas: [
      'Efon',
      'Ekiti East',
      'Ekiti South-West',
      'Ekiti West',
      'Emure',
      'Gbonyin',
      'Ido Osi',
      'Ijero',
      'Ikere',
      'Ikole',
      'Ilejemeje',
      'Irepodun/Ifelodun',
      'Ise/Orun',
      'Moba',
      'Oye',
    ],
  },
  {
    name: 'Enugu',
    lgas: [
      'Awgu',
      'Enugu East',
      'Enugu North',
      'Enugu South',
      'Ezeagu',
      'Igbo Etiti',
      'Igbo Eze North',
      'Igbo Eze South',
      'Isi Uzo',
      'Nkanu East',
      'Nkanu West',
      'Nsukka',
      'Oji River',
      'Udenu',
      'Udi',
      'Uzo Uwani',
    ],
  },
  {
    name: 'FCT',
    lgas: ['Bwari', 'Gwagwalada', 'Kuje', 'Kwali', 'Municipal Area Council'],
  },
  {
    name: 'Gombe',
    lgas: [
      'Balanga',
      'Billiri',
      'Dukku',
      'Funakaye',
      'Gombe',
      'Kaltungo',
      'Kwami',
      'Nafada',
      'Shongom',
      'Yamaltu/Deba',
    ],
  },
  {
    name: 'Imo',
    lgas: [
      'Ahiazu Mbaise',
      'Ehime Mbano',
      'Ezinihitte',
      'Ideato North',
      'Ideato South',
      'Ihitte/Uboma',
      'Ikeduru',
      'Isiala Mbano',
      'Isu',
      'Mbaitoli',
      'Ngor Okpala',
      'Njaba',
      'Nkwerre',
      'Nwangele',
      'Obowo',
      'Oguta',
      'Ohaji/Egbema',
      'Okigwe',
      'Orlu',
      'Orsu',
      'Oru East',
      'Oru West',
      'Owerri Municipal',
      'Owerri North',
      'Owerri West',
      'Unuimo',
    ],
  },
  {
    name: 'Jigawa',
    lgas: [
      'Babura',
      'Biriniwa',
      'Birnin Kudu',
      'Buji',
      'Dutse',
      'Gagarawa',
      'Garki',
      'Gumel',
      'Guri',
      'Gwaram',
      'Gwiwa',
      'Hadejia',
      'Jahun',
      'Kafin Hausa',
      'Kazaure',
      'Kiri Kasama',
      'Kiyawa',
      'Kaugama',
      'Maigatari',
      'Malam Madori',
      'Miga',
      'Ringim',
      'Roni',
      'Sule Tankarkar',
      'Taura',
      'Yankwashi',
    ],
  },
  {
    name: 'Kaduna',
    lgas: [
      'Chikun',
      'Giwa',
      'Igabi',
      'Ikara',
      'Jaba',
      "Jema'a",
      'Kachia',
      'Kaduna North',
      'Kaduna South',
      'Kagarko',
      'Kajuru',
      'Kaura',
      'Kauru',
      'Kubau',
      'Kudan',
      'Lere',
      'Makarfi',
      'Sabon Gari',
      'Sanga',
      'Soba',
      'Zangon Kataf',
      'Zaria',
    ],
  },
  {
    name: 'Kano',
    lgas: [
      'Albasu',
      'Bagwai',
      'Bebeji',
      'Bichi',
      'Bunkure',
      'Dala',
      'Dambatta',
      'Dawakin Kudu',
      'Dawakin Tofa',
      'Doguwa',
      'Fagge',
      'Gabasawa',
      'Garko',
      'Garun Mallam',
      'Gaya',
      'Gezawa',
      'Gwale',
      'Gwarzo',
      'Kabo',
      'Kano Municipal',
      'Karaye',
      'Kibiya',
      'Kiru',
      'Kumbotso',
      'Kunchi',
      'Kura',
      'Madobi',
      'Makoda',
      'Minjibir',
      'Nasarawa',
      'Rano',
      'Rimin Gado',
      'Rogo',
      'Shanono',
      'Sumaila',
      'Takai',
      'Tarauni',
      'Tofa',
      'Tsanyawa',
      'Tudun Wada',
      'Ungogo',
      'Warawa',
      'Wudil',
    ],
  },
  {
    name: 'Katsina',
    lgas: [
      'Batagarawa',
      'Batsari',
      'Baure',
      'Bindawa',
      'Charanchi',
      'Dandume',
      'Danja',
      'Dan Musa',
      'Daura',
      'Dutsi',
      'Dutsin Ma',
      'Faskari',
      'Funtua',
      'Ingawa',
      'Jibia',
      'Kafur',
      'Kaita',
      'Kankara',
      'Kankia',
      'Katsina',
      'Kurfi',
      'Kusada',
      "Mai'Adua",
      'Malumfashi',
      'Mani',
      'Mashi',
      'Matazu',
      'Musawa',
      'Rimi',
      'Sabuwa',
      'Safana',
      'Sandamu',
      'Zango',
    ],
  },
  {
    name: 'Kebbi',
    lgas: [
      'Arewa Dandi',
      'Argungu',
      'Augie',
      'Bagudo',
      'Birnin Kebbi',
      'Bunza',
      'Dandi',
      'Fakai',
      'Gwandu',
      'Jega',
      'Kalgo',
      'Koko/Besse',
      'Maiyama',
      'Ngaski',
      'Sakaba',
      'Shanga',
      'Suru',
      'Wasagu/Danko',
      'Yauri',
      'Zuru',
    ],
  },
  {
    name: 'Kogi',
    lgas: [
      'Ajaokuta',
      'Ankpa',
      'Bassa',
      'Dekina',
      'Ibaji',
      'Idah',
      'Igalamela Odolu',
      'Ijumu',
      'Kabba/Bunu',
      'Kogi',
      'Lokoja',
      'Mopa Muro',
      'Ofu',
      'Ogori/Magongo',
      'Okehi',
      'Okene',
      'Olamaboro',
      'Omala',
      'Yagba East',
      'Yagba West',
    ],
  },
  {
    name: 'Kwara',
    lgas: [
      'Baruten',
      'Edu',
      'Ekiti',
      'Ifelodun',
      'Ilorin East',
      'Ilorin South',
      'Ilorin West',
      'Irepodun',
      'Isin',
      'Kaiama',
      'Moro',
      'Offa',
      'Oke Ero',
      'Oyun',
      'Pategi',
    ],
  },
  {
    name: 'Lagos',
    lgas: [
      'Ajeromi-Ifelodun',
      'Alimosho',
      'Amuwo-Odofin',
      'Apapa',
      'Badagry',
      'Epe',
      'Eti Osa',
      'Ibeju-Lekki',
      'Ifako-Ijaiye',
      'Ikeja',
      'Ikorodu',
      'Kosofe',
      'Lagos Island',
      'Lagos Mainland',
      'Mushin',
      'Ojo',
      'Oshodi-Isolo',
      'Shomolu',
      'Surulere',
    ],
  },
  {
    name: 'Nasarawa',
    lgas: [
      'Awe',
      'Doma',
      'Karu',
      'Keana',
      'Keffi',
      'Kokona',
      'Lafia',
      'Nasarawa',
      'Nasarawa Egon',
      'Obi',
      'Toto',
      'Wamba',
    ],
  },
  {
    name: 'Niger',
    lgas: [
      'Agwara',
      'Bida',
      'Borgu',
      'Bosso',
      'Chanchaga',
      'Edati',
      'Gbako',
      'Gurara',
      'Katcha',
      'Kontagora',
      'Lapai',
      'Lavun',
      'Magama',
      'Mariga',
      'Mashegu',
      'Mokwa',
      'Moya',
      'Paikoro',
      'Rafi',
      'Rijau',
      'Shiroro',
      'Suleja',
      'Tafa',
      'Wushishi',
    ],
  },
  {
    name: 'Ogun',
    lgas: [
      'Abeokuta South',
      'Ado-Odo/Ota',
      'Egbado North',
      'Egbado South',
      'Ewekoro',
      'Ifo',
      'Ijebu East',
      'Ijebu North',
      'Ijebu North East',
      'Ijebu Ode',
      'Ikenne',
      'Imeko Afon',
      'Ipokia',
      'Obafemi Owode',
      'Odeda',
      'Odogbolu',
      'Ogun Waterside',
      'Remo North',
      'Shagamu',
    ],
  },
  {
    name: 'Ondo',
    lgas: [
      'Akoko North-West',
      'Akoko South-West',
      'Akoko South-East',
      'Akure North',
      'Akure South',
      'Ese Odo',
      'Idanre',
      'Ifedore',
      'Ilaje',
      'Ile Oluji/Okeigbo',
      'Irele',
      'Odigbo',
      'Okitipupa',
      'Ondo East',
      'Ondo West',
      'Ose',
      'Owo',
    ],
  },
  {
    name: 'Osun',
    lgas: [
      'Atakunmosa West',
      'Aiyedaade',
      'Aiyedire',
      'Boluwaduro',
      'Boripe',
      'Ede North',
      'Ede South',
      'Ife Central',
      'Ife East',
      'Ife North',
      'Ife South',
      'Egbedore',
      'Ejigbo',
      'Ifedayo',
      'Ifelodun',
      'Ila',
      'Ilesa East',
      'Ilesa West',
      'Irepodun',
      'Irewole',
      'Isokan',
      'Iwo',
      'Obokun',
      'Odo Otin',
      'Ola Oluwa',
      'Olorunda',
      'Oriade',
      'Orolu',
      'Osogbo',
    ],
  },
  {
    name: 'Oyo',
    lgas: [
      'Akinyele',
      'Atiba',
      'Atisbo',
      'Egbeda',
      'Ibadan North',
      'Ibadan North-East',
      'Ibadan North-West',
      'Ibadan South-East',
      'Ibadan South-West',
      'Ibarapa Central',
      'Ibarapa East',
      'Ibarapa North',
      'Ido',
      'Irepo',
      'Iseyin',
      'Itesiwaju',
      'Iwajowa',
      'Kajola',
      'Lagelu',
      'Ogbomosho North',
      'Ogbomosho South',
      'Ogo Oluwa',
      'Olorunsogo',
      'Oluyole',
      'Ona Ara',
      'Orelope',
      'Ori Ire',
      'Oyo',
      'Oyo East',
      'Saki East',
      'Saki West',
      'Surulere',
    ],
  },
  {
    name: 'Plateau',
    lgas: [
      'Barkin Ladi',
      'Bassa',
      'Jos East',
      'Jos North',
      'Jos South',
      'Kanam',
      'Kanke',
      'Langtang South',
      'Langtang North',
      'Mangu',
      'Mikang',
      'Pankshin',
      "Qua'an Pan",
      'Riyom',
      'Shendam',
      'Wase',
    ],
  },
  {
    name: 'Rivers',
    lgas: [
      'Ahoada East',
      'Ahoada West',
      'Akuku-Toru',
      'Andoni',
      'Asari-Toru',
      'Bonny',
      'Degema',
      'Eleme',
      'Emuoha',
      'Etche',
      'Gokana',
      'Ikwerre',
      'Khana',
      'Obio/Akpor',
      'Ogba/Egbema/Ndoni',
      'Ogu/Bolo',
      'Okrika',
      'Omuma',
      'Opobo/Nkoro',
      'Oyigbo',
      'Port Harcourt',
      'Tai',
    ],
  },
  {
    name: 'Sokoto',
    lgas: [
      'Bodinga',
      'Dange Shuni',
      'Gada',
      'Goronyo',
      'Gudu',
      'Gwadabawa',
      'Illela',
      'Isa',
      'Kebbe',
      'Kware',
      'Rabah',
      'Sabon Birni',
      'Shagari',
      'Silame',
      'Sokoto North',
      'Sokoto South',
      'Tambuwal',
      'Tangaza',
      'Tureta',
      'Wamako',
      'Wurno',
      'Yabo',
    ],
  },
  {
    name: 'Taraba',
    lgas: [
      'Bali',
      'Donga',
      'Gashaka',
      'Gassol',
      'Ibi',
      'Jalingo',
      'Karim Lamido',
      'Kumi',
      'Lau',
      'Sardauna',
      'Takum',
      'Ussa',
      'Wukari',
      'Yorro',
      'Zing',
    ],
  },
  {
    name: 'Yobe',
    lgas: [
      'Bursari',
      'Damaturu',
      'Fika',
      'Fune',
      'Geidam',
      'Gujba',
      'Gulani',
      'Jakusko',
      'Karasuwa',
      'Machina',
      'Nangere',
      'Nguru',
      'Potiskum',
      'Tarmuwa',
      'Yunusari',
      'Yusufari',
    ],
  },
  {
    name: 'Zamfara',
    lgas: [
      'Bakura',
      'Birnin Magaji/Kiyaw',
      'Bukkuyum',
      'Bungudu',
      'Gummi',
      'Gusau',
      'Kaura Namoda',
      'Maradun',
      'Maru',
      'Shinkafi',
      'Talata Mafara',
      'Chafe',
      'Zurmi',
    ],
  },
];

export const transactionSummary = [
  { label: 'Total', value: 14988, change: 24.7 },
  { label: 'Successful', value: 12458, change: -1.8 },
  { label: 'Pending', value: 2043, change: 11.4 },
  { label: 'Failed', value: 487, change: -4.7 },
];
