import { useEffect } from 'react';

export const useInteractionLock = (isLoading: boolean) => {
  useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      if (isLoading) {
        e.preventDefault();
        e.stopPropagation();
      }
    };

    if (isLoading) {
      document.body.style.overflow = 'hidden';
      document.body.style.pointerEvents = 'none';
      document.body.style.userSelect = 'none';
      window.addEventListener('keydown', handleKeydown);
    } else {
      document.body.style.overflow = 'auto';
      document.body.style.pointerEvents = 'all';
      document.body.style.userSelect = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
      document.body.style.pointerEvents = 'all';
      document.body.style.userSelect = 'auto';
      window.removeEventListener('keydown', handleKeydown);
    };
  }, [isLoading]);
};
