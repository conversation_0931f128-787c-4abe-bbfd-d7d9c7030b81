import { useGlobalState, User } from '@/globalStore';
import * as jose from 'jose';
import Cookies from 'js-cookie';

export const useVerifyJWT = () => {
  const jwtSecret = process.env.NEXT_PUBLIC_JWT_SECRET;
  const setLoggedInUser = useGlobalState(state => state.setLoggedInUser);
  const authToken = useGlobalState(state => state.authToken);

  const verifyJWT = async (): Promise<User | null> => {
    try {
      if (!jwtSecret) {
        return null;
      }

      if (!authToken) {
        Cookies.remove('3wkAt');

        return null;
      } else {
        Cookies.set('3wkAt', authToken, { expires: 999 });
      }

      const encoder = new TextEncoder();
      const secretKey = encoder.encode(jwtSecret);

      const { payload } = await jose.jwtVerify(authToken, secretKey);

      const user: User = {
        id: payload.id as string,
        email: payload.email as string,
        firstName: (payload.firstName || payload.first_name) as string,
        lastName: (payload.lastName || payload.last_name) as string,
        phone: payload.phone as string,
        userType: (payload.userType || payload.user_type) as string,
        profilePicture: (payload.profilePicture || payload.profile_picture) as string,
      };

      setLoggedInUser(user);

      return user;
    } catch (error) {
      return null;
    }
  };

  return verifyJWT;
};
