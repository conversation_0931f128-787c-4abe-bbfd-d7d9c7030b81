import { NextURL } from 'next/dist/server/web/next-url';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { verifyToken } from './lib/utils';
import { IJwtPayload } from './app/(buyers)/buyers/types';

const PROTECTED_ROUTES: string[] = ['/buyers',
   '/farmers'
  ];
const USER_SECTION_MAP: Record<string, string> = {
  BUYER: 'buyers',
  FARMER: 'farmers',
  LOGISTICS: 'logistics',
  ADMIN: 'admin',
  AGENT: 'agents',
};

// Utility function to verify the token twice
const verifyTokenThrice = async (token: string) => {
  let validPayload: IJwtPayload | null = null;

  for (let attempt = 1; attempt <= 3; attempt++) {
    console.error(`Token Verification Attempt ${attempt}...`);

    try {
      const check = await verifyToken(token);
      if (check?.isValid && check.payload) {
        validPayload = check.payload;
        break;
      }
    } catch (error) {
      console.error(`Token Verification Attempt ${attempt} Failed:`, error);
    }
  }

  return validPayload;
};

export default async function middleware(req: NextRequest) {
  const url = new NextURL(req.url);
  const path = url.pathname;
  const token = req.cookies.get('3wkAt')?.value;
  const isAuthRoute = path.startsWith('/auth/');

  // Check if the path matches any protected route
  const isProtectedRoute = PROTECTED_ROUTES.some(
    route => path === route || path.startsWith(`${route}/`)
  );

  // Verify token
  const tokenPayload = token ? await verifyTokenThrice(token) : null;
  const isValidToken = Boolean(tokenPayload);
  const userType = tokenPayload?.userType || null;
  const authorizedSection = userType ? USER_SECTION_MAP[userType] : null;

  // 🔒 Authenticated users trying to access auth pages should be redirected to their dashboard
  if (isAuthRoute && isValidToken && authorizedSection) {
    url.pathname = `/${authorizedSection}`;
    return NextResponse.redirect(url);
  }

  // 🔐 Unauthenticated users trying to access protected routes should be redirected to login
  if (isProtectedRoute && !isValidToken) {
    const section = path.split('/')[1] || '';
    url.pathname = `/auth/${section}`;
    return NextResponse.redirect(url);
  }

  // 🔓 Authenticated users trying to access unauthorized sections should be redirected
  if (isProtectedRoute && isValidToken && authorizedSection) {
    const requestedSection = path.split('/')[1] || '';

    if (authorizedSection !== requestedSection) {
      url.pathname = `/${authorizedSection}`;
      return NextResponse.redirect(url);
    }
  }

  // Allow access if all checks pass
  return NextResponse.next();
}

export const config = {
  matcher: ['/buyers/:path*', '/farmers/:path*', '/auth/:path*'],
};
