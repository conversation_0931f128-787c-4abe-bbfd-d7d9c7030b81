{"name": "e<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "npx prettier src test --check", "prettier:fix": "npm run prettier -- --write"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@paystack/inline-js": "^2.22.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@tanstack/react-query": "^5.64.2", "@tanstack/react-table": "^8.21.2", "@tiptap/extension-underline": "^2.9.1", "@tiptap/pm": "^2.9.1", "@tiptap/react": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "axios": "^1.7.9", "axios-retry": "^4.5.0", "class-variance-authority": "^0.7.0", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "eslint-config-next": "^15.2.0", "eslint-config-prettier": "^10.0.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unused-imports": "^4.1.4", "formidable": "^3.5.2", "framer-motion": "^11.11.17", "input-otp": "^1.4.1", "jose": "^5.9.6", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lib": "^5.1.0", "lodash.debounce": "^4.0.8", "lucide-react": "^0.460.0", "next": "15.0.3", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.4", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-icons": "^5.3.0", "react-query": "^3.39.3", "react-slick": "^0.30.2", "react-toastify": "^11.0.3", "server-only": "^0.0.1", "shadcn-ui": "^0.9.4", "socket.io-client": "^4.8.1", "sonner": "^1.7.2", "swiper": "^11.1.14", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.15.0", "@svgr/webpack": "^8.1.0", "@tanstack/eslint-plugin-query": "^5.64.2", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.8", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22", "@types/paystack__inline-js": "^1.0.1", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9.15.0", "postcss": "^8", "prettier": "3.3.3", "tailwindcss": "^3.4.15", "typescript": "^5"}}