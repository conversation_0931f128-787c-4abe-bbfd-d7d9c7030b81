"use client";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules"; // Correct module imports for Swiper v8+

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import farmer from "@/public/farmer.png";
import variety from "@/public/varity.png";
import prodbasket from '@/public/prodbasket.png';



const ProductSlider = () => {


    return(
        <section className=" w-full  justify-center items-center   pb-4">
        <Swiper
        modules={[Autoplay, Navigation, Pagination]}
        spaceBetween={20}
        slidesPerView={2}
        autoplay={{
          delay: 2000,
          disableOnInteraction: false,
        }}
        breakpoints={{

          300: {
            slidesPerView: 1, // Small screens (up to 640px)
          },
          568: {
            slidesPerView: 1, // Medium screens (568px to 767px)
          },
          768: {
            slidesPerView: 1, // Tablets (768px and above)
          },
          1000: {
            slidesPerView: 1, // Desktops (1024px and above)
          },
          1280: {
            slidesPerView: 2, // Large screens (1280px and above)
          },
        }}
        // navigation
       //  pagination={{ clickable: true }}
        loop={true}
        grabCursor={true}
        speed={1000} // Smooth transition duration (ms)
        className="rounded-lg overflow-hidden"
      >



        <SwiperSlide>
            <div className=" pt-5">
                <div className="bg-[#85B04C] h-auto lg:h-[100px] text-white px-6 py-1 rounded-lg flex flex-col md:flex-row items-center gap-2 overflow-y-visible relative">
            <div className="flex-1">
              <h2 className="text-[15px]  text-eweko_green_dark md:text-[19px] font-bold">
                Get 50%Off Today <br /> On <span className="text-white">All Produce</span>
              </h2>
              <p className="mt-2 text-[10px] xl:text-[7px] text-eweko_green_dark">
              Fresh savings await! don’t miss this chance to stock up on goodness!
              </p>

            </div>
            <div className="flex-1">
              <Image
                src= {farmer} // Replace with your image path
                alt="Farmer"
                width={130}
                height={300}
                className=" mx-auto xl:size-[120px] lg:mt-[-10px]   "
              />
            </div>
            <button className="mt-[-6ox] text-[11px] lg:text-[12px] bg-eweko_green_dark text-white px-2 lg:px-2 py-2 rounded-lg hover:bg-eweko_green_light transition">
                Learn More
              </button>
          </div>
          </div>

        </SwiperSlide>


        <SwiperSlide>
        <div className=" pt-4">
        <div className="bg-white h-auto lg:h-[100px] text-white px-6 py-2  rounded-lg flex flex-col md:flex-row items-center ">
          <div className="flex-1">
              <Image
                src={variety} // Replace with your image path
                alt="Farmer"
                width={150}
                height={90}
                className=" "
              />
            </div>
            <div className="flex-1  ml-[-100px]">
              <h2 className="text-[20px] text-eweko_green_dark md:text-[20px] font-bold">
                Fuelling Your  <br /><span className="text-eweko_green_light">Freshness</span> Journey!
              </h2>
              <p className="mt-2 text-[10px] text-eweko_green_dark">
              Discover unbeatable prices on the freshest produce!
              </p>

            </div>
              <button className="mt-[-6px] 11 lg:text-[12px] bg-eweko_green_dark text-white px-2 lg:px-2 py-2 rounded-lg hover:bg-eweko_green_light transition">
                Learn More
              </button>


          </div>

        </div>

        </SwiperSlide>


        <SwiperSlide>
            <div className=" pt-5">
                <div className="bg-[#85B04C] h-auto lg:h-[100px] text-white px-6 py-1 rounded-lg flex flex-col md:flex-row items-center gap-2 overflow-y-visible relative">
            <div className="flex-1">
              <h2 className="text-[15px]  text-eweko_green_dark md:text-[19px] font-bold">
              Get 50%Off Today <br /> On <span className="text-white">All Produce</span>
              </h2>
              <p className="mt-2 text-[10px] xl:text-[7px] text-eweko_green_dark">
              Fresh savings await! don’t miss this chance to stock up on goodness!
              </p>

            </div>
            <div className="flex-1">
              <Image
                src= {farmer} // Replace with your image path
                alt="Farmer"
                width={130}
                height={300}
                className=" mx-auto xl:size-[120px] lg:mt-[-10px]   "
              />
            </div>
            <button className="mt-[-6px] text-[11px] lg:text-[12px] bg-eweko_green_dark text-white px-2 lg:px-2 py-2 rounded-lg hover:bg-eweko_green_light transition">
                Learn More
              </button>
          </div></div>

        </SwiperSlide>



        <SwiperSlide>
        <div className=" pt-4">
        <div className="bg-white h-auto lg:h-[100px] text-white px-6 py-2  rounded-lg flex flex-col md:flex-row items-center ">
          <div className="flex-1">
              <Image
                src={variety} // Replace with your image path
                alt="Farmer"
                width={150}
                height={90}
                className=" "
              />
            </div>
            <div className="flex-1 ml-[-100px]">
              <h2 className="text-[20px] text-eweko_green_dark md:text-[20px] font-bold">
              Fuelling Your  <br /><span className="text-eweko_green_light">Freshness</span> Journey!
              </h2>
              <p className="mt-2 text-[10px] text-eweko_green_dark">
              Discover unbeatable prices on the freshest produce!
              </p>

            </div>
              <button className="mt-[-6px] 11 lg:text-[12px] bg-eweko_green_dark text-white px-2 lg:px-2 py-2 rounded-lg hover:bg-eweko_green_light transition">
                Learn More
              </button>


          </div>

        </div>

        </SwiperSlide>



      </Swiper>
        </section>
    )
}
export default ProductSlider
