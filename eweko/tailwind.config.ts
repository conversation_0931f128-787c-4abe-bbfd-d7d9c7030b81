import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      screens: {
        sm: '480px',
        ssm: '300px',
        sm2: '568px',
        sm3: '640px',
        md: '768px',
        md2: '840px',
        md3: '960px',
        lg: '1024px',
        lg2: '1100px',
        xl: '1280px',
        '2xl': '1440px',
        '3xl': '1600px',
        '4xl': '1920px',
        '5xl': '2560px',
      },
      fontFamily: {
        jsbold: ['var(--jsbold)'],
        jsexbold: ['var(--jsexbold)'],
        jsexlight: ['var(--jsexlight)'],
        jslight: ['var(--jslight)'],
        jsmedium: ['var(--jsmedium)'],
        jsregular: ['var(--jsregular)'],
        jssembold: ['var(--jssembold)'],
      },
      zIndex: {
        '45': '45',
        '50': '50',
        '55': '55',
        '60': '60',
        '65': '65',
        '70': '70',
        '75': '75',
        '80': '80',
        '85': '85',
        '90': '90',
        '95': '95',
      },
      backgroundImage: {
        ewekobg: "url('/ewekohero.png')",
      },

      colors: {
        eweko_green: '#587240',
        eweko_green_light: '#85B04C',
        eweko_green_dark: '#394532',
        eweko_red: '#D64847',
        eweko_error_bgcolor: '#FFE5D6',

        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'caret-blink': {
          '0%,70%,100%': { opacity: '1' },
          '20%,50%': { opacity: '0' },
        },
      },
      animation: {
        'caret-blink': 'caret-blink 1.25s ease-out infinite',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
export default config;
