
## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## API URLs

- **Staging API URL:** https://ewekoapi-staging-nilyx.ondigitalocean.app/  
- **Production API URL:** https://ewekoapi-pq59z.ondigitalocean.app/


## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```
