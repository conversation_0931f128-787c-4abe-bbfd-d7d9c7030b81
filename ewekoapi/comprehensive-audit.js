const { MongoClient } = require('mongodb');

const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DATABASE = 'ewekoapi';

// Comprehensive PostgreSQL Entity Mappings
const POSTGRES_ENTITIES = {
  // Main tables
  users: {
    table: 'users',
    fields: [
      'id', 'username', 'password', 'user_type', 'verified', 'is_active', 'last_login',
      'first_name', 'last_name', 'middle_name', 'prefix', 'gender', 'date_of_birth', 'profile_picture',
      'email', 'primary_phone', 'secondary_phone', 'is_premium', 'is_phone_verified', 'is_email_verified',
      'created_at', 'updated_at'
    ]
  },
  farmers: {
    table: 'farmers',
    fields: [
      'id', 'user_id', 'farm_name', 'farm_location', 'farm_size', 'farm_address',
      'account_number', 'account_name', 'bank_name', 'bank_branch', 'created_at', 'updated_at'
    ]
  },
  buyers: {
    table: 'buyers',
    fields: [
      'id', 'user_id', 'business_name', 'loyalty_points', 'delivery_preferences', 'payment_methods',
      'created_at', 'updated_at'
    ]
  },
  admins: {
    table: 'admins',
    fields: [
      'id', 'user_id', 'role', 'permissions', 'managed_departments', 'admin_token',
      'created_at', 'updated_at'
    ]
  },
  addresses: {
    table: 'addresses',
    fields: [
      'id', 'user_id', 'house_number', 'street_name', 'community', 'lga', 'state', 'country',
      'is_default', 'created_at', 'updated_at'
    ]
  },
  notifications: {
    table: 'notifications',
    fields: [
      'id', 'user_id', 'user_type', 'subject', 'message', 'trigger', 'is_read',
      'created_at', 'updated_at'
    ]
  },
  wallets: {
    table: 'wallets',
    fields: [
      'id', 'farmer_id', 'balance', 'gross_revenue', 'created_at', 'updated_at'
    ]
  },
  produces: {
    table: 'produces',
    fields: [
      'id', 'farmer_id', 'category_id', 'name', 'description', 'price', 'negotiable_price',
      'min_order_qty', 'stock', 'images', 'slug', 'harvest_date', 'created_at', 'updated_at'
    ]
  },
  categories: {
    table: 'categories',
    fields: [
      'id', 'name', 'slug', 'description', 'image', 'is_active', 'created_at', 'updated_at'
    ]
  },
  orders: {
    table: 'orders',
    fields: [
      'id', 'buyer_id', 'farmer_id', 'status', 'total_cost', 'shipping_fee', 'shipping_address',
      'payment_method', 'payment_status', 'payment_date', 'shipped_date', 'final_total_cost',
      'created_at', 'updated_at'
    ]
  },
  order_items: {
    table: 'order_items',
    fields: [
      'id', 'order_id', 'produce_id', 'quantity', 'unit_price', 'total_price',
      'created_at', 'updated_at'
    ]
  },
  transactions: {
    table: 'transactions',
    fields: [
      'id', 'user_id', 'buyer_id', 'farmer_id', 'order_id', 'total_amount', 'status',
      'payment_method', 'payment_type', 'reference', 'processed', 'gateway_reference',
      'description', 'metadata', 'created_at', 'updated_at'
    ]
  },
  carts: {
    table: 'carts',
    fields: [
      'id', 'user_id', 'total_cost', 'active', 'created_at', 'updated_at'
    ]
  },
  cart_items: {
    table: 'cart_items',
    fields: [
      'id', 'cart_id', 'produce_id', 'quantity', 'unit_price', 'total_price',
      'created_at', 'updated_at'
    ]
  },
  payments: {
    table: 'payments',
    fields: [
      'id', 'user_id', 'order_id', 'amount', 'payment_method', 'payment_status',
      'reference', 'gateway_reference', 'created_at', 'updated_at'
    ]
  },
  otps: {
    table: 'otps',
    fields: [
      'id', 'user_id', 'code', 'use_case', 'used', 'expires_at', 'created_at', 'updated_at'
    ]
  },
  preferences: {
    table: 'preferences',
    fields: [
      'id', 'user_id', 'otp_destination', 'receive_promotions', 'enable_2fa',
      'general_updates', 'order_updates', 'transaction_updates', 'payment_updates',
      'delivery_updates', 'created_at', 'updated_at'
    ]
  },
  weekly_prices: {
    table: 'weekly_prices',
    fields: [
      'id', 'category_id', 'produce_name', 'avg_price', 'min_price', 'max_price',
      'week_number', 'year', 'week_start', 'week_end_date', 'market_data', 'is_active',
      'created_at', 'updated_at'
    ]
  },
  admin_tokens: {
    table: 'admin_tokens',
    fields: [
      'id', 'admin_id', 'token', 'type', 'expires_at', 'used', 'revoked', 'revoked_reason',
      'revoked_at', 'created_at', 'updated_at'
    ]
  }
};

// Comprehensive field mappings (MongoDB -> PostgreSQL)
const FIELD_MAPPINGS = {
  // Common mappings
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  userId: 'user_id',
  farmerId: 'farmer_id',
  buyerId: 'buyer_id',
  orderId: 'order_id',
  cartId: 'cart_id',
  produceId: 'produce_id',
  categoryId: 'category_id',
  adminId: 'admin_id',
  
  // User fields
  firstName: 'first_name',
  lastName: 'last_name',
  middleName: 'middle_name',
  phoneNumber: 'primary_phone',
  userType: 'user_type',
  isPhoneVerified: 'is_phone_verified',
  isEmailVerified: 'is_email_verified',
  isActive: 'is_active',
  lastLogin: 'last_login',
  profilePicture: 'profile_picture',
  twoFA: 'two_fa', // Special case
  
  // Farmer fields
  farmName: 'farm_name',
  farmSize: 'farm_size',
  
  // Buyer fields
  businessName: 'business_name',
  loyaltyPoints: 'loyalty_points',
  deliveryPreferences: 'delivery_preferences',
  paymentMethods: 'payment_methods',
  
  // Address fields
  houseNumber: 'house_number',
  streetName: 'street_name',
  isDefault: 'is_default',
  
  // Notification fields
  userType: 'user_type',
  isRead: 'is_read',
  
  // Produce fields
  negotiablePrice: 'negotiable_price',
  minOrderQty: 'min_order_qty',
  harvestDate: 'harvest_date',
  
  // Order fields
  totalCost: 'total_cost',
  shippingFee: 'shipping_fee',
  shippingAddress: 'shipping_address',
  paymentMethod: 'payment_method',
  paymentStatus: 'payment_status',
  paymentDate: 'payment_date',
  shippedDate: 'shipped_date',
  finalTotalCost: 'final_total_cost',
  
  // Transaction fields
  totalAmount: 'total_amount',
  paymentMethod: 'payment_method',
  paymentType: 'payment_type',
  gatewayReference: 'gateway_reference',
  
  // Cart fields
  totalCost: 'total_cost',
  
  // Cart item fields
  unitPrice: 'unit_price',
  totalPrice: 'total_price',
  
  // Payment fields
  paymentMethod: 'payment_method',
  paymentStatus: 'payment_status',
  gatewayReference: 'gateway_reference',
  
  // Preference fields
  otpDestination: 'otp_destination',
  receivePromotions: 'receive_promotions',
  enable2fa: 'enable_2fa',
  generalUpdates: 'general_updates',
  orderUpdates: 'order_updates',
  transactionUpdates: 'transaction_updates',
  paymentUpdates: 'payment_updates',
  deliveryUpdates: 'delivery_updates',
  
  // Weekly price fields
  produceName: 'produce_name',
  avgPrice: 'avg_price',
  minPrice: 'min_price',
  maxPrice: 'max_price',
  weekNumber: 'week_number',
  weekEndDate: 'week_end_date',
  marketData: 'market_data',
  isActive: 'is_active',
  
  // Admin token fields
  expiresAt: 'expires_at',
  revokedReason: 'revoked_reason',
  revokedAt: 'revoked_at'
};

// Helper function to convert camelCase to snake_case
function camelToSnakeCase(str) {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

// Helper function to convert snake_case to camelCase
function snakeToCamelCase(str) {
  return str.replace(/_([a-z])/g, (g) => g[1].toUpperCase());
}

async function comprehensiveAudit() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(MONGODB_DATABASE);
    const collections = await db.listCollections().toArray();
    
    console.log('\n=== COMPREHENSIVE ENTITY AUDIT ===\n');
    
    // First, let's analyze all PostgreSQL entities
    console.log('📋 POSTGRESQL ENTITIES SUMMARY:');
    console.log('================================');
    for (const [entityName, entity] of Object.entries(POSTGRES_ENTITIES)) {
      console.log(`${entityName}: ${entity.fields.length} fields`);
    }
    
    console.log('\n📊 MONGODB COLLECTIONS SUMMARY:');
    console.log('================================');
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      console.log(`${collection.name}: ${count} documents`);
    }
    
    console.log('\n🔍 DETAILED FIELD-BY-FIELD ANALYSIS:');
    console.log('=====================================');
    
    for (const collection of collections) {
      const collectionName = collection.name;
      console.log(`\n--- Collection: ${collectionName} ---`);
      
      // Get a sample document to understand the structure
      const sampleDoc = await db.collection(collectionName).findOne();
      
      if (sampleDoc) {
        const mongoFields = Object.keys(sampleDoc).filter(key => key !== '_id' && key !== '__v');
        console.log(`MongoDB fields (${mongoFields.length}):`, mongoFields);
        
        // Check if we have a corresponding PostgreSQL entity
        const postgresEntity = POSTGRES_ENTITIES[collectionName];
        if (postgresEntity) {
          console.log(`PostgreSQL table: ${postgresEntity.table} (${postgresEntity.fields.length} fields)`);
          console.log('PostgreSQL fields:', postgresEntity.fields);
          
          // Analyze field mappings
          console.log('\n📝 FIELD MAPPING ANALYSIS:');
          let mappedCount = 0;
          let unmappedCount = 0;
          
          for (const mongoField of mongoFields) {
            // Try explicit mapping first, then camelCase conversion, then direct match
            let postgresField = FIELD_MAPPINGS[mongoField];
            let mappingType = 'explicit';
            
            if (!postgresField) {
              postgresField = camelToSnakeCase(mongoField);
              mappingType = 'camelCase';
            }
            if (!postgresField) {
              postgresField = mongoField; // Direct match
              mappingType = 'direct';
            }
            
            const isInPostgres = postgresEntity.fields.includes(postgresField);
            const status = isInPostgres ? '✅' : '❌';
            
            if (isInPostgres) {
              mappedCount++;
            } else {
              unmappedCount++;
            }
            
            console.log(`  ${status} ${mongoField} -> ${postgresField} (${mappingType}) ${isInPostgres ? '' : '(NOT FOUND)'}`);
          }
          
          // Check for missing fields in MongoDB
          console.log('\n🔍 MISSING IN MONGODB:');
          let missingCount = 0;
          for (const postgresField of postgresEntity.fields) {
            // Find the MongoDB field that maps to this PostgreSQL field
            const mongoField = Object.keys(FIELD_MAPPINGS).find(key => FIELD_MAPPINGS[key] === postgresField);
            const directMatch = mongoFields.includes(postgresField);
            const mappedMatch = mongoField && mongoFields.includes(mongoField);
            const camelCaseField = snakeToCamelCase(postgresField);
            const camelCaseMatch = mongoFields.includes(camelCaseField);
            
            if (!directMatch && !mappedMatch && !camelCaseMatch) {
              console.log(`  ❌ ${postgresField} (PostgreSQL only)`);
              missingCount++;
            }
          }
          
          // Summary for this collection
          const totalFields = postgresEntity.fields.length;
          const matchRate = ((mappedCount / totalFields) * 100).toFixed(1);
          console.log(`\n📊 SUMMARY: ${mappedCount}/${totalFields} fields mapped (${matchRate}% match rate)`);
          
          if (matchRate >= 80) {
            console.log('✅ EXCELLENT: High field mapping success');
          } else if (matchRate >= 50) {
            console.log('⚠️  GOOD: Moderate field mapping success');
          } else {
            console.log('❌ POOR: Low field mapping success');
          }
          
        } else {
          console.log('❌ No corresponding PostgreSQL entity found');
          
          // Check if there's a similar entity name
          const similarEntities = Object.keys(POSTGRES_ENTITIES).filter(name => 
            name.includes(collectionName) || collectionName.includes(name)
          );
          
          if (similarEntities.length > 0) {
            console.log(`💡 Similar entities found: ${similarEntities.join(', ')}`);
          }
        }
      } else {
        console.log('Collection is empty');
      }
      
      // Get collection stats
      const count = await db.collection(collectionName).countDocuments();
      console.log(`📈 Document count: ${count}`);
    }
    
    // Overall summary
    console.log('\n🎯 OVERALL SUMMARY:');
    console.log('===================');
    
    let totalCollections = collections.length;
    let mappedCollections = 0;
    let emptyCollections = 0;
    
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      if (count === 0) {
        emptyCollections++;
      } else if (POSTGRES_ENTITIES[collection.name]) {
        mappedCollections++;
      }
    }
    
    console.log(`Total collections: ${totalCollections}`);
    console.log(`Mapped to PostgreSQL: ${mappedCollections}`);
    console.log(`Empty collections: ${emptyCollections}`);
    console.log(`Unmapped collections: ${totalCollections - mappedCollections - emptyCollections}`);
    
  } catch (error) {
    console.error('Error during comprehensive audit:', error);
  } finally {
    await client.close();
  }
}

// Run the comprehensive audit
comprehensiveAudit().catch(console.error); 