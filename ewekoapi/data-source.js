const { DataSource } = require('typeorm');
require('dotenv').config();

const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'ewekoadmin',
  password: process.env.DB_PASSWORD || 'ewekoadmin',
  database: process.env.DB_NAME || 'ewekodb',
  schema: 'public',
  entities: ['dist/**/*.entity.js'],
  migrations: ['dist/migration/*.js'],
  synchronize: false,
  logging: true,
});

module.exports = { AppDataSource };
