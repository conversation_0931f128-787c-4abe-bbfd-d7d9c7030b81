import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
dotenv.config();

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'ewekoadmin',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'ewekodb',
  schema: 'public',
  entities: ['src/**/*.entity.ts'],
  migrations: ['src/migration/*.ts'],
  synchronize: true,
}); 