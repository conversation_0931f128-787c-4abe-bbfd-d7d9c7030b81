const { MongoClient } = require('mongodb');
const { Client } = require('pg');

// MongoDB connection
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DATABASE = 'ewekoapi';

// PostgreSQL connection
const PG_CONFIG = {
  host: 'localhost',
  port: 5432,
  user: 'ewekoadmin',
  password: '',
  database: 'ewekodb',
};

async function generateDataValidationReport() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);

  try {
    await mongoClient.connect();
    await pgClient.connect();
    console.log('🔗 Connected to both databases');

    const db = mongoClient.db(MONGODB_DATABASE);

    console.log('\n' + '='.repeat(80));
    console.log('📊 COMPREHENSIVE DATA VALIDATION & COMPARISON REPORT');
    console.log('='.repeat(80));

    // Users Analysis
    console.log('\n👥 USERS MIGRATION ANALYSIS:');
    console.log('-'.repeat(50));
    
    const mongoBuyers = await db.collection('buyers').countDocuments();
    const mongoFarmers = await db.collection('farmers').countDocuments();
    const mongoTotalUsers = mongoBuyers + mongoFarmers;
    
    const pgUsersResult = await pgClient.query('SELECT COUNT(*) as count, user_type FROM users GROUP BY user_type');
    const pgBuyers = pgUsersResult.rows.find(r => r.user_type === 'BUYER')?.count || 0;
    const pgFarmers = pgUsersResult.rows.find(r => r.user_type === 'FARMER')?.count || 0;
    const pgTotalUsers = parseInt(pgBuyers) + parseInt(pgFarmers);

    console.log(`MongoDB: ${mongoBuyers} buyers + ${mongoFarmers} farmers = ${mongoTotalUsers} total`);
    console.log(`PostgreSQL: ${pgBuyers} buyers + ${pgFarmers} farmers = ${pgTotalUsers} total`);
    console.log(`Migration Success Rate: ${((pgTotalUsers / mongoTotalUsers) * 100).toFixed(1)}%`);

    // Profile Tables Analysis
    console.log('\n📋 PROFILE TABLES ANALYSIS:');
    console.log('-'.repeat(50));
    
    const pgBuyerProfiles = await pgClient.query('SELECT COUNT(*) as count FROM buyers');
    const pgFarmerProfiles = await pgClient.query('SELECT COUNT(*) as count FROM farmers');
    
    console.log(`Buyer profiles: ${pgBuyerProfiles.rows[0].count}/${pgBuyers} (${((pgBuyerProfiles.rows[0].count / pgBuyers) * 100).toFixed(1)}%)`);
    console.log(`Farmer profiles: ${pgFarmerProfiles.rows[0].count}/${pgFarmers} (${((pgFarmerProfiles.rows[0].count / pgFarmers) * 100).toFixed(1)}%)`);

    // Related Data Analysis
    console.log('\n🔗 RELATED DATA ANALYSIS:');
    console.log('-'.repeat(50));
    
    const pgPreferences = await pgClient.query('SELECT COUNT(*) as count FROM preferences');
    const pgNotifications = await pgClient.query('SELECT COUNT(*) as count FROM notifications');
    const pgAddresses = await pgClient.query('SELECT COUNT(*) as count FROM addresses');
    
    console.log(`Preferences: ${pgPreferences.rows[0].count}/${pgTotalUsers} (${((pgPreferences.rows[0].count / pgTotalUsers) * 100).toFixed(1)}%)`);
    console.log(`Notifications: ${pgNotifications.rows[0].count}/${pgTotalUsers} (${((pgNotifications.rows[0].count / pgTotalUsers) * 100).toFixed(1)}%)`);
    console.log(`Addresses: ${pgAddresses.rows[0].count} migrated`);

    // Categories and Produces
    console.log('\n🏷️  CATEGORIES & PRODUCES ANALYSIS:');
    console.log('-'.repeat(50));
    
    const mongoCategories = await db.collection('categories').countDocuments();
    const mongoProduces = await db.collection('produces').countDocuments();
    
    const pgCategories = await pgClient.query('SELECT COUNT(*) as count FROM categories');
    const pgProduces = await pgClient.query('SELECT COUNT(*) as count FROM produces');
    
    console.log(`Categories: MongoDB(${mongoCategories}) → PostgreSQL(${pgCategories.rows[0].count}) - ${mongoCategories === parseInt(pgCategories.rows[0].count) ? '✅ COMPLETE' : '⚠️  PARTIAL'}`);
    console.log(`Produces: MongoDB(${mongoProduces}) → PostgreSQL(${pgProduces.rows[0].count}) - ${mongoProduces === parseInt(pgProduces.rows[0].count) ? '✅ COMPLETE' : '⚠️  PARTIAL'}`);

    // Wallets Analysis
    console.log('\n💰 WALLETS ANALYSIS:');
    console.log('-'.repeat(50));
    
    const mongoWallets = await db.collection('wallets').countDocuments();
    const pgWallets = await pgClient.query('SELECT COUNT(*) as count FROM wallets');
    
    console.log(`Wallets: MongoDB(${mongoWallets}) → PostgreSQL(${pgWallets.rows[0].count}) - ${mongoWallets === parseInt(pgWallets.rows[0].count) ? '✅ COMPLETE' : '⚠️  PARTIAL'}`);

    // Data Integrity Checks
    console.log('\n🔍 DATA INTEGRITY CHECKS:');
    console.log('-'.repeat(50));
    
    // Check for orphaned records
    const orphanedBuyers = await pgClient.query(`
      SELECT COUNT(*) as count FROM buyers b 
      LEFT JOIN users u ON b.user_id = u.id 
      WHERE u.id IS NULL
    `);
    
    const orphanedFarmers = await pgClient.query(`
      SELECT COUNT(*) as count FROM farmers f 
      LEFT JOIN users u ON f.user_id = u.id 
      WHERE u.id IS NULL
    `);
    
    const orphanedPreferences = await pgClient.query(`
      SELECT COUNT(*) as count FROM preferences p 
      LEFT JOIN users u ON p.user_id = u.id 
      WHERE u.id IS NULL
    `);
    
    console.log(`Orphaned buyer profiles: ${orphanedBuyers.rows[0].count}`);
    console.log(`Orphaned farmer profiles: ${orphanedFarmers.rows[0].count}`);
    console.log(`Orphaned preferences: ${orphanedPreferences.rows[0].count}`);

    // Email uniqueness check
    const duplicateEmails = await pgClient.query(`
      SELECT email, COUNT(*) as count 
      FROM users 
      GROUP BY email 
      HAVING COUNT(*) > 1
    `);
    
    console.log(`Duplicate emails: ${duplicateEmails.rows.length}`);
    if (duplicateEmails.rows.length > 0) {
      duplicateEmails.rows.forEach(row => {
        console.log(`  - ${row.email}: ${row.count} occurrences`);
      });
    }

    // Field Mapping Accuracy
    console.log('\n🗺️  FIELD MAPPING ACCURACY:');
    console.log('-'.repeat(50));
    
    // Sample a few users to check field mapping
    const sampleUsers = await pgClient.query(`
      SELECT u.email, u.first_name, u.last_name, u.user_type, u.primary_phone
      FROM users u 
      LIMIT 5
    `);
    
    console.log('Sample migrated users:');
    sampleUsers.rows.forEach(user => {
      console.log(`  - ${user.email} (${user.user_type}): ${user.first_name} ${user.last_name}, Phone: ${user.primary_phone || 'N/A'}`);
    });

    // Missing Critical Data
    console.log('\n❌ MISSING CRITICAL DATA:');
    console.log('-'.repeat(50));
    
    const usersWithoutPhone = await pgClient.query(`
      SELECT COUNT(*) as count FROM users WHERE primary_phone IS NULL OR primary_phone = ''
    `);
    
    const usersWithoutName = await pgClient.query(`
      SELECT COUNT(*) as count FROM users WHERE first_name IS NULL OR first_name = '' OR last_name IS NULL OR last_name = ''
    `);
    
    console.log(`Users without phone: ${usersWithoutPhone.rows[0].count}`);
    console.log(`Users without complete names: ${usersWithoutName.rows[0].count}`);

    // Migration Success Summary
    console.log('\n📈 MIGRATION SUCCESS SUMMARY:');
    console.log('-'.repeat(50));
    
    const totalMigrationItems = [
      { name: 'Users', mongo: mongoTotalUsers, pg: pgTotalUsers },
      { name: 'Categories', mongo: mongoCategories, pg: parseInt(pgCategories.rows[0].count) },
      { name: 'Preferences', mongo: pgTotalUsers, pg: parseInt(pgPreferences.rows[0].count) },
      { name: 'Notifications', mongo: pgTotalUsers, pg: parseInt(pgNotifications.rows[0].count) }
    ];
    
    let totalSuccess = 0;
    let totalItems = totalMigrationItems.length;
    
    totalMigrationItems.forEach(item => {
      const successRate = (item.pg / item.mongo) * 100;
      const status = successRate >= 100 ? '✅' : successRate >= 90 ? '⚠️' : '❌';
      console.log(`${status} ${item.name}: ${successRate.toFixed(1)}% (${item.pg}/${item.mongo})`);
      if (successRate >= 90) totalSuccess++;
    });
    
    const overallSuccess = (totalSuccess / totalItems) * 100;
    console.log(`\n🎯 Overall Migration Success: ${overallSuccess.toFixed(1)}% (${totalSuccess}/${totalItems} categories)`);

    console.log('\n' + '='.repeat(80));
    console.log('📋 RECOMMENDATIONS');
    console.log('='.repeat(80));
    
    if (parseInt(pgProduces.rows[0].count) === 0) {
      console.log('❗ CRITICAL: No produces migrated - need to fix farmer_id mapping');
    }
    
    if (parseInt(pgWallets.rows[0].count) === 0) {
      console.log('❗ IMPORTANT: No wallets migrated - farmers need wallet records');
    }
    
    if (parseInt(pgAddresses.rows[0].count) === 0) {
      console.log('❗ IMPORTANT: No addresses migrated - users need address records');
    }
    
    if (orphanedBuyers.rows[0].count > 0 || orphanedFarmers.rows[0].count > 0) {
      console.log('❗ WARNING: Orphaned profile records detected - data integrity issue');
    }
    
    console.log('\n✅ MIGRATION STATUS: ' + (overallSuccess >= 90 ? 'READY FOR TESTING' : 'NEEDS FIXES'));

  } catch (error) {
    console.error('❌ Data validation failed:', error);
    throw error;
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

// Run the validation
if (require.main === module) {
  generateDataValidationReport().catch(console.error);
}

module.exports = { generateDataValidationReport };
