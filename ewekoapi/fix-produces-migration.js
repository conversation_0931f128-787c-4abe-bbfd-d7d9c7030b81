const { MongoClient } = require('mongodb');
const { Client } = require('pg');
const crypto = require('crypto');

// MongoDB connection
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DATABASE = 'ewekoapi';

// PostgreSQL connection
const PG_CONFIG = {
  host: 'localhost',
  port: 5432,
  user: 'ewekoadmin',
  password: '',
  database: 'ewekodb',
};

async function fixProducesMigration() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);

  try {
    await mongoClient.connect();
    await pgClient.connect();
    console.log('🔗 Connected to both databases');

    const db = mongoClient.db(MONGODB_DATABASE);

    // Get all produces from MongoDB
    const mongoProduces = await db.collection('produces').find({}).toArray();
    console.log(`📊 Found ${mongoProduces.length} produces in MongoDB`);

    // Get category and farmer mappings from PostgreSQL
    const categoriesResult = await pgClient.query('SELECT id, name FROM categories');
    const farmersResult = await pgClient.query('SELECT f.id, u.email FROM farmers f JOIN users u ON f.user_id = u.id');

    const categoryMap = new Map();
    categoriesResult.rows.forEach(cat => categoryMap.set(cat.name, cat.id));

    const farmerEmailMap = new Map();
    farmersResult.rows.forEach(farmer => farmerEmailMap.set(farmer.email, farmer.id));

    console.log(`📋 Found ${categoriesResult.rows.length} categories and ${farmersResult.rows.length} farmers in PostgreSQL`);

    // Get farmers from MongoDB to create email mapping
    const mongoFarmers = await db.collection('farmers').find({}).toArray();
    const mongoFarmerIdToEmailMap = new Map();

    for (const farmer of mongoFarmers) {
      if (farmer.email) {
        mongoFarmerIdToEmailMap.set(farmer._id.toString(), farmer.email);
      }
    }

    let migratedCount = 0;
    let errorCount = 0;

    for (const produce of mongoProduces) {
      try {
        // Find farmer by mapping MongoDB farmer ID to email, then email to PostgreSQL farmer ID
        let farmerId = null;
        
        if (produce.farmer) {
          const farmerEmail = mongoFarmerIdToEmailMap.get(produce.farmer.toString());
          if (farmerEmail) {
            farmerId = farmerEmailMap.get(farmerEmail);
          }
        }

        // Find category
        let categoryId = null;
        if (produce.category) {
          // Try to find category by name
          const categoryName = produce.category;
          categoryId = categoryMap.get(categoryName);
        }

        // If we still don't have farmerId, assign to first available farmer
        if (!farmerId && farmersResult.rows.length > 0) {
          farmerId = farmersResult.rows[0].id;
          console.log(`⚠️  No farmer mapping found for produce ${produce.name}, assigning to first farmer`);
        }

        // If we still don't have categoryId, assign to first available category
        if (!categoryId && categoriesResult.rows.length > 0) {
          categoryId = categoriesResult.rows[0].id;
          console.log(`⚠️  No category mapping found for produce ${produce.name}, assigning to first category`);
        }

        if (!farmerId) {
          console.error(`❌ Cannot migrate produce ${produce.name}: no farmer available`);
          errorCount++;
          continue;
        }

        if (!categoryId) {
          console.error(`❌ Cannot migrate produce ${produce.name}: no category available`);
          errorCount++;
          continue;
        }

        const produceData = {
          id: crypto.randomUUID(),
          name: produce.name,
          slug: produce.slug || produce.name.toLowerCase().replace(/\s+/g, '-'),
          description: produce.description || null,
          price: parseFloat(produce.price) || 0,
          negotiable_price: parseFloat(produce.negotiablePrice || produce.price) || 0,
          min_order_qty: parseInt(produce.minOrderQty || 1),
          stock: parseInt(produce.stock) || 0,
          category_id: categoryId,
          farmer_id: farmerId,
          images: JSON.stringify(produce.images || []),
          harvest_date: produce.harvestDate || null,
          created_at: produce.createdAt || new Date(),
          updated_at: produce.updatedAt || new Date()
        };

        const insertProduceQuery = `
          INSERT INTO produces (
            id, name, slug, description, price, negotiable_price, min_order_qty, stock, 
            category_id, farmer_id, images, harvest_date, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        `;

        await pgClient.query(insertProduceQuery, [
          produceData.id, produceData.name, produceData.slug, produceData.description, 
          produceData.price, produceData.negotiable_price, produceData.min_order_qty, 
          produceData.stock, produceData.category_id, produceData.farmer_id,
          produceData.images, produceData.harvest_date, produceData.created_at, produceData.updated_at
        ]);

        migratedCount++;
        console.log(`✅ Migrated produce: ${produceData.name} (Farmer: ${farmerId}, Category: ${categoryId})`);

      } catch (error) {
        errorCount++;
        console.error(`❌ Error migrating produce ${produce.name}:`, error.message);
      }
    }

    console.log(`\n📊 PRODUCES MIGRATION SUMMARY:`);
    console.log(`✅ Successfully migrated: ${migratedCount} produces`);
    console.log(`❌ Errors: ${errorCount} produces`);

  } catch (error) {
    console.error('❌ Produces migration failed:', error);
    throw error;
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

// Run the migration
if (require.main === module) {
  fixProducesMigration().catch(console.error);
}

module.exports = { fixProducesMigration };
