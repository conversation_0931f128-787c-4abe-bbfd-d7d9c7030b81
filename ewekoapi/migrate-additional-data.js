const { Client } = require('pg');
const { MongoClient } = require('mongodb');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// PostgreSQL connection
const pgClient = new Client({
  host: 'localhost',
  port: 5432,
  database: 'ewekodb',
  user: 'ewekoadmin',
  password: ''
});

// MongoDB connection
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DATABASE = 'ewekoapi';

const DRY_RUN = false; // Set to true for dry run, false to actually migrate

async function migrateAdditionalData() {
  const mongoClient = new MongoClient(MONGODB_URI);
  
  try {
    // Connect to both databases
    await pgClient.connect();
    await mongoClient.connect();
    console.log('🔗 Connected to PostgreSQL and MongoDB');
    
    const db = mongoClient.db(MONGODB_DATABASE);
    
    // Get user ID mapping from the migration file (MongoDB _id to PostgreSQL UUID)
    console.log('📊 Building user ID mapping...');
    const migratedUsers = JSON.parse(fs.readFileSync('users-to-migrate-with-passwords.json', 'utf8'));

    const userIdMap = {};
    migratedUsers.forEach(user => {
      userIdMap[user.mongo_id] = user.id;
    });

    console.log(`📋 Built mapping for ${Object.keys(userIdMap).length} users`);
    
    // Migration statistics
    const stats = {
      addresses: { total: 0, success: 0, errors: 0 },
      notifications: { total: 0, success: 0, errors: 0 },
      categories: { total: 0, success: 0, errors: 0 },
      produces: { total: 0, success: 0, errors: 0 },
      orders: { total: 0, success: 0, errors: 0 },
      order_items: { total: 0, success: 0, errors: 0 },
      transactions: { total: 0, success: 0, errors: 0 },
      payments: { total: 0, success: 0, errors: 0 },
      carts: { total: 0, success: 0, errors: 0 },
      cart_items: { total: 0, success: 0, errors: 0 },
      preferences: { total: 0, success: 0, errors: 0 }
    };
    
    const errors = [];
    
    // Helper function to log errors
    function logError(table, item, error) {
      stats[table].errors++;
      errors.push({
        table,
        item_id: item._id || item.id,
        error: error.message,
        item_data: item
      });
      console.log(`❌ Error migrating ${table} ${item._id || item.id}: ${error.message}`);
    }
    
    // 1. MIGRATE ADDRESSES
    console.log('\n🏠 MIGRATING ADDRESSES...');
    const addresses = await db.collection('addresses').find({}).toArray();
    stats.addresses.total = addresses.length;
    console.log(`Found ${addresses.length} addresses`);
    
    for (const address of addresses) {
      try {
        const userId = userIdMap[address.userId?.toString()] || userIdMap[address.user_id?.toString()];
        if (!userId) {
          console.log(`⚠️  Skipping address ${address._id} - user not found`);
          continue;
        }
        
        const addressData = {
          id: uuidv4(),
          user_id: userId,
          house_number: address.houseNumber || address.house_number || '',
          street_name: address.streetName || address.street_name || '',
          community: address.community || '',
          lga: address.lga || '',
          state: address.state || '',
          country: address.country || 'Nigeria',
          is_default: address.isDefault || address.is_default || false,
          created_at: address.createdAt || address.created_at || new Date(),
          updated_at: address.updatedAt || address.updated_at || new Date()
        };
        
        if (!DRY_RUN) {
          const query = `
            INSERT INTO addresses (
              id, user_id, house_number, street_name, community, lga, state, country, is_default, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
          `;
          const values = Object.values(addressData);
          await pgClient.query(query, values);
        }
        
        stats.addresses.success++;
        
        if (stats.addresses.success % 10 === 0) {
          console.log(`✅ Migrated ${stats.addresses.success} addresses...`);
        }
        
      } catch (error) {
        logError('addresses', address, error);
      }
    }
    
    // 2. MIGRATE NOTIFICATIONS
    console.log('\n🔔 MIGRATING NOTIFICATIONS...');
    const notifications = await db.collection('notifications').find({}).toArray();
    stats.notifications.total = notifications.length;
    console.log(`Found ${notifications.length} notifications`);
    
    for (const notification of notifications) {
      try {
        const userId = userIdMap[notification.userId?.toString()] || userIdMap[notification.user_id?.toString()];
        if (!userId) {
          console.log(`⚠️  Skipping notification ${notification._id} - user not found`);
          continue;
        }
        
        const notificationData = {
          id: uuidv4(),
          user_id: userId,
          user_type: notification.userType || notification.user_type || null,
          subject: notification.subject || notification.title || '',
          message: notification.message || '',
          trigger: notification.trigger || null,
          is_read: notification.isRead || notification.is_read || false,
          created_at: notification.createdAt || notification.created_at || new Date(),
          updated_at: notification.updatedAt || notification.updated_at || new Date()
        };
        
        if (!DRY_RUN) {
          const query = `
            INSERT INTO notifications (
              id, user_id, user_type, subject, message, trigger, is_read, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          `;
          const values = Object.values(notificationData);
          await pgClient.query(query, values);
        }
        
        stats.notifications.success++;
        
        if (stats.notifications.success % 10 === 0) {
          console.log(`✅ Migrated ${stats.notifications.success} notifications...`);
        }
        
      } catch (error) {
        logError('notifications', notification, error);
      }
    }
    
    // 3. MIGRATE CATEGORIES
    console.log('\n📂 MIGRATING CATEGORIES...');
    const categories = await db.collection('categories').find({}).toArray();
    stats.categories.total = categories.length;
    console.log(`Found ${categories.length} categories`);
    
    const categoryIdMap = {};
    
    for (const category of categories) {
      try {
        const categoryId = uuidv4();
        categoryIdMap[category._id.toString()] = categoryId;
        
        const categoryData = {
          id: categoryId,
          name: category.name || '',
          slug: category.slug || category.name?.toLowerCase().replace(/\s+/g, '-') || '',
          description: category.description || null,
          image: category.image || null,
          is_active: category.isActive !== false,
          created_at: category.createdAt || category.created_at || new Date(),
          updated_at: category.updatedAt || category.updated_at || new Date()
        };
        
        if (!DRY_RUN) {
          const query = `
            INSERT INTO categories (
              id, name, slug, description, image, is_active, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          `;
          const values = Object.values(categoryData);
          await pgClient.query(query, values);
        }
        
        stats.categories.success++;
        
      } catch (error) {
        logError('categories', category, error);
      }
    }
    
    console.log(`✅ Migrated ${stats.categories.success} categories`);

    // 4. MIGRATE PRODUCES
    console.log('\n🌾 MIGRATING PRODUCES...');
    const produces = await db.collection('produces').find({}).toArray();
    stats.produces.total = produces.length;
    console.log(`Found ${produces.length} produces`);

    // Get farmer ID mapping
    const farmerMappingQuery = 'SELECT id, user_id FROM farmers';
    const farmerMappingResult = await pgClient.query(farmerMappingQuery);
    const farmerIdMap = {};
    farmerMappingResult.rows.forEach(row => {
      farmerIdMap[row.user_id] = row.id;
    });

    const produceIdMap = {};

    for (const produce of produces) {
      try {
        const userId = userIdMap[produce.farmerId?.toString()] || userIdMap[produce.farmer_id?.toString()];
        const farmerId = farmerIdMap[userId];

        if (!farmerId) {
          console.log(`⚠️  Skipping produce ${produce._id} - farmer not found`);
          continue;
        }

        const categoryId = categoryIdMap[produce.categoryId?.toString()] || categoryIdMap[produce.category_id?.toString()];
        if (!categoryId) {
          console.log(`⚠️  Skipping produce ${produce._id} - category not found`);
          continue;
        }

        const produceId = uuidv4();
        produceIdMap[produce._id.toString()] = produceId;

        const produceData = {
          id: produceId,
          farmer_id: farmerId,
          category_id: categoryId,
          name: produce.name || '',
          description: produce.description || null,
          price: produce.price || 0,
          negotiable_price: produce.negotiablePrice || produce.negotiable_price || produce.price || 0,
          min_order_qty: produce.minOrderQty || produce.min_order_qty || 1,
          stock: produce.stock || produce.quantity || 0,
          images: produce.images ? JSON.stringify(produce.images) : null,
          slug: produce.slug || produce.name?.toLowerCase().replace(/\s+/g, '-') || '',
          harvest_date: produce.harvestDate || produce.harvest_date || null,
          created_at: produce.createdAt || produce.created_at || new Date(),
          updated_at: produce.updatedAt || produce.updated_at || new Date()
        };

        if (!DRY_RUN) {
          const query = `
            INSERT INTO produces (
              id, farmer_id, category_id, name, description, price, negotiable_price,
              min_order_qty, stock, images, slug, harvest_date, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
          `;
          const values = Object.values(produceData);
          await pgClient.query(query, values);
        }

        stats.produces.success++;

        if (stats.produces.success % 10 === 0) {
          console.log(`✅ Migrated ${stats.produces.success} produces...`);
        }

      } catch (error) {
        logError('produces', produce, error);
      }
    }

    console.log(`✅ Migrated ${stats.produces.success} produces`);

    // 5. MIGRATE PREFERENCES
    console.log('\n⚙️ MIGRATING PREFERENCES...');
    const preferences = await db.collection('preferences').find({}).toArray();
    stats.preferences.total = preferences.length;
    console.log(`Found ${preferences.length} preferences`);

    for (const preference of preferences) {
      try {
        const userId = userIdMap[preference.userId?.toString()] || userIdMap[preference.user_id?.toString()];
        if (!userId) {
          console.log(`⚠️  Skipping preference ${preference._id} - user not found`);
          continue;
        }

        const preferenceData = {
          id: uuidv4(),
          user_id: userId,
          otp_destination: preference.otpDestination || preference.otp_destination || 'EMAIL',
          receive_promotions: preference.receivePromotions !== false,
          enable_2fa: preference.enable2FA || preference.enable_2fa || false,
          general_updates: preference.generalUpdates !== false,
          order_updates: preference.orderUpdates !== false,
          transaction_updates: preference.transactionUpdates !== false,
          payment_updates: preference.paymentUpdates !== false,
          delivery_updates: preference.deliveryUpdates !== false,
          created_at: preference.createdAt || preference.created_at || new Date(),
          updated_at: preference.updatedAt || preference.updated_at || new Date()
        };

        if (!DRY_RUN) {
          const query = `
            INSERT INTO preferences (
              id, user_id, otp_destination, receive_promotions, enable_2fa, general_updates,
              order_updates, transaction_updates, payment_updates, delivery_updates, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
          `;
          const values = Object.values(preferenceData);
          await pgClient.query(query, values);
        }

        stats.preferences.success++;

      } catch (error) {
        logError('preferences', preference, error);
      }
    }

    console.log(`✅ Migrated ${stats.preferences.success} preferences`);

    // Save progress and continue with more tables...
    console.log('\n📊 MIGRATION PROGRESS SO FAR:');
    Object.entries(stats).forEach(([table, stat]) => {
      if (stat.total > 0) {
        console.log(`${table}: ${stat.success}/${stat.total} (${stat.errors} errors)`);
      }
    });

    // Save intermediate results
    const results = {
      migration_date: new Date().toISOString(),
      dry_run: DRY_RUN,
      user_mapping_count: Object.keys(userIdMap).length,
      category_mapping_count: Object.keys(categoryIdMap).length,
      produce_mapping_count: Object.keys(produceIdMap).length,
      statistics: stats,
      errors: errors.slice(0, 20) // Save first 20 errors
    };

    fs.writeFileSync('migration-additional-data-results.json', JSON.stringify(results, null, 2));
    console.log('💾 Intermediate results saved');

    // 6. MIGRATE CARTS
    console.log('\n🛒 MIGRATING CARTS...');
    const carts = await db.collection('carts').find({}).toArray();
    stats.carts.total = carts.length;
    console.log(`Found ${carts.length} carts`);

    const cartIdMap = {};

    for (const cart of carts) {
      try {
        const userId = userIdMap[cart.userId?.toString()] || userIdMap[cart.user_id?.toString()];
        if (!userId) {
          console.log(`⚠️  Skipping cart ${cart._id} - user not found`);
          continue;
        }

        const cartId = uuidv4();
        cartIdMap[cart._id.toString()] = cartId;

        const cartData = {
          id: cartId,
          user_id: userId,
          total_cost: cart.totalCost || cart.total_cost || 0,
          active: cart.active !== false,
          created_at: cart.createdAt || cart.created_at || new Date(),
          updated_at: cart.updatedAt || cart.updated_at || new Date()
        };

        if (!DRY_RUN) {
          const query = `
            INSERT INTO carts (id, user_id, total_cost, active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6)
          `;
          const values = Object.values(cartData);
          await pgClient.query(query, values);
        }

        stats.carts.success++;

      } catch (error) {
        logError('carts', cart, error);
      }
    }

    console.log(`✅ Migrated ${stats.carts.success} carts`);

    // 7. MIGRATE CART ITEMS
    console.log('\n🛍️ MIGRATING CART ITEMS...');
    const cartItems = await db.collection('cartitems').find({}).toArray();
    stats.cart_items.total = cartItems.length;
    console.log(`Found ${cartItems.length} cart items`);

    for (const cartItem of cartItems) {
      try {
        const cartId = cartIdMap[cartItem.cartId?.toString()] || cartIdMap[cartItem.cart_id?.toString()];
        const produceId = produceIdMap[cartItem.produceId?.toString()] || produceIdMap[cartItem.produce_id?.toString()];

        if (!cartId || !produceId) {
          console.log(`⚠️  Skipping cart item ${cartItem._id} - cart or produce not found`);
          continue;
        }

        const cartItemData = {
          id: uuidv4(),
          cart_id: cartId,
          produce_id: produceId,
          quantity: cartItem.quantity || 1,
          unit_price: cartItem.unitPrice || cartItem.unit_price || 0,
          total_price: cartItem.totalPrice || cartItem.total_price || 0,
          created_at: cartItem.createdAt || cartItem.created_at || new Date(),
          updated_at: cartItem.updatedAt || cartItem.updated_at || new Date()
        };

        if (!DRY_RUN) {
          const query = `
            INSERT INTO cart_items (
              id, cart_id, produce_id, quantity, unit_price, total_price, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          `;
          const values = Object.values(cartItemData);
          await pgClient.query(query, values);
        }

        stats.cart_items.success++;

      } catch (error) {
        logError('cart_items', cartItem, error);
      }
    }

    console.log(`✅ Migrated ${stats.cart_items.success} cart items`);

    // FINAL RESULTS
    console.log('\n🎉 MIGRATION COMPLETED!');
    console.log('\n📊 FINAL STATISTICS:');
    let totalSuccess = 0;
    let totalErrors = 0;

    Object.entries(stats).forEach(([table, stat]) => {
      if (stat.total > 0) {
        console.log(`${table}: ${stat.success}/${stat.total} (${stat.errors} errors)`);
        totalSuccess += stat.success;
        totalErrors += stat.errors;
      }
    });

    console.log(`\n📈 OVERALL: ${totalSuccess} successful migrations, ${totalErrors} errors`);

    // Save final results
    const finalResults = {
      migration_date: new Date().toISOString(),
      dry_run: DRY_RUN,
      user_mapping_count: Object.keys(userIdMap).length,
      category_mapping_count: Object.keys(categoryIdMap).length,
      produce_mapping_count: Object.keys(produceIdMap).length,
      cart_mapping_count: Object.keys(cartIdMap).length,
      statistics: stats,
      total_success: totalSuccess,
      total_errors: totalErrors,
      errors: errors
    };

    fs.writeFileSync('migration-additional-data-final-results.json', JSON.stringify(finalResults, null, 2));
    console.log('💾 Final results saved to migration-additional-data-final-results.json');

    if (errors.length > 0) {
      fs.writeFileSync('migration-additional-data-errors.json', JSON.stringify(errors, null, 2));
      console.log('💾 Errors saved to migration-additional-data-errors.json');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await pgClient.end();
    await mongoClient.close();
    console.log('🔌 Database connections closed');
  }
}

migrateAdditionalData();
