const { Client } = require('pg');
const { MongoClient } = require('mongodb');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// PostgreSQL connection
const pgClient = new Client({
  host: 'localhost',
  port: 5432,
  database: 'ewekodb',
  user: 'ewekoadmin',
  password: ''
});

// MongoDB connection
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DATABASE = 'ewekoapi';

const DRY_RUN = false; // Set to true for dry run, false to actually migrate

async function migrateBusinessData() {
  const mongoClient = new MongoClient(MONGODB_URI);
  
  try {
    // Connect to both databases
    await pgClient.connect();
    await mongoClient.connect();
    console.log('🔗 Connected to PostgreSQL and MongoDB');
    
    const db = mongoClient.db(MONGODB_DATABASE);
    
    // Get user ID mapping from the migration file (MongoDB _id to PostgreSQL UUID)
    console.log('📊 Building user ID mapping...');
    const migratedUsers = JSON.parse(fs.readFileSync('users-to-migrate-with-passwords.json', 'utf8'));
    
    const userIdMap = {};
    migratedUsers.forEach(user => {
      userIdMap[user.mongo_id] = user.id;
    });
    
    console.log(`📋 Built mapping for ${Object.keys(userIdMap).length} users`);
    
    // Get farmer and buyer ID mappings from PostgreSQL
    const farmerQuery = 'SELECT id, user_id FROM farmers';
    const farmerResult = await pgClient.query(farmerQuery);
    const farmerIdMap = {};
    farmerResult.rows.forEach(row => {
      farmerIdMap[row.user_id] = row.id;
    });

    const buyerQuery = 'SELECT id, user_id FROM buyers';
    const buyerResult = await pgClient.query(buyerQuery);
    const buyerIdMap = {};
    buyerResult.rows.forEach(row => {
      buyerIdMap[row.user_id] = row.id;
    });

    console.log(`📋 Found ${farmerResult.rows.length} farmers and ${buyerResult.rows.length} buyers in PostgreSQL`);

    // Get default farmer and buyer IDs for orders that don't have proper relationships
    const defaultFarmerId = farmerResult.rows.length > 0 ? farmerResult.rows[0].id : null;
    const defaultBuyerId = buyerResult.rows.length > 0 ? buyerResult.rows[0].id : null;

    if (!defaultFarmerId || !defaultBuyerId) {
      console.log('❌ No farmers or buyers found - cannot migrate orders');
      return;
    }
    
    // Migration statistics
    const stats = {
      orders: { total: 0, success: 0, errors: 0 },
      order_items: { total: 0, success: 0, errors: 0 },
      transactions: { total: 0, success: 0, errors: 0 }
    };
    
    const errors = [];
    const orderIdMap = {};
    
    // Helper function to log errors
    function logError(table, item, error) {
      stats[table].errors++;
      errors.push({
        table,
        item_id: item._id || item.id,
        error: error.message,
        item_data: item
      });
      console.log(`❌ Error migrating ${table} ${item._id || item.id}: ${error.message}`);
    }
    
    // 1. MIGRATE ORDERS
    console.log('\n📦 MIGRATING ORDERS...');
    const orders = await db.collection('orders').find({}).toArray();
    stats.orders.total = orders.length;
    console.log(`Found ${orders.length} orders`);
    
    for (const order of orders) {
      try {
        const userId = userIdMap[order.userId?.toString()];
        if (!userId) {
          console.log(`⚠️  Skipping order ${order._id} - user not found`);
          continue;
        }

        // Check if user exists as buyer or farmer
        const buyerId = buyerIdMap[userId];
        const farmerId = farmerIdMap[userId];

        if (!buyerId && !farmerId) {
          console.log(`⚠️  Skipping order ${order._id} - user is neither buyer nor farmer`);
          continue;
        }

        const orderId = uuidv4();
        orderIdMap[order._id.toString()] = orderId;
        
        // Map MongoDB status values to PostgreSQL enum values
        const statusMap = {
          'Processing': 'processing',
          'Shipped': 'shipped',
          'Delivered': 'delivered',
          'Cancelled': 'cancelled',
          'Pending': 'pending',
          'Confirmed': 'confirmed'
        };

        const paymentStatusMap = {
          'Pending': 'pending',
          'Paid': 'completed',
          'Failed': 'failed',
          'Cancelled': 'cancelled'
        };

        const orderData = {
          id: orderId,
          buyer_id: buyerId || defaultBuyerId, // Use user's buyer ID or default buyer
          farmer_id: farmerId || defaultFarmerId, // Use user's farmer ID or default farmer
          total_cost: order.totalCost || order.total_cost || 0,
          shipping_fee: order.shippingFee || order.shipping_fee || 0,
          final_total_cost: order.finalTotalCost || order.final_total_cost || order.totalCost || 0,
          status: statusMap[order.status] || 'pending',
          payment_method: order.paymentMethod || order.payment_method || 'card',
          payment_status: paymentStatusMap[order.paymentStatus] || 'pending',
          shipping_address: order.shippingAddress ? JSON.stringify(order.shippingAddress) : null,
          shipped_date: order.shippedDate || order.shipped_date || null,
          payment_date: order.paymentDate || order.payment_date || null,
          created_at: order.createdAt || order.created_at || new Date(),
          updated_at: order.updatedAt || order.updated_at || new Date()
        };
        
        if (!DRY_RUN) {
          const query = `
            INSERT INTO orders (
              id, buyer_id, farmer_id, total_cost, shipping_fee, final_total_cost, status,
              payment_method, payment_status, shipping_address, shipped_date, payment_date,
              created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
          `;
          const values = Object.values(orderData);
          await pgClient.query(query, values);
        }
        
        stats.orders.success++;
        
        if (stats.orders.success % 10 === 0) {
          console.log(`✅ Migrated ${stats.orders.success} orders...`);
        }
        
      } catch (error) {
        logError('orders', order, error);
      }
    }
    
    console.log(`✅ Migrated ${stats.orders.success} orders`);
    
    // 2. MIGRATE ORDER ITEMS
    console.log('\n📋 MIGRATING ORDER ITEMS...');
    const orderItems = await db.collection('orderitems').find({}).toArray();
    stats.order_items.total = orderItems.length;
    console.log(`Found ${orderItems.length} order items`);
    
    for (const orderItem of orderItems) {
      try {
        // For now, we'll create order items without linking to specific orders
        // since the relationship might be complex
        const orderItemData = {
          id: uuidv4(),
          order_id: null, // Will need to be linked manually or through additional logic
          produce_id: null, // Will need to be linked to produces
          quantity: orderItem.quantity || 1,
          unit_price: orderItem.unitPrice || orderItem.unit_price || 0,
          total_price: orderItem.totalPrice || orderItem.total_price || 0,
          created_at: orderItem.createdAt || orderItem.created_at || new Date(),
          updated_at: orderItem.updatedAt || orderItem.updated_at || new Date()
        };
        
        // Skip for now since we don't have proper relationships
        console.log(`⚠️  Skipping order item ${orderItem._id} - needs relationship mapping`);
        continue;
        
      } catch (error) {
        logError('order_items', orderItem, error);
      }
    }
    
    console.log(`✅ Migrated ${stats.order_items.success} order items`);
    
    // 3. MIGRATE TRANSACTIONS
    console.log('\n💰 MIGRATING TRANSACTIONS...');
    const transactions = await db.collection('transactions').find({}).toArray();
    stats.transactions.total = transactions.length;
    console.log(`Found ${transactions.length} transactions`);
    
    for (const transaction of transactions) {
      try {
        const userId = userIdMap[transaction.user?.toString()];
        const orderId = orderIdMap[transaction.order?.toString()];
        
        if (!userId) {
          console.log(`⚠️  Skipping transaction ${transaction._id} - user not found`);
          continue;
        }
        
        // Map MongoDB status values to PostgreSQL enum values
        const transactionStatusMap = {
          'Pending': 'pending',
          'Completed': 'completed',
          'Success': 'success',
          'Failed': 'failed',
          'Cancelled': 'cancelled'
        };

        const paymentMethodMap = {
          'Card': 'card',
          'CARD': 'card',
          'Bank Transfer': 'bank_transfer',
          'Wallet': 'wallet',
          'Cash': 'cash'
        };

        const paymentTypeMap = {
          'Purchase': 'purchase',
          'ORDER': 'purchase',
          'Refund': 'refund',
          'Withdrawal': 'withdrawal',
          'Deposit': 'deposit'
        };

        const transactionData = {
          id: uuidv4(),
          user_id: userId,
          buyer_id: userId,
          farmer_id: userId, // Will be updated based on order
          order_id: orderId || null,
          total_amount: transaction.totalAmount || transaction.amount || 0,
          status: transactionStatusMap[transaction.status] || 'pending',
          payment_method: paymentMethodMap[transaction.paymentMethod] || 'card',
          payment_type: paymentTypeMap[transaction.paymentType] || 'purchase',
          reference: transaction.reference || null,
          processed: transaction.processed || false,
          created_at: transaction.createdAt || transaction.created_at || new Date(),
          updated_at: transaction.updatedAt || transaction.updated_at || new Date()
        };
        
        if (!DRY_RUN) {
          const query = `
            INSERT INTO transactions (
              id, user_id, buyer_id, farmer_id, order_id, total_amount, status, payment_method,
              payment_type, reference, processed, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
          `;
          const values = Object.values(transactionData);
          await pgClient.query(query, values);
        }
        
        stats.transactions.success++;
        
        if (stats.transactions.success % 10 === 0) {
          console.log(`✅ Migrated ${stats.transactions.success} transactions...`);
        }
        
      } catch (error) {
        logError('transactions', transaction, error);
      }
    }
    
    console.log(`✅ Migrated ${stats.transactions.success} transactions`);
    
    // FINAL RESULTS
    console.log('\n🎉 BUSINESS DATA MIGRATION COMPLETED!');
    console.log('\n📊 FINAL STATISTICS:');
    let totalSuccess = 0;
    let totalErrors = 0;
    
    Object.entries(stats).forEach(([table, stat]) => {
      if (stat.total > 0) {
        console.log(`${table}: ${stat.success}/${stat.total} (${stat.errors} errors)`);
        totalSuccess += stat.success;
        totalErrors += stat.errors;
      }
    });
    
    console.log(`\n📈 OVERALL: ${totalSuccess} successful migrations, ${totalErrors} errors`);
    
    // Save final results
    const finalResults = {
      migration_date: new Date().toISOString(),
      dry_run: DRY_RUN,
      user_mapping_count: Object.keys(userIdMap).length,
      order_mapping_count: Object.keys(orderIdMap).length,
      statistics: stats,
      total_success: totalSuccess,
      total_errors: totalErrors,
      errors: errors
    };
    
    fs.writeFileSync('migration-business-data-results.json', JSON.stringify(finalResults, null, 2));
    console.log('💾 Results saved to migration-business-data-results.json');
    
    if (errors.length > 0) {
      fs.writeFileSync('migration-business-data-errors.json', JSON.stringify(errors, null, 2));
      console.log('💾 Errors saved to migration-business-data-errors.json');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await pgClient.end();
    await mongoClient.close();
    console.log('🔌 Database connections closed');
  }
}

migrateBusinessData();
