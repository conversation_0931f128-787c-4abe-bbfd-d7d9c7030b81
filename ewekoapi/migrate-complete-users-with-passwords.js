const { Client } = require('pg');
const { MongoClient } = require('mongodb');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const zlib = require('zlib');

// PostgreSQL connection
const pgClient = new Client({
  host: 'localhost',
  port: 5432,
  database: 'ewekodb',
  user: 'ewekoadmin',
  password: 'ewekoadmin'
});

// MongoDB connection
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DATABASE = 'ewekoapi';

const DRY_RUN = false; // Set to true for dry run, false to actually migrate

// Function to try multiple decoding methods for nested data
function tryDecodeBuffer(buffer) {
  if (!buffer || !buffer.buffer) return null;

  const methods = [
    // Method 1: Direct UTF-8
    () => {
      try {
        const str = Buffer.from(buffer.buffer).toString('utf8');
        return JSON.parse(str);
      } catch (e) {
        return null;
      }
    },

    // Method 2: Try gzip decompression
    () => {
      try {
        const decompressed = zlib.gunzipSync(Buffer.from(buffer.buffer));
        const str = decompressed.toString('utf8');
        return JSON.parse(str);
      } catch (e) {
        return null;
      }
    },

    // Method 3: Try deflate decompression
    () => {
      try {
        const decompressed = zlib.inflateSync(Buffer.from(buffer.buffer));
        const str = decompressed.toString('utf8');
        return JSON.parse(str);
      } catch (e) {
        return null;
      }
    },

    // Method 4: Try base64 decode first
    () => {
      try {
        const str = Buffer.from(buffer.buffer).toString('utf8');
        const decoded = Buffer.from(str, 'base64').toString('utf8');
        return JSON.parse(decoded);
      } catch (e) {
        return null;
      }
    }
  ];

  for (const method of methods) {
    const result = method();
    if (result) return result;
  }

  return null;
}

// Function to extract complete user data including nested profile/contact/business data
function extractCompleteUserData(user) {
  let profileData = {};
  let contactData = {};
  let businessData = {};

  // Try to decode nested data if it exists
  if (user.profile) {
    const decoded = tryDecodeBuffer(user.profile);
    if (decoded) {
      profileData = decoded;
      console.log(`✅ Decoded profile data for ${user._id}: ${Object.keys(decoded).join(', ')}`);
    }
  }

  if (user.contact) {
    const decoded = tryDecodeBuffer(user.contact);
    if (decoded) {
      contactData = decoded;
      console.log(`✅ Decoded contact data for ${user._id}: ${Object.keys(decoded).join(', ')}`);
    }
  }

  if (user.business) {
    const decoded = tryDecodeBuffer(user.business);
    if (decoded) {
      businessData = decoded;
      console.log(`✅ Decoded business data for ${user._id}: ${Object.keys(decoded).join(', ')}`);
    }
  }

  // Merge all data with priority: nested data > direct fields
  return {
    // Direct fields from user document
    ...user,
    // Override with nested data if available
    ...profileData,
    ...contactData,
    ...businessData,
    // Preserve original nested data for reference
    _originalProfile: profileData,
    _originalContact: contactData,
    _originalBusiness: businessData
  };
}

async function migrateCompleteUsersWithPasswords() {
  const mongoClient = new MongoClient(MONGODB_URI);
  
  try {
    // Connect to both databases
    await pgClient.connect();
    await mongoClient.connect();
    console.log('🔗 Connected to PostgreSQL and MongoDB');
    
    const db = mongoClient.db(MONGODB_DATABASE);
    
    // Get ALL users from MongoDB (with or without passwords)
    console.log('📊 Fetching ALL users from MongoDB...');

    const buyers = await db.collection('buyers').find({}).toArray();
    const farmers = await db.collection('farmers').find({}).toArray();

    console.log(`Found ${buyers.length} buyers and ${farmers.length} farmers`);
    console.log(`Total users found: ${buyers.length + farmers.length}`);
    
    // Create a map to handle duplicates - keep the most complete record
    const userMap = new Map();
    const duplicates = [];
    
    // Process buyers - extract complete data including nested profile/contact/business
    buyers.forEach(buyer => {
      // Extract complete user data including nested fields
      const completeUser = extractCompleteUserData(buyer);

      // Use email as primary key, but fallback to _id if no email
      const key = completeUser.email || completeUser._id.toString();

      if (userMap.has(key)) {
        duplicates.push({
          key: key,
          existing: userMap.get(key),
          new: { ...completeUser, source: 'buyers' }
        });
        // Keep the more complete record
        const existing = userMap.get(key);
        const existingCompleteness = calculateCompleteness(existing);
        const newCompleteness = calculateCompleteness(completeUser);

        if (newCompleteness > existingCompleteness) {
          userMap.set(key, { ...completeUser, source: 'buyers' });
        }
      } else {
        userMap.set(key, { ...completeUser, source: 'buyers' });
      }
    });

    // Process farmers - extract complete data including nested profile/contact/business
    farmers.forEach(farmer => {
      // Extract complete user data including nested fields
      const completeUser = extractCompleteUserData(farmer);

      // Use email as primary key, but fallback to _id if no email
      const key = completeUser.email || completeUser._id.toString();

      if (userMap.has(key)) {
        duplicates.push({
          key: key,
          existing: userMap.get(key),
          new: { ...completeUser, source: 'farmers' }
        });
        // Keep the more complete record
        const existing = userMap.get(key);
        const existingCompleteness = calculateCompleteness(existing);
        const newCompleteness = calculateCompleteness(completeUser);

        if (newCompleteness > existingCompleteness) {
          userMap.set(key, { ...completeUser, source: 'farmers' });
        }
      } else {
        userMap.set(key, { ...completeUser, source: 'farmers' });
      }
    });
    
    console.log(`📋 Processed ${userMap.size} unique users`);
    console.log(`⚠️  Found ${duplicates.length} duplicate emails`);
    
    // Convert map to array and prepare for migration
    const usersToMigrate = Array.from(userMap.values()).map(user => {
      const newUuid = uuidv4();
      const userType = user.source === 'buyers' ? 'BUYER' : 'FARMER';

      return {
        id: newUuid,
        user_type: userType,
        username: user.username || (user.email ? user.email.split('@')[0] : `user_${user._id.toString().slice(-8)}`),
        email: user.email || null,
        primary_phone: user.phoneNumber || user.phone_number || user.phone || user.primaryPhone || user.primary_phone || null,
        secondary_phone: user.secondaryPhone || user.secondary_phone || user.secondPhone || null,
        first_name: user.firstName || user.first_name || user.firstname || '',
        last_name: user.lastName || user.last_name || user.lastname || '',
        date_of_birth: user.dateOfBirth || user.date_of_birth || user.dob || null,
        gender: user.gender ? user.gender.toLowerCase() : null,
        middle_name: user.middleName || user.middle_name || user.middlename || null,
        prefix: user.prefix || null,
        profile_picture: user.profilePicture || user.profile_picture || user.profileImage || user.avatar || null,
        password: user.password || '$2b$10$defaultHashForUsersWithoutPassword', // Keep original hash or provide default
        created_at: user.createdAt || user.created_at || new Date(),
        updated_at: user.updatedAt || user.updated_at || new Date(),
        verified: user.verified || false,
        is_active: user.isActive !== false, // Default to true
        is_premium: user.isPremium || false,
        is_phone_verified: user.isPhoneVerified || false,
        is_email_verified: user.isEmailVerified || false,
        last_login: user.lastLogin || user.last_login || null,
        mongo_id: user._id.toString(),
        source_collection: user.source,
        // Farmer-specific fields (directly on farmer document)
        farm_name: user.farmName || user.farm_name || null,
        farm_location: user.farmLocation || user.farm_location || null,
        farm_size: user.farmSize || user.farm_size || null,
        farm_address: user.farmAddress || user.farm_address || null,
        account_number: user.accountNumber || user.account_number || null,
        account_name: user.accountName || user.account_name || null,
        bank_name: user.bankName || user.bank_name || null,
        bank_branch: user.bankBranch || user.bank_branch || null,
        // Buyer-specific fields (directly on buyer document)
        business_name: user.businessName || user.business_name || null,
        loyalty_points: user.loyaltyPoints || user.loyalty_points || 0,
        delivery_preferences: user.deliveryPreferences || user.delivery_preferences || null,
        payment_methods: user.paymentMethods || user.payment_methods || null
      };
    });
    
    // Save duplicates report
    fs.writeFileSync('duplicate-resolution-report.json', JSON.stringify(duplicates, null, 2));
    console.log('💾 Duplicate resolution report saved');
    
    // Save users to migrate
    fs.writeFileSync('users-to-migrate-with-passwords.json', JSON.stringify(usersToMigrate, null, 2));
    console.log('💾 Users to migrate saved');
    
    if (DRY_RUN) {
      console.log('\n🧪 DRY RUN - No data will be inserted');
      console.log(`📊 Would migrate ${usersToMigrate.length} users`);
      console.log(`📊 Would resolve ${duplicates.length} duplicates`);
      return;
    }
    
    // Start migration
    console.log('\n🚀 Starting migration...');
    
    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    
    for (const user of usersToMigrate) {
      try {
        const query = `
          INSERT INTO users (
            id, user_type, username, email, primary_phone, secondary_phone,
            first_name, last_name, middle_name, prefix, gender, date_of_birth,
            profile_picture, password, created_at, updated_at, verified, is_active,
            is_premium, is_phone_verified, is_email_verified, last_login
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
          )
        `;

        const values = [
          user.id, user.user_type, user.username, user.email,
          user.primary_phone, user.secondary_phone, user.first_name, user.last_name,
          user.middle_name, user.prefix, user.gender, user.date_of_birth,
          user.profile_picture, user.password, user.created_at, user.updated_at,
          user.verified, user.is_active, user.is_premium, user.is_phone_verified,
          user.is_email_verified, user.last_login
        ];
        
        await pgClient.query(query, values);

        // Insert into farmers or buyers table as appropriate
        if (user.user_type === 'FARMER') {
          const farmerId = uuidv4();
          const farmerQuery = `
            INSERT INTO farmers (
              id, user_id, farm_name, farm_location, farm_size, farm_address,
              account_number, account_name, bank_name, bank_branch, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
          `;
          const farmerValues = [
            farmerId, user.id, user.farm_name, user.farm_location, user.farm_size,
            user.farm_address, user.account_number, user.account_name, user.bank_name,
            user.bank_branch, user.created_at, user.updated_at
          ];
          await pgClient.query(farmerQuery, farmerValues);

          // Create wallet for farmer (as originally designed)
          const walletQuery = `
            INSERT INTO wallets (id, farmer_id, balance, gross_revenue, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6)
          `;
          const walletValues = [
            uuidv4(), farmerId, 0, 0, user.created_at, user.updated_at
          ];
          await pgClient.query(walletQuery, walletValues);
        } else if (user.user_type === 'BUYER') {
          const buyerQuery = `
            INSERT INTO buyers (
              id, user_id, business_name, loyalty_points, delivery_preferences,
              payment_methods, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          `;
          const buyerValues = [
            uuidv4(), user.id, user.business_name, user.loyalty_points,
            user.delivery_preferences ? JSON.stringify(user.delivery_preferences) : null,
            user.payment_methods ? JSON.stringify(user.payment_methods) : null,
            user.created_at, user.updated_at
          ];
          await pgClient.query(buyerQuery, buyerValues);
        }
        successCount++;
        
        if (successCount % 10 === 0) {
          console.log(`✅ Migrated ${successCount} users...`);
        }
        
      } catch (error) {
        errorCount++;
        errors.push({
          user_id: user.id,
          email: user.email,
          error: error.message,
          user_data: user
        });
        console.log(`❌ Failed to migrate ${user.email}: ${error.message}`);
      }
    }
    
    // Save migration results
    const results = {
      total_users: usersToMigrate.length,
      successful_migrations: successCount,
      failed_migrations: errorCount,
      duplicates_resolved: duplicates.length,
      migration_date: new Date().toISOString()
    };
    
    fs.writeFileSync('migration-results.json', JSON.stringify(results, null, 2));
    
    if (errors.length > 0) {
      fs.writeFileSync('migration-errors-with-passwords.json', JSON.stringify(errors, null, 2));
    }
    
    console.log('\n🎉 MIGRATION COMPLETED!');
    console.log(`✅ Successfully migrated: ${successCount} users`);
    console.log(`❌ Failed migrations: ${errorCount} users`);
    console.log(`⚠️  Duplicates resolved: ${duplicates.length} emails`);
    console.log(`📊 Total processed: ${usersToMigrate.length} users`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await pgClient.end();
    await mongoClient.close();
    console.log('🔌 Database connections closed');
  }
}

function calculateCompleteness(user) {
  let score = 0;

  // Check for email (multiple field variations)
  if (user.email && user.email.toString().trim() !== '') {
    score += 2; // Email is very important
  }

  // Check for phone (multiple field variations)
  const phoneFields = ['phoneNumber', 'phone_number', 'phone', 'primaryPhone', 'primary_phone'];
  if (phoneFields.some(field => user[field] && user[field].toString().trim() !== '')) {
    score += 2; // Phone is very important
  }

  // Check for first name (multiple field variations)
  const firstNameFields = ['firstName', 'first_name', 'firstname'];
  if (firstNameFields.some(field => user[field] && user[field].toString().trim() !== '')) {
    score += 1;
  }

  // Check for last name (multiple field variations)
  const lastNameFields = ['lastName', 'last_name', 'lastname'];
  if (lastNameFields.some(field => user[field] && user[field].toString().trim() !== '')) {
    score += 1;
  }

  // Bonus points for having password
  if (user.password && user.password.toString().trim() !== '') {
    score += 3; // Give extra weight to users with passwords
  }

  // Bonus for having nested data decoded
  if (user._originalProfile && Object.keys(user._originalProfile).length > 0) {
    score += 1;
  }
  if (user._originalContact && Object.keys(user._originalContact).length > 0) {
    score += 1;
  }
  if (user._originalBusiness && Object.keys(user._originalBusiness).length > 0) {
    score += 1;
  }

  return score;
}

migrateCompleteUsersWithPasswords(); 