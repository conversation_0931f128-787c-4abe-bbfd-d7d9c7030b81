const { Client } = require('pg');
const crypto = require('crypto');

// PostgreSQL connection
const PG_CONFIG = {
  host: 'localhost',
  port: 5432,
  user: 'ewekoadmin',
  password: '',
  database: 'ewekodb',
};

async function migrateMissingPreferencesAndNotifications() {
  const pgClient = new Client(PG_CONFIG);

  try {
    await pgClient.connect();
    console.log('🔗 Connected to PostgreSQL');

    // Get all users who don't have preferences
    const usersWithoutPreferences = await pgClient.query(`
      SELECT u.id, u.email, u.user_type, u.created_at, u.updated_at
      FROM users u
      LEFT JOIN preferences p ON u.id = p.user_id
      WHERE p.user_id IS NULL
    `);

    console.log(`📊 Found ${usersWithoutPreferences.rows.length} users without preferences`);

    // Create preferences for each user
    for (const user of usersWithoutPreferences.rows) {
      try {
        const preferenceData = {
          id: crypto.randomUUID(),
          user_id: user.id,
          otp_destination: 'EMAIL',
          receive_promotions: true,
          enable_2fa: false,
          general_updates: true,
          order_updates: true,
          transaction_updates: true,
          payment_updates: true,
          delivery_updates: true,
          created_at: user.created_at,
          updated_at: user.updated_at
        };

        const insertPreferenceQuery = `
          INSERT INTO preferences (
            id, user_id, otp_destination, receive_promotions, enable_2fa, general_updates, 
            order_updates, transaction_updates, payment_updates, delivery_updates, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        `;

        await pgClient.query(insertPreferenceQuery, [
          preferenceData.id, preferenceData.user_id, preferenceData.otp_destination,
          preferenceData.receive_promotions, preferenceData.enable_2fa, preferenceData.general_updates,
          preferenceData.order_updates, preferenceData.transaction_updates, preferenceData.payment_updates,
          preferenceData.delivery_updates, preferenceData.created_at, preferenceData.updated_at
        ]);

        console.log(`✅ Created preferences for user: ${user.email}`);
      } catch (error) {
        console.error(`❌ Error creating preferences for user ${user.email}:`, error.message);
      }
    }

    // Get all users who don't have notifications
    const usersWithoutNotifications = await pgClient.query(`
      SELECT u.id, u.email, u.user_type, u.created_at, u.updated_at
      FROM users u
      LEFT JOIN notifications n ON u.id = n.user_id
      WHERE n.user_id IS NULL
    `);

    console.log(`📊 Found ${usersWithoutNotifications.rows.length} users without notifications`);

    // Create welcome notifications for each user
    for (const user of usersWithoutNotifications.rows) {
      try {
        const notificationData = {
          id: crypto.randomUUID(),
          user_id: user.id,
          user_type: user.user_type,
          subject: 'Welcome to Eweko!',
          message: 'Your account has been successfully migrated to our new system. Welcome to the Eweko platform!',
          trigger: 'MIGRATION',
          is_read: false,
          created_at: user.created_at,
          updated_at: user.updated_at
        };

        const insertNotificationQuery = `
          INSERT INTO notifications (
            id, user_id, user_type, subject, message, trigger, is_read, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        `;

        await pgClient.query(insertNotificationQuery, [
          notificationData.id, notificationData.user_id, notificationData.user_type, 
          notificationData.subject, notificationData.message, notificationData.trigger, 
          notificationData.is_read, notificationData.created_at, notificationData.updated_at
        ]);

        console.log(`✅ Created notification for user: ${user.email}`);
      } catch (error) {
        console.error(`❌ Error creating notification for user ${user.email}:`, error.message);
      }
    }

    console.log('\n🎉 MISSING DATA MIGRATION COMPLETED!');
    console.log(`✅ Created preferences for ${usersWithoutPreferences.rows.length} users`);
    console.log(`✅ Created notifications for ${usersWithoutNotifications.rows.length} users`);

  } catch (error) {
    console.error('❌ Missing data migration failed:', error);
    throw error;
  } finally {
    await pgClient.end();
  }
}

// Run the migration
if (require.main === module) {
  migrateMissingPreferencesAndNotifications().catch(console.error);
}

module.exports = { migrateMissingPreferencesAndNotifications };
