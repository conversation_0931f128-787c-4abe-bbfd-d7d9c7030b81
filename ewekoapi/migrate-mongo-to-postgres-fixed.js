const { MongoClient } = require('mongodb');
const { Client } = require('pg');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// MongoDB config
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DB = 'ewekoapi';

// Postgres config
const PG_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  database: process.env.DB_NAME || 'ewekodb',
  user: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || '',
};

// Helper function to convert camelCase to snake_case
function keysToSnake(obj) {
  const snakeObj = {};
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    snakeObj[snakeKey] = value;
  }
  return snakeObj;
}

async function migrate() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);
  
  // ID mapping for relationships
  const idMap = {
    users: {},
    farmers: {},
    buyers: {},
    admins: {},
    categories: {},
    produces: {},
  };

  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoClient.connect();
    const mongoDb = mongoClient.db(MONGODB_DB);
    console.log('✅ Connected to MongoDB!');

    console.log('🔌 Connecting to Postgres...');
    await pgClient.connect();
    console.log('✅ Connected to Postgres!');

    // Clear existing data
    console.log('🧹 Clearing existing PostgreSQL data...');
    await pgClient.query('TRUNCATE TABLE users, farmers, buyers, admins, addresses, preferences, notifications, wallets CASCADE');
    console.log('✅ Cleared existing data!');

    // 1. MIGRATE FARMERS DIRECTLY (58 users)
    console.log('🚜 Migrating farmers...');
    const farmers = await mongoDb.collection('farmers').find().toArray();
    console.log(`📊 Found ${farmers.length} farmers to migrate`);

    for (const farmer of farmers) {
      const userId = uuidv4();
      idMap.users[farmer._id] = userId;
      idMap.farmers[farmer._id] = userId;

      // Extract user data directly from farmer document
      let email = farmer.email || null;
      let phone = farmer.phoneNumber || farmer.primaryPhone || farmer.phone || null;

      // Generate fallback credentials if missing
      if (!email || !phone) {
        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        if (!email) {
          email = `farmer_${timestamp}_${randomSuffix}@eweko.com`;
        }
        if (!phone) {
          phone = `+234${Math.floor(Math.random() * 900000000) + 100000000}`;
        }
        console.log(`🔄 Creating farmer ${farmer._id} with generated credentials - Email: ${email}, Phone: ${phone}`);
      }

      const user = {
        id: userId,
        first_name: farmer.firstName || 'Unknown',
        last_name: farmer.lastName || 'Farmer',
        middle_name: farmer.middleName || null,
        prefix: farmer.prefix || null,
        gender: farmer.gender || null,
        date_of_birth: farmer.dateOfBirth || null,
        profile_picture: farmer.profilePicture || null,
        email: email,
        primary_phone: phone,
        username: email,
        password: farmer.password || '', // Keep original password hash if available
        user_type: 'FARMER',
        verified: farmer.verified || false,
        is_active: farmer.isActive !== false,
        last_login: farmer.lastLogin || null,
        is_premium: farmer.isPremium || false,
        is_phone_verified: farmer.isPhoneVerified || false,
        is_email_verified: farmer.isEmailVerified || false,
        created_at: farmer.createdAt || new Date(),
        updated_at: farmer.updatedAt || new Date(),
      };

      try {
        // Insert user
        await pgClient.query(
          `INSERT INTO users (${Object.keys(user).join(',')}) VALUES (${Object.keys(user).map((_,i)=>`$${i+1}`).join(',')})`,
          Object.values(user)
        );
        
        // Insert farmer profile
        await pgClient.query(
          `INSERT INTO farmers (id, user_id, farm_name, farm_address, farm_size, account_number, account_name, bank_name, bank_branch, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
          [
            uuidv4(), 
            userId, 
            farmer.farmName || null,
            farmer.farmAddress || null,
            farmer.farmSize || null,
            farmer.farmerAccountNumber || null,
            farmer.farmerAccountName || null,
            farmer.farmerBankName || null,
            farmer.farmerBankBranch || null,
            user.created_at,
            user.updated_at
          ]
        );
        console.log(`✅ Created farmer user ${userId} from ${farmer._id}`);
      } catch (error) {
        console.error(`❌ Failed to create farmer ${farmer._id}:`, error.message);
      }
    }

    // 2. MIGRATE BUYERS DIRECTLY (25 users)
    console.log('🛒 Migrating buyers...');
    const buyers = await mongoDb.collection('buyers').find().toArray();
    console.log(`📊 Found ${buyers.length} buyers to migrate`);

    for (const buyer of buyers) {
      const userId = uuidv4();
      idMap.users[buyer._id] = userId;
      idMap.buyers[buyer._id] = userId;

      // Extract user data directly from buyer document
      let email = buyer.email || null;
      let phone = buyer.phoneNumber || buyer.primaryPhone || buyer.phone || null;

      // Generate fallback credentials if missing
      if (!email || !phone) {
        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        if (!email) {
          email = `buyer_${timestamp}_${randomSuffix}@eweko.com`;
        }
        if (!phone) {
          phone = `+234${Math.floor(Math.random() * 900000000) + 100000000}`;
        }
        console.log(`🔄 Creating buyer ${buyer._id} with generated credentials - Email: ${email}, Phone: ${phone}`);
      }

      const user = {
        id: userId,
        first_name: buyer.firstName || 'Unknown',
        last_name: buyer.lastName || 'Buyer',
        middle_name: buyer.middleName || null,
        prefix: buyer.prefix || null,
        gender: buyer.gender || null,
        date_of_birth: buyer.dateOfBirth || null,
        profile_picture: buyer.profilePicture || null,
        email: email,
        primary_phone: phone,
        username: email,
        password: buyer.password || '', // Keep original password hash if available
        user_type: 'BUYER',
        verified: buyer.verified || false,
        is_active: buyer.isActive !== false,
        last_login: buyer.lastLogin || null,
        is_premium: buyer.isPremium || false,
        is_phone_verified: buyer.isPhoneVerified || false,
        is_email_verified: buyer.isEmailVerified || false,
        created_at: buyer.createdAt || new Date(),
        updated_at: buyer.updatedAt || new Date(),
      };

      try {
        // Insert user
        await pgClient.query(
          `INSERT INTO users (${Object.keys(user).join(',')}) VALUES (${Object.keys(user).map((_,i)=>`$${i+1}`).join(',')})`,
          Object.values(user)
        );
        
        // Insert buyer profile
        await pgClient.query(
          `INSERT INTO buyers (id, user_id, loyalty_points, created_at, updated_at) VALUES ($1, $2, $3, $4, $5)`,
          [uuidv4(), userId, buyer.loyaltyPoints || 0, user.created_at, user.updated_at]
        );
        console.log(`✅ Created buyer user ${userId} from ${buyer._id}`);
      } catch (error) {
        console.error(`❌ Failed to create buyer ${buyer._id}:`, error.message);
      }
    }

    // 3. MIGRATE ADMINS
    console.log('👑 Migrating admins...');
    const admins = await mongoDb.collection('admins').find().toArray();
    console.log(`📊 Found ${admins.length} admins to migrate`);

    for (const admin of admins) {
      const userId = uuidv4();
      idMap.users[admin._id] = userId;
      idMap.admins[admin._id] = userId;
      
      // Try to find matching profile/contact data
      const profile = profiles.find(p => p._id.equals(admin._id));
      const contact = contacts.find(c => c._id.equals(admin._id));
      
      let email = contact?.email || admin.email || `admin_${userId}@eweko.com`;
      let phone = contact?.primaryPhone || admin.primaryPhone || admin.phone || `+1234567890`;
      
      const user = {
        id: userId,
        first_name: profile?.firstName || admin.firstName || admin.first_name || 'Admin',
        last_name: profile?.lastName || admin.lastName || admin.last_name || 'User',
        middle_name: profile?.middleName || admin.middleName || null,
        prefix: profile?.prefix || null,
        gender: profile?.gender || admin.gender || null,
        date_of_birth: profile?.dateOfBirth || admin.dateOfBirth || null,
        profile_picture: profile?.profilePicture || admin.profilePicture || null,
        email: email,
        primary_phone: phone,
        username: email,
        password: admin.password || '',
        user_type: 'ADMIN', // Fixed: use user_type instead of type
        verified: admin.verified || false,
        is_active: admin.isActive !== false,
        last_login: admin.lastLogin || null,
        is_premium: false,
        is_phone_verified: admin.isPhoneVerified || false,
        is_email_verified: admin.isEmailVerified || false,
        created_at: admin.createdAt || new Date(),
        updated_at: admin.updatedAt || new Date(),
      };

      try {
        // Insert user
        await pgClient.query(
          `INSERT INTO users (${Object.keys(user).join(',')}) VALUES (${Object.keys(user).map((_,i)=>`$${i+1}`).join(',')})`,
          Object.values(user)
        );
        
        // Insert admin profile
        await pgClient.query(
          `INSERT INTO admins (id, user_id, role, created_at, updated_at) VALUES ($1, $2, $3, $4, $5)`,
          [uuidv4(), userId, admin.adminRole || 'SUPER_ADMIN', user.created_at, user.updated_at]
        );
        console.log(`✅ Created admin user ${userId} from ${admin._id}`);
      } catch (error) {
        console.error(`❌ Failed to create admin ${admin._id}:`, error.message);
      }
    }

    console.log('✅ User migration completed!');
    console.log(`📊 Migrated: ${farmers.length} farmers + ${buyers.length} buyers + ${admins.length} admins = ${farmers.length + buyers.length + admins.length} total users`);

    // 4. MIGRATE WALLETS (atomic with users)
    console.log('💰 Migrating wallets...');
    const wallets = await mongoDb.collection('wallets').find().toArray();
    console.log(`📊 Found ${wallets.length} wallets to migrate`);

    for (const wallet of wallets) {
      try {
        // Get farmer_id and user_id from farmers table - wallets are linked to farmer profiles, not users directly
        const farmerId = idMap.farmers[wallet.farmer] || null;

        if (!farmerId) {
          console.log(`⚠️  Skipping wallet ${wallet._id} - farmer not found for ${wallet.farmer}`);
          continue;
        }

        // Get the actual farmer profile ID from PostgreSQL
        const farmerResult = await pgClient.query('SELECT id FROM farmers WHERE user_id = $1', [farmerId]);
        if (farmerResult.rows.length === 0) {
          console.log(`⚠️  Skipping wallet ${wallet._id} - farmer profile not found`);
          continue;
        }

        const mappedWallet = {
          id: uuidv4(),
          farmer_id: farmerResult.rows[0].id, // Use the actual farmer profile ID
          balance: wallet.balance || 0,
          gross_revenue: wallet.grossRevenue || 0,
          created_at: wallet.createdAt || new Date(),
          updated_at: wallet.updatedAt || new Date(),
        };

        await pgClient.query(
          `INSERT INTO wallets (${Object.keys(mappedWallet).join(',')}) VALUES (${Object.keys(mappedWallet).map((_,i)=>`$${i+1}`).join(',')})`,
          Object.values(mappedWallet)
        );
        console.log(`✅ Migrated wallet ${wallet._id}`);
      } catch (error) {
        console.error(`❌ Failed to migrate wallet ${wallet._id}:`, error.message);
      }
    }

    // 5. MIGRATE ADDRESSES (atomic with users)
    console.log('🏠 Migrating addresses...');
    const addresses = await mongoDb.collection('addresses').find().toArray();
    console.log(`📊 Found ${addresses.length} addresses to migrate`);

    for (const address of addresses) {
      try {
        const userId = idMap.users[address.userId] || idMap.users[address.user_id] || null;
        if (!userId) {
          console.log(`⚠️  Skipping address ${address._id} - user not found`);
          continue;
        }

        const mappedAddress = {
          id: uuidv4(),
          user_id: userId,
          house_number: address.houseNumber || address.house_number || '',
          street_name: address.streetName || address.street_name || '',
          community: address.community || '',
          lga: address.lga || '',
          state: address.state || '',
          country: address.country || 'Nigeria',
          is_default: address.isDefault || address.is_default || false,
          created_at: address.createdAt || address.created_at || new Date(),
          updated_at: address.updatedAt || address.updated_at || new Date(),
        };

        await pgClient.query(
          `INSERT INTO addresses (${Object.keys(mappedAddress).join(',')}) VALUES (${Object.keys(mappedAddress).map((_,i)=>`$${i+1}`).join(',')})`,
          Object.values(mappedAddress)
        );
        console.log(`✅ Migrated address ${address._id}`);
      } catch (error) {
        console.error(`❌ Failed to migrate address ${address._id}:`, error.message);
      }
    }

    // 6. MIGRATE PREFERENCES (atomic with users)
    console.log('⚙️ Migrating preferences...');
    const preferences = await mongoDb.collection('preferences').find().toArray();
    console.log(`📊 Found ${preferences.length} preferences to migrate`);

    for (const preference of preferences) {
      try {
        const userId = idMap.users[preference.user] || idMap.users[preference.userId] || idMap.users[preference.user_id] || null;
        if (!userId) {
          console.log(`⚠️  Skipping preference ${preference._id} - user not found`);
          continue;
        }

        const mappedPreference = {
          id: uuidv4(),
          user_id: userId,
          otp_destination: 'EMAIL', // Default value
          receive_promotions: preference.marketingEmails || preference.marketing_emails || true,
          enable_2fa: preference.enable2fa || preference.enable_2fa || false,
          general_updates: preference.generalUpdates || preference.general_updates || true,
          order_updates: preference.orderUpdates || preference.order_updates || true,
          transaction_updates: preference.transactionUpdates || preference.transaction_updates || true,
          payment_updates: preference.paymentUpdates || preference.payment_updates || true,
          delivery_updates: preference.deliveryUpdates || preference.delivery_updates || true,
          created_at: preference.createdAt || preference.created_at || new Date(),
          updated_at: preference.updatedAt || preference.updated_at || new Date(),
        };

        await pgClient.query(
          `INSERT INTO preferences (${Object.keys(mappedPreference).join(',')}) VALUES (${Object.keys(mappedPreference).map((_,i)=>`$${i+1}`).join(',')})`,
          Object.values(mappedPreference)
        );
        console.log(`✅ Migrated preference ${preference._id}`);
      } catch (error) {
        console.error(`❌ Failed to migrate preference ${preference._id}:`, error.message);
      }
    }

    // 7. MIGRATE NOTIFICATIONS (atomic with users)
    console.log('🔔 Migrating notifications...');
    const notifications = await mongoDb.collection('notifications').find().toArray();
    console.log(`📊 Found ${notifications.length} notifications to migrate`);

    for (const notification of notifications) {
      try {
        const userId = idMap.users[notification.userId] || idMap.users[notification.user_id] || idMap.users[notification.user] || null;
        if (!userId) {
          console.log(`⚠️  Skipping notification ${notification._id} - user not found`);
          continue;
        }

        const mappedNotification = {
          id: uuidv4(),
          user_id: userId,
          user_type: notification.userType || 'GENERAL',
          subject: notification.subject || 'Notification',
          message: notification.message || '',
          trigger: notification.trigger || 'GENERAL',
          is_read: notification.isRead || false,
          created_at: notification.createdAt || new Date(),
          updated_at: notification.updatedAt || new Date(),
        };

        await pgClient.query(
          `INSERT INTO notifications (${Object.keys(mappedNotification).join(',')}) VALUES (${Object.keys(mappedNotification).map((_,i)=>`$${i+1}`).join(',')})`,
          Object.values(mappedNotification)
        );
        console.log(`✅ Migrated notification ${notification._id}`);
      } catch (error) {
        console.error(`❌ Failed to migrate notification ${notification._id}:`, error.message);
      }
    }

    console.log('🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('📊 FINAL SUMMARY:');
    console.log(`   Users: ${farmers.length + buyers.length + admins.length} total`);
    console.log(`   - Farmers: ${farmers.length}`);
    console.log(`   - Buyers: ${buyers.length}`);
    console.log(`   - Admins: ${admins.length}`);
    console.log(`   Wallets: ${wallets.length}`);
    console.log(`   Addresses: ${addresses.length}`);
    console.log(`   Preferences: ${preferences.length}`);
    console.log(`   Notifications: ${notifications.length}`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

migrate();
