const { MongoClient } = require('mongodb');
const { Client } = require('pg');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// MongoDB config
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DB = 'ewekoapi';

// Postgres config
const PG_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  database: process.env.DB_NAME || 'ewekodb',
  user: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || '',
};

// Helper function to convert camelCase to snake_case
function keysToSnake(obj) {
  const snakeObj = {};
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    snakeObj[snakeKey] = value;
  }
  return snakeObj;
}

// Field mapping for MongoDB to Postgres
const fieldMapping = {
  // Orders
  totalCost: 'total_cost',
  shippingFee: 'shipping_fee', 
  shippingAddress: 'shipping_address',
  finalTotalCost: 'final_total_cost',
  
  // Produce
  stock: 'stock', // not quantity
  
  // Notifications
  isRead: 'is_read',
  userType: 'user_type',
  
  // Cart
  totalCost: 'total_cost',
  unitPrice: 'unit_price',
  totalPrice: 'total_price'
};

async function migrate() {
  const mongoClient = new MongoClient(MONGODB_URI);
  const pgClient = new Client(PG_CONFIG);
  const idMap = { users: {}, buyers: {}, farmers: {}, admins: {}, businesses: {}, profiles: {}, contacts: {} };

  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoClient.connect();
    const mongoDb = mongoClient.db(MONGODB_DB);
    console.log('✅ Connected to MongoDB!');

    console.log('🔌 Connecting to Postgres...');
    await pgClient.connect();
    console.log('✅ Connected to Postgres!');

    // 1. USERS (merge profiles, contacts, businesses)
    const profiles = await mongoDb.collection('profiles').find().toArray();
    const contacts = await mongoDb.collection('contacts').find().toArray();
    const businesses = await mongoDb.collection('businesses').find().toArray();
    const buyers = await mongoDb.collection('buyers').find().toArray();
    const farmers = await mongoDb.collection('farmers').find().toArray();
    const admins = await mongoDb.collection('admins').find().toArray();

    console.log(`📊 Found ${profiles.length} profiles, ${contacts.length} contacts, ${businesses.length} businesses`);

    // Build users from profiles, contacts, businesses
    for (const profile of profiles) {
      const contact = contacts.find(c => c._id.equals(profile._id));
      const business = businesses.find(b => b._id.equals(profile._id));
      const userId = uuidv4();
      idMap.users[profile._id] = userId;
      idMap.profiles[profile._id] = userId;
      
      // Validate required fields
      let email = contact ? contact.email : null;
      let phone = contact ? contact.primaryPhone : null;
      
      // Create user even if no email/phone - generate unique credentials
      if (!email && !phone) {
        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        email = `user_${timestamp}_${randomSuffix}@eweko.com`;
        phone = `+234${Math.floor(Math.random() * 900000000) + 100000000}`;
        console.log(`🔄 Creating user ${profile._id} with generated credentials: ${email}`);
      }
      
      // Compose user row with fallbacks
      const user = {
        id: userId,
        first_name: profile.firstName || 'Unknown',
        last_name: profile.lastName || 'User',
        middle_name: profile.middleName || null,
        prefix: profile.prefix || null,
        gender: profile.gender || null,
        date_of_birth: profile.dateOfBirth || null,
        profile_picture: profile.profilePicture || null,
        email: email || `user_${userId}@placeholder.com`, // Fallback email
        primary_phone: phone || `+1234567890`, // Fallback phone
        business_name: business ? business.businessName : null,
        username: email || `user_${userId}`,
        password: '', // Set to empty or a default, as you can't migrate passwords
        type: 'BUYER', // Will update for farmers/admins below
        verified: false,
        is_active: true,
        last_login: null,
        is_premium: false,
        is_phone_verified: false,
        is_email_verified: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      try {
        await pgClient.query(
          `INSERT INTO users (${Object.keys(user).join(',')}) VALUES (${Object.keys(user).map((_,i)=>`$${i+1}`).join(',')})`,
          Object.values(user)
        );
        console.log(`✅ Created user ${userId} from profile ${profile._id}`);
      } catch (error) {
        console.error(`❌ Failed to create user ${profile._id}:`, error.message);
      }
    }

    // 2. UPDATE USER TYPES (farmers, buyers, admins)
    for (const farmer of farmers) {
      const userId = idMap.users[farmer._id] || uuidv4();
      idMap.farmers[farmer._id] = userId;
      try {
        // Update user type to FARMER
        await pgClient.query(
          `UPDATE users SET type = 'FARMER' WHERE id = $1`,
          [userId]
        );
        
        // Create farmer profile
        await pgClient.query(
          `INSERT INTO farmers (id, user_id, farm_name, farm_address, farm_size, account_number, account_name, bank_name, bank_branch, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
          [
            uuidv4(), 
            userId, 
            farmer.farmName || null,
            farmer.farmAddress || null,
            farmer.farmSize || null,
            farmer.farmerAccountNumber || null,
            farmer.farmerAccountName || null,
            farmer.farmerBankName || null,
            farmer.farmerBankBranch || null,
            new Date(),
            new Date()
          ]
        );
        console.log(`✅ Created farmer profile for ${farmer._id}`);
      } catch (error) {
        console.error(`❌ Failed to create farmer ${farmer._id}:`, error.message);
      }
    }

    for (const buyer of buyers) {
      const userId = idMap.users[buyer._id] || uuidv4();
      idMap.buyers[buyer._id] = userId;
      try {
        // Update user type to BUYER
        await pgClient.query(
          `UPDATE users SET type = 'BUYER' WHERE id = $1`,
          [userId]
        );
        
        // Create buyer profile
        await pgClient.query(
          `INSERT INTO buyers (id, user_id, loyalty_points, created_at, updated_at) VALUES ($1, $2, $3, $4, $5)`,
          [uuidv4(), userId, buyer.loyaltyPoints || 0, new Date(), new Date()]
        );
        console.log(`✅ Created buyer profile for ${buyer._id}`);
      } catch (error) {
        console.error(`❌ Failed to create buyer ${buyer._id}:`, error.message);
      }
    }

    for (const admin of admins) {
      const userId = idMap.users[admin._id] || uuidv4();
      idMap.admins[admin._id] = userId;
      try {
        // Update user type to ADMIN
        await pgClient.query(
          `UPDATE users SET type = 'ADMIN' WHERE id = $1`,
          [userId]
        );
        
        await pgClient.query(
          `INSERT INTO admins (id, user_id, role, created_at, updated_at) VALUES ($1, $2, $3, $4, $5)`,
          [uuidv4(), userId, admin.adminRole || 'SUPER_ADMIN', new Date(), new Date()]
        );
        console.log(`✅ Created admin profile for ${admin._id}`);
      } catch (error) {
        console.error(`❌ Failed to create admin ${admin._id}:`, error.message);
      }
    }

    // 5. MIGRATE ALL OTHER TABLES (addresses, preferences, orders, etc.)
    // Helper: generic migration for 1:1 tables
    async function migrateTable(mongoCol, pgTable, fieldMap = x => x) {
      const docs = await mongoDb.collection(mongoCol).find().toArray();
      console.log(`📊 Processing ${docs.length} ${mongoCol}...`);
      for (const doc of docs) {
        try {
          const row = keysToSnake(fieldMap(doc));
          // Replace Mongo _id with uuid if needed
          if (row._id) row.id = uuidv4();
          delete row._id;
          // Map user references
          if (row.user_id && idMap.users[row.user_id]) row.user_id = idMap.users[row.user_id];
          await pgClient.query(
            `INSERT INTO ${pgTable} (${Object.keys(row).join(',')}) VALUES (${Object.keys(row).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(row)
          );
        } catch (error) {
          console.error(`❌ Failed to migrate ${mongoCol} ${doc._id}:`, error.message);
        }
      }
    }

    // Special handling for orders - need to derive farmer_id
    async function migrateOrders() {
      const orders = await mongoDb.collection('orders').find().toArray();
      console.log(`📊 Processing ${orders.length} orders...`);
      
      for (const order of orders) {
        try {
          // Get order items to find the farmer
          const orderItems = await mongoDb.collection('orderitems')
            .find({ order: order._id }).toArray();
          
          let farmerId = null;
          if (orderItems.length > 0) {
            const firstItem = orderItems[0];
            const produce = await mongoDb.collection('produces')
              .findOne({ _id: firstItem.produce });
            if (produce && produce.farmer) {
              const farmer = await mongoDb.collection('farmers')
                .findOne({ _id: produce.farmer });
              if (farmer && idMap.users[farmer._id]) {
                farmerId = idMap.users[farmer._id];
              }
            }
          }
          
          // Get buyer_id from buyers table
          const buyerId = idMap.buyers[order.userId] || null;
          
          // Map status values to match PostgreSQL enum
          const statusMap = {
            'Pending': 'pending',
            'Confirmed': 'confirmed', 
            'Processing': 'processing',
            'Shipped': 'shipped',
            'Delivered': 'delivered',
            'Cancelled': 'cancelled'
          };
          
          const paymentStatusMap = {
            'Pending': 'pending',
            'Completed': 'completed',
            'Failed': 'failed',
            'Cancelled': 'cancelled'
          };
          
          // Map fields using the field mapping
          const mappedOrder = {
            id: uuidv4(),
            buyer_id: buyerId,
            farmer_id: farmerId,
            status: statusMap[order.status] || 'pending',
            total_cost: order.totalCost || 0,
            shipping_fee: order.shippingFee || 0,
            shipping_address: order.shippingAddress ? JSON.stringify(order.shippingAddress) : null,
            payment_method: order.paymentMethod || 'card',
            payment_status: paymentStatusMap[order.paymentStatus] || 'pending',
            payment_date: order.paymentDate || null,
            shipped_date: order.shippedDate || null,
            final_total_cost: order.finalTotalCost || order.totalCost || 0,
            notes: order.notes || null,
            delivery_date: order.deliveryDate || null,
            created_at: order.createdAt || new Date(),
            updated_at: order.updatedAt || new Date(),
          };
          
          await pgClient.query(
            `INSERT INTO orders (${Object.keys(mappedOrder).join(',')}) VALUES (${Object.keys(mappedOrder).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(mappedOrder)
          );
          console.log(`✅ Migrated order ${order._id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate order ${order._id}:`, error.message);
        }
      }
    }

    // Special handling for produce - map stock field
    async function migrateProduce() {
      const produces = await mongoDb.collection('produces').find().toArray();
      console.log(`📊 Processing ${produces.length} produces...`);
      
      for (const produce of produces) {
        try {
          // Get farmer_id from farmers table
          const farmerId = idMap.farmers[produce.farmer] || null;
          
          const mappedProduce = {
            id: uuidv4(),
            farmer_id: farmerId,
            category_id: uuidv4(), // Will need to map categories properly
            name: produce.name || '',
            description: produce.description || '',
            price: produce.price || 0,
            negotiable_price: produce.negotiablePrice || 0,
            unit: 'kg', // Default unit
            min_order_qty: produce.minOrderQty || 1,
            stock: produce.stock || 0,
            images: Array.isArray(produce.images) ? JSON.stringify(produce.images) : '[]',
            is_available: true,
            is_active: true,
            slug: produce.slug || '',
            harvest_date: produce.harvestDate || null,
            expiry_date: null,
            created_at: produce.createdAt || new Date(),
            updated_at: produce.updatedAt || new Date(),
          };
          
          await pgClient.query(
            `INSERT INTO produces (${Object.keys(mappedProduce).join(',')}) VALUES (${Object.keys(mappedProduce).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(mappedProduce)
          );
          console.log(`✅ Migrated produce ${produce._id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate produce ${produce._id}:`, error.message);
        }
      }
    }

    // Special handling for notifications - map isRead to is_read
    async function migrateNotifications() {
      const notifications = await mongoDb.collection('notifications').find().toArray();
      console.log(`📊 Processing ${notifications.length} notifications...`);
      
      for (const notification of notifications) {
        try {
          const mappedNotification = {
            id: uuidv4(),
            user_id: idMap.users[notification.userId] || null,
            user_type: notification.userType || null,
            subject: notification.subject || '',
            message: notification.message || '',
            trigger: notification.trigger || null,
            is_read: notification.isRead || false,
            created_at: notification.createdAt || new Date(),
            updated_at: notification.updatedAt || new Date(),
          };
          
          await pgClient.query(
            `INSERT INTO notifications (${Object.keys(mappedNotification).join(',')}) VALUES (${Object.keys(mappedNotification).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(mappedNotification)
          );
          console.log(`✅ Migrated notification ${notification._id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate notification ${notification._id}:`, error.message);
        }
      }
    }

    // Special handling for wallets - add user_id
    async function migrateWallets() {
      const wallets = await mongoDb.collection('wallets').find().toArray();
      console.log(`📊 Processing ${wallets.length} wallets...`);
      
      for (const wallet of wallets) {
        try {
          // Get farmer_id from farmers table
          const farmerId = idMap.farmers[wallet.farmer] || null;
          const userId = idMap.users[wallet.farmer] || null;
          
          const mappedWallet = {
            id: uuidv4(),
            user_id: userId,
            farmer_id: farmerId,
            balance: wallet.balance || 0,
            gross_revenue: wallet.grossRevenue || 0,
            created_at: wallet.createdAt || new Date(),
            updated_at: wallet.updatedAt || new Date(),
          };
          
          await pgClient.query(
            `INSERT INTO wallets (${Object.keys(mappedWallet).join(',')}) VALUES (${Object.keys(mappedWallet).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(mappedWallet)
          );
          console.log(`✅ Migrated wallet ${wallet._id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate wallet ${wallet._id}:`, error.message);
        }
      }
    }

    // Special handling for addresses - remove __v and handle missing country
    async function migrateAddresses() {
      const addresses = await mongoDb.collection('addresses').find().toArray();
      console.log(`📊 Processing ${addresses.length} addresses...`);
      
      for (const address of addresses) {
        try {
          const mappedAddress = {
            id: uuidv4(),
            user_id: idMap.users[address.userId] || null,
            house_number: address.houseNumber || '',
            street_name: address.streetName || '',
            community: address.community || '',
            lga: address.lga || '',
            state: address.state || '',
            country: address.country || 'Nigeria',
            is_default: address.isDefault || false,
            created_at: address.createdAt || new Date(),
            updated_at: address.updatedAt || new Date(),
          };
          
          await pgClient.query(
            `INSERT INTO addresses (${Object.keys(mappedAddress).join(',')}) VALUES (${Object.keys(mappedAddress).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(mappedAddress)
          );
          console.log(`✅ Migrated address ${address._id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate address ${address._id}:`, error.message);
        }
      }
    }

    // Special handling for preferences - fix reserved keyword 'user'
    async function migratePreferences() {
      const preferences = await mongoDb.collection('preferences').find().toArray();
      console.log(`📊 Processing ${preferences.length} preferences...`);
      
      for (const preference of preferences) {
        try {
          const mappedPreference = {
            id: uuidv4(),
            user_id: idMap.users[preference.user] || null,
            otp_destination: preference.otpDestination || 'EMAIL',
            receive_promotions: preference.receivePromotions !== false,
            enable_2fa: preference.enable2fa || false,
            general_updates: preference.generalUpdates !== false,
            order_updates: preference.orderUpdates !== false,
            transaction_updates: preference.transactionUpdates !== false,
            payment_updates: preference.paymentUpdates !== false,
            delivery_updates: preference.deliveryUpdates !== false,
            created_at: preference.createdAt || new Date(),
            updated_at: preference.updatedAt || new Date(),
          };
          
          await pgClient.query(
            `INSERT INTO preferences (${Object.keys(mappedPreference).join(',')}) VALUES (${Object.keys(mappedPreference).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(mappedPreference)
          );
          console.log(`✅ Migrated preference ${preference._id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate preference ${preference._id}:`, error.message);
        }
      }
    }

    // Special handling for order items - map produce to produce_id
    async function migrateOrderItems() {
      const orderItems = await mongoDb.collection('orderitems').find().toArray();
      console.log(`📊 Processing ${orderItems.length} order items...`);
      
      for (const item of orderItems) {
        try {
          const mappedItem = {
            id: uuidv4(),
            order_id: uuidv4(), // Will need to map to actual order_id
            produce_id: uuidv4(), // Will need to map to actual produce_id
            quantity: item.quantity || 1,
            unit_price: item.unitPrice || 0,
            total_price: item.totalPrice || 0,
            created_at: item.createdAt || new Date(),
            updated_at: item.updatedAt || new Date(),
          };
          
          await pgClient.query(
            `INSERT INTO order_items (${Object.keys(mappedItem).join(',')}) VALUES (${Object.keys(mappedItem).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(mappedItem)
          );
          console.log(`✅ Migrated order item ${item._id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate order item ${item._id}:`, error.message);
        }
      }
    }

    // Special handling for transactions - fix reserved keyword 'user'
    async function migrateTransactions() {
      const transactions = await mongoDb.collection('transactions').find().toArray();
      console.log(`📊 Processing ${transactions.length} transactions...`);
      
      for (const transaction of transactions) {
        try {
          const mappedTransaction = {
            id: uuidv4(),
            user_id: idMap.users[transaction.user] || null,
            buyer_id: idMap.users[transaction.user] || null, // For buyer transactions
            farmer_id: null, // Will be set based on order
            order_id: uuidv4(), // Will need to map to actual order_id
            total_amount: transaction.totalAmount || 0,
            status: transaction.status || 'pending',
            payment_method: transaction.paymentMethod || 'card',
            payment_type: 'purchase',
            reference: transaction.reference || null,
            processed: transaction.processed || false,
            created_at: transaction.createdAt || new Date(),
            updated_at: transaction.updatedAt || new Date(),
          };
          
          await pgClient.query(
            `INSERT INTO transactions (${Object.keys(mappedTransaction).join(',')}) VALUES (${Object.keys(mappedTransaction).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(mappedTransaction)
          );
          console.log(`✅ Migrated transaction ${transaction._id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate transaction ${transaction._id}:`, error.message);
        }
      }
    }

    // Special handling for categories - remove produces field
    async function migrateCategories() {
      const categories = await mongoDb.collection('categories').find().toArray();
      console.log(`📊 Processing ${categories.length} categories...`);
      
      for (const category of categories) {
        try {
          const mappedCategory = {
            id: uuidv4(),
            name: category.name || '',
            slug: category.slug || category.name?.toLowerCase().replace(/\s+/g, '-') || '',
            description: category.description || null,
            image: category.image || null,
            is_active: category.isActive !== false,
            created_at: category.createdAt || new Date(),
            updated_at: category.updatedAt || new Date(),
          };
          
          await pgClient.query(
            `INSERT INTO categories (${Object.keys(mappedCategory).join(',')}) VALUES (${Object.keys(mappedCategory).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(mappedCategory)
          );
          console.log(`✅ Migrated category ${category._id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate category ${category._id}:`, error.message);
        }
      }
    }

    // Special handling for carts - normalize items
    async function migrateCarts() {
      const carts = await mongoDb.collection('carts').find().toArray();
      console.log(`📊 Processing ${carts.length} carts...`);
      
      for (const cart of carts) {
        try {
          const mappedCart = {
            id: uuidv4(),
            user_id: idMap.users[cart.buyer] || null, // Map 'buyer' field to 'user_id'
            total_cost: cart.totalCost || 0,
            active: cart.active !== false,
            created_at: cart.createdAt || new Date(),
            updated_at: cart.updatedAt || new Date(),
          };
          
          await pgClient.query(
            `INSERT INTO carts (${Object.keys(mappedCart).join(',')}) VALUES (${Object.keys(mappedCart).map((_,i)=>`$${i+1}`).join(',')})`,
            Object.values(mappedCart)
          );
          console.log(`✅ Migrated cart ${cart._id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate cart ${cart._id}:`, error.message);
        }
      }
    }

    await migrateAddresses();
    await migratePreferences();
    await migrateOrders();
    await migrateOrderItems();
    await migrateTransactions();
    await migrateProduce();
    await migrateNotifications();
    await migrateCategories();
    await migrateWallets();
    await migrateCarts();

    console.log('✅ Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await mongoClient.close();
    await pgClient.end();
  }
}

migrate(); 