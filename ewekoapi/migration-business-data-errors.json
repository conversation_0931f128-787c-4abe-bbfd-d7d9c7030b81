[{"table": "orders", "item_id": "67f28d7f41947d7dfd8ddfcb", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "67f28d7f41947d7dfd8ddfcb", "userId": "67e7372f71afe8262a5b365d", "items": [{"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 400, "totalPrice": 400000, "_id": "67f28d7f41947d7dfd8ddfc8", "createdAt": "2025-04-06T14:19:43.425Z", "updatedAt": "2025-04-06T14:19:43.596Z", "__v": 0}], "totalCost": 400000, "shippingFee": 14000.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "99, Agboyi road, Kosofe, Lagos, Nigeria", "shippedDate": null, "finalTotalCost": 414000, "paymentDate": "2025-04-06T14:19:43.595Z", "createdAt": "2025-04-06T14:19:43.597Z", "updatedAt": "2025-04-06T14:19:43.597Z", "__v": 0}}, {"table": "orders", "item_id": "67f28dbd41947d7dfd8ddfec", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "67f28dbd41947d7dfd8ddfec", "userId": "67e7372f71afe8262a5b365d", "items": [{"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 400, "totalPrice": 400000, "_id": "67f28dbd41947d7dfd8ddfe9", "createdAt": "2025-04-06T14:20:45.828Z", "updatedAt": "2025-04-06T14:20:45.989Z", "__v": 0}], "totalCost": 400000, "shippingFee": 14000.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "99, Agboyi road, Kosofe, Lagos, Nigeria", "shippedDate": null, "finalTotalCost": 414000, "paymentDate": "2025-04-06T14:20:45.987Z", "createdAt": "2025-04-06T14:20:45.989Z", "updatedAt": "2025-04-06T14:20:45.989Z", "__v": 0}}, {"table": "orders", "item_id": "67f28e2241947d7dfd8de00a", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "67f28e2241947d7dfd8de00a", "userId": "67e7372f71afe8262a5b365d", "items": [{"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 400, "totalPrice": 400000, "_id": "67f28e2141947d7dfd8de007", "createdAt": "2025-04-06T14:22:25.938Z", "updatedAt": "2025-04-06T14:22:26.089Z", "__v": 0}], "totalCost": 400000, "shippingFee": 14000.000000000002, "status": "Processing", "paymentMethod": "CentiivPay", "paymentStatus": "Pending", "shippingAddress": "99, Agboyi road, Kosofe, Lagos, Nigeria", "shippedDate": null, "finalTotalCost": 414000, "paymentDate": "2025-04-06T14:22:26.089Z", "createdAt": "2025-04-06T14:22:26.089Z", "updatedAt": "2025-04-06T14:22:26.089Z", "__v": 0}}, {"table": "orders", "item_id": "67fb07abf6e63e904e7dc470", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "67fb07abf6e63e904e7dc470", "userId": "67e83a1b71afe8262a5b5230", "items": [{"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 134, "totalPrice": 134000, "_id": "67fb07abf6e63e904e7dc46d", "createdAt": "2025-04-13T00:39:07.101Z", "updatedAt": "2025-04-13T00:39:07.282Z", "__v": 0}], "totalCost": 134000, "shippingFee": 4690, "status": "Processing", "paymentMethod": "CentiivPay", "paymentStatus": "Pending", "shippingAddress": "4, e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>woza, Borno, Nigeria", "shippedDate": null, "finalTotalCost": 138690, "paymentDate": "2025-04-13T00:39:07.281Z", "createdAt": "2025-04-13T00:39:07.282Z", "updatedAt": "2025-04-13T00:39:07.282Z", "__v": 0}}, {"table": "orders", "item_id": "67fc0810f6e63e904e7ddf53", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "67fc0810f6e63e904e7ddf53", "userId": "67e822c071afe8262a5b4f5e", "items": [{"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 300, "totalPrice": 300000, "_id": "67fc0810f6e63e904e7ddf50", "createdAt": "2025-04-13T18:53:04.639Z", "updatedAt": "2025-04-13T18:53:04.792Z", "__v": 0}], "totalCost": 300000, "shippingFee": 10500.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "No 18, Noforija diversion epe , Epe, Lagos, Nigeria", "shippedDate": null, "finalTotalCost": 310500, "paymentDate": "2025-04-13T18:53:04.791Z", "createdAt": "2025-04-13T18:53:04.792Z", "updatedAt": "2025-04-13T18:53:04.792Z", "__v": 0}}, {"table": "orders", "item_id": "6803f51bad51c892e5b2331a", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6803f51bad51c892e5b2331a", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 200, "totalPrice": 280000, "_id": "6803f51bad51c892e5b23317", "createdAt": "2025-04-19T19:10:19.409Z", "updatedAt": "2025-04-19T19:10:19.566Z", "__v": 0}], "totalCost": 280000, "shippingFee": 9800.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 289800, "paymentDate": "2025-04-19T19:10:19.565Z", "createdAt": "2025-04-19T19:10:19.567Z", "updatedAt": "2025-04-19T19:10:19.567Z", "__v": 0}}, {"table": "orders", "item_id": "6803f66ead51c892e5b2333f", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6803f66ead51c892e5b2333f", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 200, "totalPrice": 280000, "_id": "6803f66ead51c892e5b2333c", "createdAt": "2025-04-19T19:15:58.435Z", "updatedAt": "2025-04-19T19:15:58.603Z", "__v": 0}], "totalCost": 280000, "shippingFee": 9800.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 289800, "paymentDate": "2025-04-19T19:15:58.603Z", "createdAt": "2025-04-19T19:15:58.604Z", "updatedAt": "2025-04-19T19:15:58.604Z", "__v": 0}}, {"table": "orders", "item_id": "6803f773c8161942c985b1ac", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6803f773c8161942c985b1ac", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 200, "totalPrice": 280000, "_id": "6803f773c8161942c985b1a9", "createdAt": "2025-04-19T19:20:19.144Z", "updatedAt": "2025-04-19T19:20:19.315Z", "__v": 0}], "totalCost": 280000, "shippingFee": 9800.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 289800, "paymentDate": "2025-04-19T19:20:19.313Z", "createdAt": "2025-04-19T19:20:19.316Z", "updatedAt": "2025-04-19T19:20:19.316Z", "__v": 0}}, {"table": "orders", "item_id": "6803f7d5c8161942c985b1da", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6803f7d5c8161942c985b1da", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 200, "totalPrice": 280000, "_id": "6803f7d5c8161942c985b1d7", "createdAt": "2025-04-19T19:21:57.413Z", "updatedAt": "2025-04-19T19:21:57.567Z", "__v": 0}], "totalCost": 280000, "shippingFee": 9800.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 289800, "paymentDate": "2025-04-19T19:21:57.566Z", "createdAt": "2025-04-19T19:21:57.567Z", "updatedAt": "2025-04-19T19:21:57.567Z", "__v": 0}}, {"table": "orders", "item_id": "6803f83cc8161942c985b222", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6803f83cc8161942c985b222", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 200, "totalPrice": 280000, "_id": "6803f83cc8161942c985b21f", "createdAt": "2025-04-19T19:23:40.417Z", "updatedAt": "2025-04-19T19:23:40.570Z", "__v": 0}], "totalCost": 280000, "shippingFee": 9800.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 289800, "paymentDate": "2025-04-19T19:23:40.569Z", "createdAt": "2025-04-19T19:23:40.570Z", "updatedAt": "2025-04-19T19:23:40.570Z", "__v": 0}}, {"table": "orders", "item_id": "6804c691c8161942c985b507", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6804c691c8161942c985b507", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 200, "totalPrice": 280000, "_id": "6804c691c8161942c985b504", "createdAt": "2025-04-20T10:04:01.466Z", "updatedAt": "2025-04-20T10:04:01.635Z", "__v": 0}], "totalCost": 280000, "shippingFee": 9800.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 289800, "paymentDate": "2025-04-20T10:04:01.632Z", "createdAt": "2025-04-20T10:04:01.635Z", "updatedAt": "2025-04-20T10:04:01.635Z", "__v": 0}}, {"table": "orders", "item_id": "6804de520479bf9f63e2cd9a", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6804de520479bf9f63e2cd9a", "userId": "6804c96ab55244614aadb0e8", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 201, "totalPrice": 281400, "_id": "6804de510479bf9f63e2cd93", "createdAt": "2025-04-20T11:45:21.643Z", "updatedAt": "2025-04-20T11:45:22.023Z", "__v": 0}, {"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 105, "totalPrice": 105000, "_id": "6804de510479bf9f63e2cd97", "createdAt": "2025-04-20T11:45:21.868Z", "updatedAt": "2025-04-20T11:45:22.023Z", "__v": 0}], "totalCost": 386400, "shippingFee": 13524.000000000002, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "99, Agboyi road, Alapere, Kosofe, Lagos", "shippedDate": null, "finalTotalCost": 399924, "paymentDate": "2025-04-20T11:45:22.021Z", "createdAt": "2025-04-20T11:45:22.023Z", "updatedAt": "2025-04-20T11:45:22.023Z", "__v": 0}}, {"table": "orders", "item_id": "6804fd0b0479bf9f63e2cf2c", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6804fd0b0479bf9f63e2cf2c", "userId": "6804c96ab55244614aadb0e8", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 900, "totalPrice": 1260000, "_id": "6804fd0b0479bf9f63e2cf29", "createdAt": "2025-04-20T13:56:27.482Z", "updatedAt": "2025-04-20T13:56:27.638Z", "__v": 0}], "totalCost": 1260000, "shippingFee": 44100.00000000001, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Completed", "shippingAddress": "99, Agboyi road, Alapere, Kosofe, Lagos", "shippedDate": null, "finalTotalCost": 1304100, "paymentDate": "2025-04-20T13:56:27.637Z", "createdAt": "2025-04-20T13:56:27.639Z", "updatedAt": "2025-04-21T02:37:34.501Z", "__v": 0}}, {"table": "orders", "item_id": "6805793f0479bf9f63e2d0c0", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6805793f0479bf9f63e2d0c0", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 200, "totalPrice": 280000, "_id": "6805793f0479bf9f63e2d0bd", "createdAt": "2025-04-20T22:46:23.061Z", "updatedAt": "2025-04-20T22:46:23.218Z", "__v": 0}], "totalCost": 280000, "shippingFee": 9800.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Completed", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 289800, "paymentDate": "2025-04-20T22:46:23.216Z", "createdAt": "2025-04-20T22:46:23.218Z", "updatedAt": "2025-04-21T02:16:37.025Z", "__v": 0}}, {"table": "orders", "item_id": "68059c720479bf9f63e2d1cc", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "68059c720479bf9f63e2d1cc", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 253, "totalPrice": 354200, "_id": "68059c720479bf9f63e2d1c9", "createdAt": "2025-04-21T01:16:34.038Z", "updatedAt": "2025-04-21T01:16:34.207Z", "__v": 0}], "totalCost": 354200, "shippingFee": 12397.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 366597, "paymentDate": "2025-04-21T01:16:34.206Z", "createdAt": "2025-04-21T01:16:34.207Z", "updatedAt": "2025-04-21T01:16:34.207Z", "__v": 0}}, {"table": "orders", "item_id": "68059ded0479bf9f63e2d20f", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "68059ded0479bf9f63e2d20f", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 225, "totalPrice": 315000, "_id": "68059ded0479bf9f63e2d20c", "createdAt": "2025-04-21T01:22:53.733Z", "updatedAt": "2025-04-21T01:22:53.886Z", "__v": 0}], "totalCost": 315000, "shippingFee": 11025.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 326025, "paymentDate": "2025-04-21T01:22:53.884Z", "createdAt": "2025-04-21T01:22:53.886Z", "updatedAt": "2025-04-21T01:22:53.886Z", "__v": 0}}, {"table": "orders", "item_id": "6805a1a70479bf9f63e2d27a", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6805a1a70479bf9f63e2d27a", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 120, "totalPrice": 120000, "_id": "6805a1a70479bf9f63e2d277", "createdAt": "2025-04-21T01:38:47.050Z", "updatedAt": "2025-04-21T01:38:47.202Z", "__v": 0}], "totalCost": 120000, "shippingFee": 4200, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 124200, "paymentDate": "2025-04-21T01:38:47.201Z", "createdAt": "2025-04-21T01:38:47.202Z", "updatedAt": "2025-04-21T01:38:47.202Z", "__v": 0}}, {"table": "orders", "item_id": "6805aa615d902a768971cac4", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6805aa615d902a768971cac4", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 246, "totalPrice": 246000, "_id": "6805aa615d902a768971cac1", "createdAt": "2025-04-21T02:16:01.207Z", "updatedAt": "2025-04-21T02:16:01.367Z", "__v": 0}], "totalCost": 246000, "shippingFee": 8610, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 254610, "paymentDate": "2025-04-21T02:16:01.365Z", "createdAt": "2025-04-21T02:16:01.367Z", "updatedAt": "2025-04-21T02:16:01.367Z", "__v": 0}}, {"table": "orders", "item_id": "680680175d902a768971cd95", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "680680175d902a768971cd95", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 500, "totalPrice": 500000, "_id": "680680165d902a768971cd8e", "createdAt": "2025-04-21T17:27:50.621Z", "updatedAt": "2025-04-21T17:27:51.003Z", "__v": 0}, {"produce": "67f26efb41947d7dfd8ddb24", "quantity": 250, "totalPrice": 350000, "_id": "680680165d902a768971cd92", "createdAt": "2025-04-21T17:27:50.851Z", "updatedAt": "2025-04-21T17:27:51.003Z", "__v": 0}], "totalCost": 850000, "shippingFee": 29750.000000000004, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Completed", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 879750, "paymentDate": "2025-04-21T17:27:51.002Z", "createdAt": "2025-04-21T17:27:51.003Z", "updatedAt": "2025-04-21T17:28:13.558Z", "__v": 0}}, {"table": "orders", "item_id": "68068b325d902a768971ce34", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "68068b325d902a768971ce34", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 250, "totalPrice": 350000, "_id": "68068b315d902a768971ce2d", "createdAt": "2025-04-21T18:15:13.735Z", "updatedAt": "2025-04-21T18:15:14.121Z", "__v": 0}, {"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 200, "totalPrice": 200000, "_id": "68068b315d902a768971ce31", "createdAt": "2025-04-21T18:15:13.969Z", "updatedAt": "2025-04-21T18:15:14.121Z", "__v": 0}], "totalCost": 550000, "shippingFee": 19250.000000000004, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Completed", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 569250, "paymentDate": "2025-04-21T18:15:14.120Z", "createdAt": "2025-04-21T18:15:14.121Z", "updatedAt": "2025-04-21T18:15:46.473Z", "__v": 0}}, {"table": "orders", "item_id": "6811ea83b0b2b5fd6aa34287", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6811ea83b0b2b5fd6aa34287", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 200, "totalPrice": 280000, "_id": "6811ea83b0b2b5fd6aa34280", "createdAt": "2025-04-30T09:16:51.527Z", "updatedAt": "2025-04-30T09:16:51.910Z", "__v": 0}, {"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 200, "totalPrice": 200000, "_id": "6811ea83b0b2b5fd6aa34284", "createdAt": "2025-04-30T09:16:51.760Z", "updatedAt": "2025-04-30T09:16:51.910Z", "__v": 0}], "totalCost": 480000, "shippingFee": 16800, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 496800, "paymentDate": "2025-04-30T09:16:51.909Z", "createdAt": "2025-04-30T09:16:51.911Z", "updatedAt": "2025-04-30T09:16:51.911Z", "__v": 0}}, {"table": "orders", "item_id": "6813fed7b0b2b5fd6aa34831", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6813fed7b0b2b5fd6aa34831", "userId": "6804c96ab55244614aadb0e8", "items": [{"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 250, "totalPrice": 250000, "_id": "6813fed7b0b2b5fd6aa3482e", "createdAt": "2025-05-01T23:08:07.765Z", "updatedAt": "2025-05-01T23:08:07.932Z", "__v": 0}], "totalCost": 250000, "shippingFee": 8750, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "99, Agboyi road, Alapere, Kosofe, Lagos", "shippedDate": null, "finalTotalCost": 258750, "paymentDate": "2025-05-01T23:08:07.931Z", "createdAt": "2025-05-01T23:08:07.932Z", "updatedAt": "2025-05-01T23:08:07.932Z", "__v": 0}}, {"table": "orders", "item_id": "6814033bb0b2b5fd6aa349df", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6814033bb0b2b5fd6aa349df", "userId": "6804c96ab55244614aadb0e8", "items": [{"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 200, "totalPrice": 200000, "_id": "6814033bb0b2b5fd6aa349dc", "createdAt": "2025-05-01T23:26:51.434Z", "updatedAt": "2025-05-01T23:26:51.584Z", "__v": 0}], "totalCost": 200000, "shippingFee": 7000.000000000001, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Completed", "shippingAddress": "99, Agboyi road, Alapere, Kosofe, Lagos", "shippedDate": null, "finalTotalCost": 207000, "paymentDate": "2025-05-01T23:26:51.583Z", "createdAt": "2025-05-01T23:26:51.584Z", "updatedAt": "2025-05-01T23:27:04.389Z", "__v": 0}}, {"table": "orders", "item_id": "681404b1b0b2b5fd6aa34a43", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "681404b1b0b2b5fd6aa34a43", "userId": "6804c96ab55244614aadb0e8", "items": [{"produce": "67f26efb41947d7dfd8ddb24", "quantity": 200, "totalPrice": 280000, "_id": "681404b0b0b2b5fd6aa34a3c", "createdAt": "2025-05-01T23:33:04.909Z", "updatedAt": "2025-05-01T23:33:05.278Z", "__v": 0}, {"produce": "67f26fde41947d7dfd8ddb4e", "quantity": 100, "totalPrice": 100000, "_id": "681404b1b0b2b5fd6aa34a40", "createdAt": "2025-05-01T23:33:05.129Z", "updatedAt": "2025-05-01T23:33:05.278Z", "__v": 0}], "totalCost": 380000, "shippingFee": 13300.000000000002, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Completed", "shippingAddress": "99, Agboyi road, Alapere, Kosofe, Lagos", "shippedDate": null, "finalTotalCost": 393300, "paymentDate": "2025-05-01T23:33:05.277Z", "createdAt": "2025-05-01T23:33:05.278Z", "updatedAt": "2025-05-01T23:33:15.891Z", "__v": 0}}, {"table": "orders", "item_id": "681b27cfb0b2b5fd6aa35f6d", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "681b27cfb0b2b5fd6aa35f6d", "userId": "6804c96ab55244614aadb0e8", "items": [{"produce": "681b274db0b2b5fd6aa35f19", "quantity": 500, "totalPrice": 675000, "_id": "681b27cfb0b2b5fd6aa35f6a", "createdAt": "2025-05-07T09:28:47.027Z", "updatedAt": "2025-05-07T09:28:47.200Z", "__v": 0}], "totalCost": 675000, "shippingFee": 23625.000000000004, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "99, Agboyi road, Alapere, Kosofe, Lagos", "shippedDate": null, "finalTotalCost": 698625, "paymentDate": "2025-05-07T09:28:47.199Z", "createdAt": "2025-05-07T09:28:47.200Z", "updatedAt": "2025-05-07T09:28:47.200Z", "__v": 0}}, {"table": "orders", "item_id": "681b2803b0b2b5fd6aa35f83", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "681b2803b0b2b5fd6aa35f83", "userId": "6804c96ab55244614aadb0e8", "items": [{"produce": "681b274db0b2b5fd6aa35f19", "quantity": 500, "totalPrice": 675000, "_id": "681b2803b0b2b5fd6aa35f80", "createdAt": "2025-05-07T09:29:39.062Z", "updatedAt": "2025-05-07T09:29:39.217Z", "__v": 0}], "totalCost": 675000, "shippingFee": 23625.000000000004, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "99, Agboyi road, Alapere, Kosofe, Lagos", "shippedDate": null, "finalTotalCost": 698625, "paymentDate": "2025-05-07T09:29:39.216Z", "createdAt": "2025-05-07T09:29:39.217Z", "updatedAt": "2025-05-07T09:29:39.217Z", "__v": 0}}, {"table": "orders", "item_id": "681b2825b0b2b5fd6aa35f9c", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "681b2825b0b2b5fd6aa35f9c", "userId": "6804c96ab55244614aadb0e8", "items": [{"produce": "681b274db0b2b5fd6aa35f19", "quantity": 500, "totalPrice": 675000, "_id": "681b2825b0b2b5fd6aa35f99", "createdAt": "2025-05-07T09:30:13.079Z", "updatedAt": "2025-05-07T09:30:13.232Z", "__v": 0}], "totalCost": 675000, "shippingFee": 23625.000000000004, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Pending", "shippingAddress": "99, Agboyi road, Alapere, Kosofe, Lagos", "shippedDate": null, "finalTotalCost": 698625, "paymentDate": "2025-05-07T09:30:13.231Z", "createdAt": "2025-05-07T09:30:13.232Z", "updatedAt": "2025-05-07T09:30:13.232Z", "__v": 0}}, {"table": "orders", "item_id": "681b5f2fb0b2b5fd6aa367c3", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "681b5f2fb0b2b5fd6aa367c3", "userId": "681b4c60b0b2b5fd6aa36220", "items": [{"produce": "681b58c7b0b2b5fd6aa3655d", "quantity": 70, "totalPrice": 175000, "_id": "681b5f2fb0b2b5fd6aa367c0", "createdAt": "2025-05-07T13:25:03.124Z", "updatedAt": "2025-05-07T13:25:03.293Z", "__v": 0}], "totalCost": 175000, "shippingFee": 6125.000000000001, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "3, Kajero Adan Street, Papa, Epe, Epe, Lagos", "shippedDate": null, "finalTotalCost": 181125, "paymentDate": "2025-05-07T13:25:03.292Z", "createdAt": "2025-05-07T13:25:03.294Z", "updatedAt": "2025-05-07T13:25:03.294Z", "__v": 0}}, {"table": "orders", "item_id": "681f8399b0b2b5fd6aa378c2", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "681f8399b0b2b5fd6aa378c2", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "681b58c7b0b2b5fd6aa3655d", "quantity": 53, "totalPrice": 132500, "_id": "681f8399b0b2b5fd6aa378bf", "createdAt": "2025-05-10T16:49:29.427Z", "updatedAt": "2025-05-10T16:49:29.590Z", "__v": 0}], "totalCost": 132500, "shippingFee": 4637.5, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Completed", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 137137.5, "paymentDate": "2025-05-10T16:49:29.589Z", "createdAt": "2025-05-10T16:49:29.590Z", "updatedAt": "2025-05-10T16:49:42.217Z", "__v": 0}}, {"table": "orders", "item_id": "6823c127b0b2b5fd6aa38567", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "6823c127b0b2b5fd6aa38567", "userId": "6804c96ab55244614aadb0e8", "items": [{"produce": "681b58c7b0b2b5fd6aa3655d", "quantity": 60, "totalPrice": 150000, "_id": "6823c127b0b2b5fd6aa38564", "createdAt": "2025-05-13T22:01:11.431Z", "updatedAt": "2025-05-13T22:01:11.587Z", "__v": 0}], "totalCost": 150000, "shippingFee": 5250.000000000001, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Completed", "shippingAddress": "99, Agboyi road, Alapere, Kosofe, Lagos", "shippedDate": null, "finalTotalCost": 155250, "paymentDate": "2025-05-13T22:01:11.586Z", "createdAt": "2025-05-13T22:01:11.587Z", "updatedAt": "2025-05-13T22:01:29.694Z", "__v": 0}}, {"table": "orders", "item_id": "682787e3b0b2b5fd6aa392a2", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "682787e3b0b2b5fd6aa392a2", "userId": "681a121eb0b2b5fd6aa35b08", "items": [{"produce": "681b274db0b2b5fd6aa35f19", "quantity": 1000, "totalPrice": 1350000, "_id": "682787e2b0b2b5fd6aa3929f", "createdAt": "2025-05-16T18:45:54.972Z", "updatedAt": "2025-05-16T18:45:55.138Z", "__v": 0}], "totalCost": 1350000, "shippingFee": 47250.00000000001, "status": "Processing", "paymentMethod": "Paystack", "paymentStatus": "Completed", "shippingAddress": "18, <PERSON><PERSON><PERSON><PERSON> , Epe , Epe, Lagos", "shippedDate": null, "finalTotalCost": 1397250, "paymentDate": "2025-05-16T18:45:55.137Z", "createdAt": "2025-05-16T18:45:55.138Z", "updatedAt": "2025-05-16T18:46:53.762Z", "__v": 0}}, {"table": "orders", "item_id": "684eff96a16b26e039a79972", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "684eff96a16b26e039a79972", "userId": "6804c96ab55244614aadb0e8", "items": [{"produce": "684b07c3a16b26e039a78d2a", "quantity": 3000, "totalPrice": 5100000, "_id": "684eff96a16b26e039a7996f", "createdAt": "2025-06-15T17:15:02.387Z", "updatedAt": "2025-06-15T17:15:02.549Z", "__v": 0}], "totalCost": 5100000, "shippingFee": 178500.00000000003, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "99, Agboyi road, Alapere, Kosofe, Lagos", "shippedDate": null, "finalTotalCost": 5278500, "paymentDate": "2025-06-15T17:15:02.548Z", "createdAt": "2025-06-15T17:15:02.549Z", "updatedAt": "2025-06-15T17:15:02.549Z", "__v": 0}}, {"table": "orders", "item_id": "684f1e97a16b26e039a79ac5", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "684f1e97a16b26e039a79ac5", "userId": "683187c6b0b2b5fd6aa3af36", "items": [{"produce": "681b274db0b2b5fd6aa35f19", "quantity": 600, "totalPrice": 810000, "_id": "684f1e97a16b26e039a79ac2", "createdAt": "2025-06-15T19:27:19.439Z", "updatedAt": "2025-06-15T19:27:19.589Z", "__v": 0}], "totalCost": 810000, "shippingFee": 28350.000000000004, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "126B, Aggregates Road, Eweko, Epe", "shippedDate": null, "finalTotalCost": 838350, "paymentDate": "2025-06-15T19:27:19.588Z", "createdAt": "2025-06-15T19:27:19.589Z", "updatedAt": "2025-06-15T19:27:19.589Z", "__v": 0}}, {"table": "orders", "item_id": "686394bf0a28ae215774310e", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "686394bf0a28ae215774310e", "userId": "683187c6b0b2b5fd6aa3af36", "items": [{"produce": "684b07c3a16b26e039a78d2a", "quantity": 3000, "totalPrice": 5100000, "_id": "686394bf0a28ae215774310b", "createdAt": "2025-07-01T07:56:47.774Z", "updatedAt": "2025-07-01T07:56:47.937Z", "__v": 0}], "totalCost": 5100000, "shippingFee": 178500.00000000003, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON>, <PERSON>beju<PERSON><PERSON><PERSON><PERSON>, Lagos", "shippedDate": null, "finalTotalCost": 5278500, "paymentDate": "2025-07-01T07:56:47.936Z", "createdAt": "2025-07-01T07:56:47.938Z", "updatedAt": "2025-07-01T07:56:47.938Z", "__v": 0}}, {"table": "orders", "item_id": "686e5d620a28ae21577456ef", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "686e5d620a28ae21577456ef", "userId": "686e5ba30a28ae2157745686", "items": [{"produce": "684b07c3a16b26e039a78d2a", "quantity": 3000, "totalPrice": 5100000, "_id": "686e5d610a28ae21577456ec", "createdAt": "2025-07-09T12:15:29.944Z", "updatedAt": "2025-07-09T12:15:30.101Z", "__v": 0}], "totalCost": 5100000, "shippingFee": 178500.00000000003, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "1, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Lagos", "shippedDate": null, "finalTotalCost": 5278500, "paymentDate": "2025-07-09T12:15:30.100Z", "createdAt": "2025-07-09T12:15:30.101Z", "updatedAt": "2025-07-09T12:15:30.101Z", "__v": 0}}, {"table": "orders", "item_id": "686faa600a28ae2157745cb1", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "686faa600a28ae2157745cb1", "userId": "6803a046b2e70cc02481da14", "items": [{"produce": "681b274db0b2b5fd6aa35f19", "quantity": 2000, "totalPrice": 2700000, "_id": "686faa5f0a28ae2157745cae", "createdAt": "2025-07-10T11:56:15.978Z", "updatedAt": "2025-07-10T11:56:16.146Z", "__v": 0}], "totalCost": 2700000, "shippingFee": 94500.00000000001, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "16, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ue", "shippedDate": null, "finalTotalCost": 2794500, "paymentDate": "2025-07-10T11:56:16.145Z", "createdAt": "2025-07-10T11:56:16.146Z", "updatedAt": "2025-07-10T11:56:16.146Z", "__v": 0}}, {"table": "orders", "item_id": "686fb1500a28ae2157745d0e", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "686fb1500a28ae2157745d0e", "userId": "683187c6b0b2b5fd6aa3af36", "items": [{"produce": "684b07c3a16b26e039a78d2a", "quantity": 3000, "totalPrice": 5100000, "_id": "686fb1500a28ae2157745d0b", "createdAt": "2025-07-10T12:25:52.698Z", "updatedAt": "2025-07-10T12:25:52.866Z", "__v": 0}], "totalCost": 5100000, "shippingFee": 178500.00000000003, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "126B, Aggregates Road, Eweko, Epe", "shippedDate": null, "finalTotalCost": 5278500, "paymentDate": "2025-07-10T12:25:52.865Z", "createdAt": "2025-07-10T12:25:52.866Z", "updatedAt": "2025-07-10T12:25:52.866Z", "__v": 0}}, {"table": "orders", "item_id": "686fb1560a28ae2157745d23", "error": "insert or update on table \"orders\" violates foreign key constraint \"FK_67eda0a81584d518eb146f4dbf9\"", "item_data": {"_id": "686fb1560a28ae2157745d23", "userId": "683187c6b0b2b5fd6aa3af36", "items": [{"produce": "684b07c3a16b26e039a78d2a", "quantity": 3000, "totalPrice": 5100000, "_id": "686fb1560a28ae2157745d20", "createdAt": "2025-07-10T12:25:58.340Z", "updatedAt": "2025-07-10T12:25:58.495Z", "__v": 0}], "totalCost": 5100000, "shippingFee": 178500.00000000003, "status": "Processing", "paymentMethod": "Centiiv", "paymentStatus": "Pending", "shippingAddress": "126B, Aggregates Road, Eweko, Epe", "shippedDate": null, "finalTotalCost": 5278500, "paymentDate": "2025-07-10T12:25:58.495Z", "createdAt": "2025-07-10T12:25:58.496Z", "updatedAt": "2025-07-10T12:25:58.496Z", "__v": 0}}, {"table": "transactions", "item_id": "67f28d8041947d7dfd8ddfd3", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "67f28d8041947d7dfd8ddfd3", "user": "67e7372f71afe8262a5b365d", "order": "67f28d7f41947d7dfd8ddfcb", "totalAmount": 444000, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-6055FF162CA4", "processed": false, "createdAt": "2025-04-06T14:19:44.294Z", "updatedAt": "2025-04-06T14:19:44.294Z", "__v": 0}}, {"table": "transactions", "item_id": "67f28dbe41947d7dfd8ddff5", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "67f28dbe41947d7dfd8ddff5", "user": "67e7372f71afe8262a5b365d", "order": "67f28dbd41947d7dfd8ddfec", "totalAmount": 444000, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-B903EEE74BD7", "processed": false, "createdAt": "2025-04-06T14:20:46.496Z", "updatedAt": "2025-04-06T14:20:46.496Z", "__v": 0}}, {"table": "transactions", "item_id": "67f28e2241947d7dfd8de013", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "67f28e2241947d7dfd8de013", "user": "67e7372f71afe8262a5b365d", "order": "67f28e2241947d7dfd8de00a", "totalAmount": 444000, "status": "Pending", "paymentMethod": "CentiivPay", "paymentType": "Purchase", "reference": "EWKTRX-0D482E9BF8B5", "processed": false, "createdAt": "2025-04-06T14:22:26.615Z", "updatedAt": "2025-04-06T14:22:26.615Z", "__v": 0}}, {"table": "transactions", "item_id": "67fb07abf6e63e904e7dc478", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "67fb07abf6e63e904e7dc478", "user": "67e83a1b71afe8262a5b5230", "order": "67fb07abf6e63e904e7dc470", "totalAmount": 148740, "status": "Pending", "paymentMethod": "CentiivPay", "paymentType": "Purchase", "reference": "EWKTRX-C166FD75F79B", "processed": false, "createdAt": "2025-04-13T00:39:07.919Z", "updatedAt": "2025-04-13T00:39:07.919Z", "__v": 0}}, {"table": "transactions", "item_id": "67fc0811f6e63e904e7ddf5b", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "67fc0811f6e63e904e7ddf5b", "user": "67e822c071afe8262a5b4f5e", "order": "67fc0810f6e63e904e7ddf53", "totalAmount": 333000, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-D5389258C4B6", "processed": false, "createdAt": "2025-04-13T18:53:05.820Z", "updatedAt": "2025-04-13T18:53:05.820Z", "__v": 0}}, {"table": "transactions", "item_id": "6803f51cad51c892e5b23323", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6803f51cad51c892e5b23323", "user": "6803a046b2e70cc02481da14", "order": "6803f51bad51c892e5b2331a", "totalAmount": 310800, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-5BB992F315B8", "processed": false, "createdAt": "2025-04-19T19:10:20.259Z", "updatedAt": "2025-04-19T19:10:20.259Z", "__v": 0}}, {"table": "transactions", "item_id": "6803f66fad51c892e5b23348", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6803f66fad51c892e5b23348", "user": "6803a046b2e70cc02481da14", "order": "6803f66ead51c892e5b2333f", "totalAmount": 310800, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-1F27976E9A5C", "processed": false, "createdAt": "2025-04-19T19:15:59.073Z", "updatedAt": "2025-04-19T19:15:59.073Z", "__v": 0}}, {"table": "transactions", "item_id": "6803f773c8161942c985b1b5", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6803f773c8161942c985b1b5", "user": "6803a046b2e70cc02481da14", "order": "6803f773c8161942c985b1ac", "totalAmount": 310800, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-738EFAEB6E38", "processed": false, "createdAt": "2025-04-19T19:20:19.855Z", "updatedAt": "2025-04-19T19:20:19.855Z", "__v": 0}}, {"table": "transactions", "item_id": "6803f7d6c8161942c985b1e3", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6803f7d6c8161942c985b1e3", "user": "6803a046b2e70cc02481da14", "order": "6803f7d5c8161942c985b1da", "totalAmount": 310800, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-487E7AFAB27D", "processed": false, "createdAt": "2025-04-19T19:21:58.090Z", "updatedAt": "2025-04-19T19:21:58.090Z", "__v": 0}}, {"table": "transactions", "item_id": "6803f83dc8161942c985b22b", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6803f83dc8161942c985b22b", "user": "6803a046b2e70cc02481da14", "order": "6803f83cc8161942c985b222", "totalAmount": 310800, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-82DFA0058826", "processed": false, "createdAt": "2025-04-19T19:23:41.073Z", "updatedAt": "2025-04-19T19:23:41.073Z", "__v": 0}}, {"table": "transactions", "item_id": "6804c692c8161942c985b510", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6804c692c8161942c985b510", "user": "6803a046b2e70cc02481da14", "order": "6804c691c8161942c985b507", "totalAmount": 310800, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-0C82A1DB35CA", "processed": false, "createdAt": "2025-04-20T10:04:02.275Z", "updatedAt": "2025-04-20T10:04:02.275Z", "__v": 0}}, {"table": "transactions", "item_id": "6804de520479bf9f63e2cda5", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6804de520479bf9f63e2cda5", "user": "6804c96ab55244614aadb0e8", "order": "6804de520479bf9f63e2cd9a", "totalAmount": 428904, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-ADA39485C693", "processed": false, "createdAt": "2025-04-20T11:45:22.696Z", "updatedAt": "2025-04-20T11:45:22.696Z", "__v": 0}}, {"table": "transactions", "item_id": "6804fd0c0479bf9f63e2cf35", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6804fd0c0479bf9f63e2cf35", "user": "6804c96ab55244614aadb0e8", "order": "6804fd0b0479bf9f63e2cf2c", "totalAmount": 1398600, "status": "Completed", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-BF4EBD21F2EA", "processed": true, "createdAt": "2025-04-20T13:56:28.345Z", "updatedAt": "2025-04-21T02:37:34.587Z", "__v": 0}}, {"table": "transactions", "item_id": "6805793f0479bf9f63e2d0c9", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6805793f0479bf9f63e2d0c9", "user": "6803a046b2e70cc02481da14", "order": "6805793f0479bf9f63e2d0c0", "totalAmount": 310800, "status": "Completed", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-EB3B01ED1B87", "processed": true, "createdAt": "2025-04-20T22:46:23.860Z", "updatedAt": "2025-04-21T02:16:37.106Z", "__v": 0}}, {"table": "transactions", "item_id": "68059c720479bf9f63e2d1d5", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "68059c720479bf9f63e2d1d5", "user": "6803a046b2e70cc02481da14", "order": "68059c720479bf9f63e2d1cc", "totalAmount": 393162, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-0DE7FC0AD4A8", "processed": false, "createdAt": "2025-04-21T01:16:34.853Z", "updatedAt": "2025-04-21T01:16:34.853Z", "__v": 0}}, {"table": "transactions", "item_id": "68059dee0479bf9f63e2d218", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "68059dee0479bf9f63e2d218", "user": "6803a046b2e70cc02481da14", "order": "68059ded0479bf9f63e2d20f", "totalAmount": 349650, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-908C7E79E212", "processed": false, "createdAt": "2025-04-21T01:22:54.553Z", "updatedAt": "2025-04-21T01:22:54.553Z", "__v": 0}}, {"table": "transactions", "item_id": "6805a1a70479bf9f63e2d283", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6805a1a70479bf9f63e2d283", "user": "6803a046b2e70cc02481da14", "order": "6805a1a70479bf9f63e2d27a", "totalAmount": 133200, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-1EAA3FE68D0B", "processed": false, "createdAt": "2025-04-21T01:38:47.882Z", "updatedAt": "2025-04-21T01:38:47.882Z", "__v": 0}}, {"table": "transactions", "item_id": "6805aa615d902a768971cacd", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6805aa615d902a768971cacd", "user": "6803a046b2e70cc02481da14", "order": "6805aa615d902a768971cac4", "totalAmount": 273060, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-93C216310087", "processed": false, "createdAt": "2025-04-21T02:16:01.867Z", "updatedAt": "2025-04-21T02:16:01.867Z", "__v": 0}}, {"table": "transactions", "item_id": "680680175d902a768971cda0", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "680680175d902a768971cda0", "user": "6803a046b2e70cc02481da14", "order": "680680175d902a768971cd95", "totalAmount": 943500, "status": "Completed", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-0FB31CC646E7", "processed": true, "createdAt": "2025-04-21T17:27:51.630Z", "updatedAt": "2025-04-21T17:28:13.643Z", "__v": 0}}, {"table": "transactions", "item_id": "68068b325d902a768971ce3f", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "68068b325d902a768971ce3f", "user": "6803a046b2e70cc02481da14", "order": "68068b325d902a768971ce34", "totalAmount": 610500, "status": "Completed", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-B0FE145C9C6F", "processed": true, "createdAt": "2025-04-21T18:15:14.795Z", "updatedAt": "2025-04-21T18:15:46.549Z", "__v": 0}}, {"table": "transactions", "item_id": "6811ea84b0b2b5fd6aa34292", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6811ea84b0b2b5fd6aa34292", "user": "6803a046b2e70cc02481da14", "order": "6811ea83b0b2b5fd6aa34287", "totalAmount": 532800, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-2031737DC108", "processed": false, "createdAt": "2025-04-30T09:16:52.582Z", "updatedAt": "2025-04-30T09:16:52.582Z", "__v": 0}}, {"table": "transactions", "item_id": "6813fed8b0b2b5fd6aa3483a", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6813fed8b0b2b5fd6aa3483a", "user": "6804c96ab55244614aadb0e8", "order": "6813fed7b0b2b5fd6aa34831", "totalAmount": 277500, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-81FC4189424E", "processed": false, "createdAt": "2025-05-01T23:08:08.613Z", "updatedAt": "2025-05-01T23:08:08.613Z", "__v": 0}}, {"table": "transactions", "item_id": "6814033cb0b2b5fd6aa349e8", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6814033cb0b2b5fd6aa349e8", "user": "6804c96ab55244614aadb0e8", "order": "6814033bb0b2b5fd6aa349df", "totalAmount": 222000, "status": "Completed", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-39F8CC2DB97F", "processed": true, "createdAt": "2025-05-01T23:26:52.310Z", "updatedAt": "2025-05-01T23:27:04.467Z", "__v": 0}}, {"table": "transactions", "item_id": "681404b1b0b2b5fd6aa34a4e", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "681404b1b0b2b5fd6aa34a4e", "user": "6804c96ab55244614aadb0e8", "order": "681404b1b0b2b5fd6aa34a43", "totalAmount": 421800, "status": "Completed", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-EA34275D919B", "processed": true, "createdAt": "2025-05-01T23:33:05.771Z", "updatedAt": "2025-05-01T23:33:15.967Z", "__v": 0}}, {"table": "transactions", "item_id": "681b27cfb0b2b5fd6aa35f76", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "681b27cfb0b2b5fd6aa35f76", "user": "6804c96ab55244614aadb0e8", "order": "681b27cfb0b2b5fd6aa35f6d", "totalAmount": 749250, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-5A896E8AB956", "processed": false, "createdAt": "2025-05-07T09:28:47.972Z", "updatedAt": "2025-05-07T09:28:47.972Z", "__v": 0}}, {"table": "transactions", "item_id": "681b2803b0b2b5fd6aa35f8d", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "681b2803b0b2b5fd6aa35f8d", "user": "6804c96ab55244614aadb0e8", "order": "681b2803b0b2b5fd6aa35f83", "totalAmount": 749250, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-5D68077BEC46", "processed": false, "createdAt": "2025-05-07T09:29:39.786Z", "updatedAt": "2025-05-07T09:29:39.786Z", "__v": 0}}, {"table": "transactions", "item_id": "681b2825b0b2b5fd6aa35fa5", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "681b2825b0b2b5fd6aa35fa5", "user": "6804c96ab55244614aadb0e8", "order": "681b2825b0b2b5fd6aa35f9c", "totalAmount": 749250, "status": "Pending", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-AA71EC036873", "processed": false, "createdAt": "2025-05-07T09:30:13.804Z", "updatedAt": "2025-05-07T09:30:13.804Z", "__v": 0}}, {"table": "transactions", "item_id": "681b5f2fb0b2b5fd6aa367cc", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "681b5f2fb0b2b5fd6aa367cc", "user": "681b4c60b0b2b5fd6aa36220", "order": "681b5f2fb0b2b5fd6aa367c3", "totalAmount": 194250, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-CA9A0AFF37BD", "processed": false, "createdAt": "2025-05-07T13:25:03.986Z", "updatedAt": "2025-05-07T13:25:03.986Z", "__v": 0}}, {"table": "transactions", "item_id": "681f839ab0b2b5fd6aa378cb", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "681f839ab0b2b5fd6aa378cb", "user": "6803a046b2e70cc02481da14", "order": "681f8399b0b2b5fd6aa378c2", "totalAmount": 147075, "status": "Completed", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-8EDD0DE77417", "processed": true, "createdAt": "2025-05-10T16:49:30.303Z", "updatedAt": "2025-05-10T16:49:42.292Z", "__v": 0}}, {"table": "transactions", "item_id": "6823c128b0b2b5fd6aa38570", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "6823c128b0b2b5fd6aa38570", "user": "6804c96ab55244614aadb0e8", "order": "6823c127b0b2b5fd6aa38567", "totalAmount": 166500, "status": "Completed", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-EA17D477AAC6", "processed": true, "createdAt": "2025-05-13T22:01:12.939Z", "updatedAt": "2025-05-13T22:01:29.770Z", "__v": 0}}, {"table": "transactions", "item_id": "682787e4b0b2b5fd6aa392ab", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "682787e4b0b2b5fd6aa392ab", "user": "681a121eb0b2b5fd6aa35b08", "order": "682787e3b0b2b5fd6aa392a2", "totalAmount": 1498500, "status": "Completed", "paymentMethod": "Paystack", "paymentType": "Purchase", "reference": "EWKTRX-647FEE3190D7", "processed": true, "createdAt": "2025-05-16T18:45:56.012Z", "updatedAt": "2025-05-16T18:46:53.839Z", "__v": 0}}, {"table": "transactions", "item_id": "684eff97a16b26e039a7997b", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "684eff97a16b26e039a7997b", "user": "6804c96ab55244614aadb0e8", "order": "684eff96a16b26e039a79972", "totalAmount": 5661000, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-75F7CC60BCF8", "processed": false, "createdAt": "2025-06-15T17:15:03.175Z", "updatedAt": "2025-06-15T17:15:03.175Z", "__v": 0}}, {"table": "transactions", "item_id": "684f1e98a16b26e039a79ace", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "684f1e98a16b26e039a79ace", "user": "683187c6b0b2b5fd6aa3af36", "order": "684f1e97a16b26e039a79ac5", "totalAmount": 899100, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-F14EEA0EE8F2", "processed": false, "createdAt": "2025-06-15T19:27:20.269Z", "updatedAt": "2025-06-15T19:27:20.269Z", "__v": 0}}, {"table": "transactions", "item_id": "686394c00a28ae2157743117", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "686394c00a28ae2157743117", "user": "683187c6b0b2b5fd6aa3af36", "order": "686394bf0a28ae215774310e", "totalAmount": 5661000, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-3127E28D3C1C", "processed": false, "createdAt": "2025-07-01T07:56:48.681Z", "updatedAt": "2025-07-01T07:56:48.681Z", "__v": 0}}, {"table": "transactions", "item_id": "686e5d620a28ae21577456f8", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "686e5d620a28ae21577456f8", "user": "686e5ba30a28ae2157745686", "order": "686e5d620a28ae21577456ef", "totalAmount": 5661000, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-1B96C85D9CAF", "processed": false, "createdAt": "2025-07-09T12:15:30.745Z", "updatedAt": "2025-07-09T12:15:30.745Z", "__v": 0}}, {"table": "transactions", "item_id": "686faa610a28ae2157745cba", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "686faa610a28ae2157745cba", "user": "6803a046b2e70cc02481da14", "order": "686faa600a28ae2157745cb1", "totalAmount": 2997000, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-5F4EC1060424", "processed": false, "createdAt": "2025-07-10T11:56:17.034Z", "updatedAt": "2025-07-10T11:56:17.034Z", "__v": 0}}, {"table": "transactions", "item_id": "686fb1510a28ae2157745d17", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "686fb1510a28ae2157745d17", "user": "683187c6b0b2b5fd6aa3af36", "order": "686fb1500a28ae2157745d0e", "totalAmount": 5661000, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-599C17D70018", "processed": false, "createdAt": "2025-07-10T12:25:53.737Z", "updatedAt": "2025-07-10T12:25:53.737Z", "__v": 0}}, {"table": "transactions", "item_id": "686fb1570a28ae2157745d2d", "error": "insert or update on table \"transactions\" violates foreign key constraint \"FK_3cb0558ed36997f1d9ecc1118e7\"", "item_data": {"_id": "686fb1570a28ae2157745d2d", "user": "683187c6b0b2b5fd6aa3af36", "order": "686fb1560a28ae2157745d23", "totalAmount": 5661000, "status": "Pending", "paymentMethod": "Centiiv", "paymentType": "Purchase", "reference": "EWKTRX-899C5BBC949F", "processed": false, "createdAt": "2025-07-10T12:25:59.399Z", "updatedAt": "2025-07-10T12:25:59.399Z", "__v": 0}}]