[{"user_id": "14493c10-202f-41f0-b83f-b6ea8505772d", "email": "<EMAIL>", "error": "null value in column \"type\" of relation \"users\" violates not-null constraint", "user_data": {"id": "14493c10-202f-41f0-b83f-b6ea8505772d", "user_type": "BUYER", "username": "richmindset1597", "email": "<EMAIL>", "primary_phone": "+2348086675592", "secondary_phone": null, "first_name": "Bolarinwa", "last_name": "<PERSON><PERSON><PERSON>", "date_of_birth": null, "gender": null, "middle_name": null, "prefix": null, "profile_picture": null, "password": "$2b$10$DIPXYzjZeiGrVC4lM2ow0e3dbtpMneEAOBUaA60VLnqqSyoPKFpKC", "created_at": "2025-07-11T03:09:16.921Z", "updated_at": "2025-07-11T03:09:16.921Z", "verified": true, "is_active": true, "is_premium": false, "is_phone_verified": false, "is_email_verified": false, "last_login": "2025-04-04T14:56:13.401Z", "mongo_id": "67e0702f876cfba49d51016d", "source_collection": "buyers", "farm_name": null, "farm_location": null, "farm_size": null, "farm_address": null, "account_number": null, "account_name": null, "bank_name": null, "bank_branch": null, "business_name": null, "loyalty_points": 0, "delivery_preferences": null, "payment_methods": null}}, {"user_id": "3ce8d00a-4c96-484d-8580-97a6cd9e5417", "email": "<EMAIL>", "error": "null value in column \"type\" of relation \"users\" violates not-null constraint", "user_data": {"id": "3ce8d00a-4c96-484d-8580-97a6cd9e5417", "user_type": "BUYER", "username": "seedorf75", "email": "<EMAIL>", "primary_phone": "+*************", "secondary_phone": null, "first_name": "<PERSON><PERSON><PERSON> ", "last_name": "<PERSON>", "date_of_birth": null, "gender": null, "middle_name": null, "prefix": null, "profile_picture": null, "password": "$2b$10$OQen8ZZ/FMsoRuBeYTEffe5jx.gWD6SL4bEwq2oSl5NqvxfZB6mNS", "created_at": "2025-07-11T03:09:16.921Z", "updated_at": "2025-07-11T03:09:16.921Z", "verified": true, "is_active": true, "is_premium": false, "is_phone_verified": false, "is_email_verified": false, "last_login": "2025-04-13T22:10:19.479Z", "mongo_id": "67e7372f71afe8262a5b365d", "source_collection": "buyers", "farm_name": null, "farm_location": null, "farm_size": null, "farm_address": null, "account_number": null, "account_name": null, "bank_name": null, "bank_branch": null, "business_name": null, "loyalty_points": 0, "delivery_preferences": null, "payment_methods": null}}, {"user_id": "66f4f38b-564e-42b7-b057-4a1283a6b911", "email": "<EMAIL>", "error": "null value in column \"type\" of relation \"users\" violates not-null constraint", "user_data": {"id": "66f4f38b-564e-42b7-b057-4a1283a6b911", "user_type": "BUYER", "username": "sannidamilola7", "email": "<EMAIL>", "primary_phone": "+*************", "secondary_phone": null, "first_name": "<PERSON><PERSON>", "last_name": "Dam<PERSON><PERSON>", "date_of_birth": null, "gender": null, "middle_name": null, "prefix": null, "profile_picture": "https://res.cloudinary.com/do1iiylud/image/upload/v1744277342/4854073D-AE87-4993-BE01-FCF2898EC220_gsqies.jpg", "password": "$2b$10$Tmz4bhrKdp1ZmNko8F5.9.v/7q9yolFjxZe5M7qtP.RK2kCI68jbK", "created_at": "2025-07-11T03:09:16.921Z", "updated_at": "2025-07-11T03:09:16.921Z", "verified": true, "is_active": true, "is_premium": false, "is_phone_verified": false, "is_email_verified": false, "last_login": "2025-04-13T18:54:17.510Z", "mongo_id": "67e822c071afe8262a5b4f5e", "source_collection": "buyers", "farm_name": null, "farm_location": null, "farm_size": null, "farm_address": null, "account_number": null, "account_name": null, "bank_name": null, "bank_branch": null, "business_name": null, "loyalty_points": 0, "delivery_preferences": null, "payment_methods": null}}, {"user_id": "bd4a11cc-b7a5-4080-a497-3a259cf8bb84", "email": "<EMAIL>", "error": "null value in column \"type\" of relation \"users\" violates not-null constraint", "user_data": {"id": "bd4a11cc-b7a5-4080-a497-3a259cf8bb84", "user_type": "BUYER", "username": "adeboyemalik1", "email": "<EMAIL>", "primary_phone": "+*************", "secondary_phone": null, "first_name": "Adeboye", "last_name": "<PERSON><PERSON><PERSON>", "date_of_birth": null, "gender": null, "middle_name": null, "prefix": null, "profile_picture": null, "password": "$2b$10$ewn.5u1lgnmE8BqyKb3bRuE010DHtjb7TyCZf2DTjkdy.BvcUm9V2", "created_at": "2025-07-11T03:09:16.921Z", "updated_at": "2025-07-11T03:09:16.921Z", "verified": true, "is_active": true, "is_premium": false, "is_phone_verified": false, "is_email_verified": false, "last_login": "2025-04-13T00:23:29.172Z", "mongo_id": "67e83a1b71afe8262a5b5230", "source_collection": "buyers", "farm_name": null, "farm_location": null, "farm_size": null, "farm_address": null, "account_number": null, "account_name": null, "bank_name": null, "bank_branch": null, "business_name": null, "loyalty_points": 0, "delivery_preferences": null, "payment_methods": null}}, {"user_id": "da717eaf-9e58-40ba-aea3-ec05315b9e92", "email": "<EMAIL>", "error": "null value in column \"type\" of relation \"users\" violates not-null constraint", "user_data": {"id": "da717eaf-9e58-40ba-aea3-ec05315b9e92", "user_type": "BUYER", "username": "feezyakinwunmi001", "email": "<EMAIL>", "primary_phone": "+*************", "secondary_phone": null, "first_name": "AKINWUNMI", "last_name": "<PERSON><PERSON>", "date_of_birth": null, "gender": null, "middle_name": null, "prefix": null, "profile_picture": null, "password": "$2b$10$9taLXD78A710atbNFyTxGO8nuVrSumhRchyVxynP69EAFF6sV4UKC", "created_at": "2025-07-11T03:09:16.921Z", "updated_at": "2025-07-11T03:09:16.921Z", "verified": true, "is_active": true, "is_premium": false, "is_phone_verified": false, "is_email_verified": false, "last_login": "2025-04-04T08:33:32.355Z", "mongo_id": "67ef9649243ebd949ba51354", "source_collection": "buyers", "farm_name": null, "farm_location": null, "farm_size": null, "farm_address": null, "account_number": null, "account_name": null, "bank_name": null, "bank_branch": null, "business_name": null, "loyalty_points": 0, "delivery_preferences": null, "payment_methods": null}}, {"user_id": "5d38adf8-089a-4500-9e32-844c92e15a02", "email": "<EMAIL>", "error": "null value in column \"type\" of relation \"users\" violates not-null constraint", "user_data": {"id": "5d38adf8-089a-4500-9e32-844c92e15a02", "user_type": "BUYER", "username": "kuamdeen", "email": "<EMAIL>", "primary_phone": "+*************", "secondary_phone": null, "first_name": "Kuamdeen", "last_name": "<PERSON>", "date_of_birth": null, "gender": null, "middle_name": null, "prefix": null, "profile_picture": null, "password": "$2b$10$vVg/ced/VOI7QvhRHbXBiukmcWd6j0O7UO0fTisXtOLELLeEbtfea", "created_at": "2025-07-11T03:09:16.921Z", "updated_at": "2025-07-11T03:09:16.921Z", "verified": true, "is_active": true, "is_premium": false, "is_phone_verified": false, "is_email_verified": false, "last_login": "2025-04-07T22:34:49.574Z", "mongo_id": "67f2aed141947d7dfd8de3d5", "source_collection": "buyers", "farm_name": null, "farm_location": null, "farm_size": null, "farm_address": null, "account_number": null, "account_name": null, "bank_name": null, "bank_branch": null, "business_name": null, "loyalty_points": 0, "delivery_preferences": null, "payment_methods": null}}, {"user_id": "35da4614-5a87-4c06-bd4e-7fd8ac489f72", "email": "<EMAIL>", "error": "null value in column \"type\" of relation \"users\" violates not-null constraint", "user_data": {"id": "35da4614-5a87-4c06-bd4e-7fd8ac489f72", "user_type": "BUYER", "username": "r<PERSON>q<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "primary_phone": "+*************", "secondary_phone": null, "first_name": "Risquot", "last_name": "Balogun", "date_of_birth": null, "gender": null, "middle_name": null, "prefix": null, "profile_picture": null, "password": "$2b$10$YzMHDTfGK4ltjyS0VyLjtuVx6.m7oqZWg3rQILQSMPRrsikTPX18e", "created_at": "2025-07-11T03:09:16.921Z", "updated_at": "2025-07-11T03:09:16.921Z", "verified": false, "is_active": true, "is_premium": false, "is_phone_verified": false, "is_email_verified": false, "last_login": null, "mongo_id": "67fc00bff6e63e904e7dde60", "source_collection": "buyers", "farm_name": null, "farm_location": null, "farm_size": null, "farm_address": null, "account_number": null, "account_name": null, "bank_name": null, "bank_branch": null, "business_name": null, "loyalty_points": 0, "delivery_preferences": null, "payment_methods": null}}, {"user_id": "f869343e-5547-4960-b1e6-8ee31ccb6b39", "email": "<EMAIL>", "error": "null value in column \"type\" of relation \"users\" violates not-null constraint", "user_data": {"id": "f869343e-5547-4960-b1e6-8ee31ccb6b39", "user_type": "FARMER", "username": "ewekoconcept", "email": "<EMAIL>", "primary_phone": "+*************", "secondary_phone": null, "first_name": "Le<PERSON>", "last_name": "<PERSON>", "date_of_birth": null, "gender": null, "middle_name": null, "prefix": null, "profile_picture": "https://res.cloudinary.com/do1iiylud/image/upload/v1743867733/Sahee<PERSON>_<PERSON>_Pix_2_rmdbsg.jpg", "password": "$2b$10$g9kPdjJdEHS5C1y4ZcnN2eFyGmXeel8a02NA8yVqRAKBHM4VvxIjK", "created_at": "2025-07-11T03:09:16.921Z", "updated_at": "2025-07-11T03:09:16.921Z", "verified": true, "is_active": true, "is_premium": false, "is_phone_verified": false, "is_email_verified": false, "last_login": "2025-04-01T23:29:23.242Z", "mongo_id": "67e7381671afe8262a5b36b1", "source_collection": "farmers", "farm_name": "Eweko Farms", "farm_location": null, "farm_size": "10", "farm_address": null, "account_number": null, "account_name": null, "bank_name": null, "bank_branch": null, "business_name": null, "loyalty_points": 0, "delivery_preferences": null, "payment_methods": null}}, {"user_id": "ae772d8e-ffda-485f-af78-c5d7c94c9b5d", "email": "<EMAIL>", "error": "null value in column \"type\" of relation \"users\" violates not-null constraint", "user_data": {"id": "ae772d8e-ffda-485f-af78-c5d7c94c9b5d", "user_type": "FARMER", "username": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "primary_phone": "+*************", "secondary_phone": null, "first_name": "<PERSON><PERSON>", "last_name": "Ogunde ", "date_of_birth": null, "gender": null, "middle_name": null, "prefix": null, "profile_picture": null, "password": "$2b$10$NtczWZjEbFGIT8KWgAJlxOxlv70GQ3JturzfqbMbK8Nm087pH6vGW", "created_at": "2025-07-11T03:09:16.921Z", "updated_at": "2025-07-11T03:09:16.921Z", "verified": false, "is_active": true, "is_premium": false, "is_phone_verified": false, "is_email_verified": false, "last_login": null, "mongo_id": "67e8d1f071afe8262a5b6347", "source_collection": "farmers", "farm_name": "Global petad Farms ", "farm_location": null, "farm_size": null, "farm_address": null, "account_number": null, "account_name": null, "bank_name": null, "bank_branch": null, "business_name": null, "loyalty_points": 0, "delivery_preferences": null, "payment_methods": null}}, {"user_id": "48e339f4-85ff-4f9b-8fac-412d6b235829", "email": "<EMAIL>", "error": "null value in column \"type\" of relation \"users\" violates not-null constraint", "user_data": {"id": "48e339f4-85ff-4f9b-8fac-412d6b235829", "user_type": "FARMER", "username": "maidahfarms", "email": "<EMAIL>", "primary_phone": "+*************", "secondary_phone": null, "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Otunba", "date_of_birth": null, "gender": null, "middle_name": null, "prefix": null, "profile_picture": null, "password": "$2b$10$44HUvF8p/naDSjTzE5t1/O5oagxy7E0qO.NYoN4Li1FAp197UU/eW", "created_at": "2025-07-11T03:09:16.921Z", "updated_at": "2025-07-11T03:09:16.921Z", "verified": true, "is_active": true, "is_premium": false, "is_phone_verified": false, "is_email_verified": false, "last_login": null, "mongo_id": "67ee624a5f91cff528cc5598", "source_collection": "farmers", "farm_name": "Ma'idah Farms", "farm_location": null, "farm_size": null, "farm_address": null, "account_number": null, "account_name": null, "bank_name": null, "bank_branch": null, "business_name": null, "loyalty_points": 0, "delivery_preferences": null, "payment_methods": null}}]