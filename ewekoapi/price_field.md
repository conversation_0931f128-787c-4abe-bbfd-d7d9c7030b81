# Weekly Category Price Tracking

## Overview
This document outlines the implementation of a simplified weekly price tracking system that calculates and stores the average price for each produce category. The system maintains a historical record of these metrics to enable price trend analysis.

## Database Schema

### Collection: `WeeklyCategoryPrice`
```typescript
@Schema({
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
  collection: 'weekly_category_prices',
})
export class WeeklyCategoryPrice {
  @Prop({ type: Types.ObjectId, ref: 'Category', required: true })
  category: Types.ObjectId;

  @Prop({ type: Date, required: true, index: true })
  weekStartDate: Date;  // Start of the week (Monday)

  @Prop({ type: Number, required: true, min: 0 })
  averagePrice: number;

  @Prop({ type: Number, required: true, min: 0 })
  totalListings: number;
}
```

## API Endpoints

### 1. Get Latest Price Trends
```
GET /market/price-trends/latest
```

**Authentication**: Required (JWT)

**Response**:
```typescript
{
  "data": [
    {
      "categoryName": "Fruits",
      "currentPrice": 2.99,
      "previousPrice": 2.89
    },
    {
      "categoryName": "Vegetables",
      "currentPrice": 1.99,
      "previousPrice": 1.79
    }
  ]
}
```

### 2. Trigger Calculation (Admin)
```
POST /market/price-trends/calculate
```

Manually triggers the weekly price calculation.

## Implementation Files

### 1. Core Module Structure
```
src/weekly-price/
├── dto/
│   └── weekly-price.dto.ts    # Request/response DTOs
├── schema.ts                  # MongoDB schema
├── weekly-price.module.ts     # NestJS module
├── weekly-price.service.ts    # Business logic
└── weekly-price.controller.ts # API endpoints
```

### 2. Weekly Aggregation Job
A scheduled task calculates and stores weekly price averages every Sunday at midnight:

```typescript
@Cron(CronExpression.EVERY_WEEKEND)  // Runs Sunday at midnight
async calculateWeeklyAverages() {
  const weekStart = this.getStartOfWeek();
  const categories = await this.categoryModel.find({});
  
  for (const category of categories) {
    const result = await this.categoryModel.aggregate([
      { $match: { _id: category._id } },
      {
        $lookup: {
          from: 'produces',
          localField: '_id',
          foreignField: 'category',
          as: 'produces',
          pipeline: [
            { $match: { status: 'active' } },
            { $project: { price: 1 } }
          ]
        }
      },
      { $unwind: '$produces' },
      {
        $group: {
          _id: '$_id',
          averagePrice: { $avg: '$produces.price' },
          totalListings: { $sum: 1 }
        }
      }
    ]);

    if (result.length > 0 && result[0].totalListings > 0) {
      const { averagePrice, totalListings } = result[0];
      const roundedAvg = Math.round(averagePrice * 100) / 100;

      await this.weeklyPriceModel.findOneAndUpdate(
        { category: category._id, weekStartDate: weekStart },
        { $set: { averagePrice: roundedAvg, totalListings } },
        { upsert: true, new: true }
      );
    }
  }
}
```

## Key Features

1. **Simplified Data Model**:
   - Only stores essential price metrics (average price and listing count)
   - Efficient indexing on category and week start date

2. **Performance Optimizations**:
   - Single aggregation query for price calculations
   - Efficient lookup of current and previous week's data
   - Only includes categories with active listings

3. **Scheduled Processing**:
   - Automatic weekly calculation (Sunday midnight)
   - Manual trigger available for admin use
      const produces = await this.produceModel.find({
        category: category._id,
        status: 'active'  // Only include active listings
      });
    
    if (produces.length > 0) {
      const prices = produces.map(p => p.price);
      const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length;
      
      await this.weeklyPriceModel.create({
        category: category._id,
        weekStartDate: weekStart,
        averagePrice: avgPrice,
        minPrice: Math.min(...prices),
        maxPrice: Math.max(...prices),
        totalListings: prices.length
      });
    }
  }
}
```

### 3. Price Trend Service
```typescript
@Injectable()
export class PriceTrendService {
  constructor(
    @InjectModel(WeeklyCategoryPrice.name) 
    private weeklyPriceModel: Model<WeeklyCategoryPriceDocument>,
    @InjectModel(Category.name)
    private categoryModel: Model<CategoryDocument>
  ) {}

  async getCategoryTrends(categoryId: string, weeksBack: number = 12) {
    const category = await this.categoryModel.findById(categoryId);
    if (!category) {
      throw new NotFoundException('Category not found');
    }

    const currentDate = new Date();
    const startDate = new Date();
    startDate.setDate(currentDate.getDate() - (weeksBack * 7));

    const history = await this.weeklyPriceModel
      .find({
        category: categoryId,
        weekStartDate: { $gte: startDate }
      })
      .sort({ weekStartDate: 1 })
      .lean();

    if (history.length < 2) {
      return {
        category: {
          id: category._id,
          name: category.name,
          slug: category.slug
        },
        message: 'Insufficient data for trend analysis'
      };
    }

    const currentWeek = history[history.length - 1];
    const previousWeek = history[history.length - 2];
    const percentageChange = ((currentWeek.averagePrice - previousWeek.averagePrice) / 
                             previousWeek.averagePrice) * 100;

    return {
      category: {
        id: category._id,
        name: category.name,
        slug: category.slug
      },
      currentWeek: {
        ...currentWeek,
        weekStart: currentWeek.weekStartDate.toISOString().split('T')[0]
      },
      previousWeek: {
        ...previousWeek,
        weekStart: previousWeek.weekStartDate.toISOString().split('T')[0]
      },
      percentageChange: parseFloat(percentageChange.toFixed(2)),
      trend: percentageChange > 0 ? 'up' : percentageChange < 0 ? 'down' : 'stable',
      history: history.map(item => ({
        weekStart: item.weekStartDate.toISOString().split('T')[0],
        averagePrice: item.averagePrice,
        minPrice: item.minPrice,
        maxPrice: item.maxPrice,
        totalListings: item.totalListings
      }))
    };
  }
}
```

## Frontend Implementation

### Price Trend Chart Component
```jsx
function CategoryPriceTrend({ categoryId }) {
  const [trendData, setTrendData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTrends = async () => {
      try {
        const response = await fetch(`/api/market/price-trends/category/${categoryId}?weeksBack=12`);
        if (!response.ok) throw new Error('Failed to fetch price trends');
        const data = await response.json();
        setTrendData(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTrends();
  }, [categoryId]);

  if (loading) return <div>Loading price trends...</div>;
  if (error) return <div className="error">Error: {error}</div>;
  if (!trendData?.history?.length) return <div>No price history available</div>;

  const { currentWeek, previousWeek, percentageChange, trend } = trendData;
  const isIncrease = trend === 'up';
  const isDecrease = trend === 'down';

  return (
    <div className="price-trend">
      <div className="price-summary">
        <div className="current-price">
          <span className="label">Current Average:</span>
          <span className="value">₦{currentWeek.averagePrice.toLocaleString()}</span>
        </div>
        
        {previousWeek && (
          <div className="price-change">
            <span className="label">Change from last week:</span>
            <span className={`value ${isIncrease ? 'up' : isDecrease ? 'down' : ''}`}>
              {isIncrease ? '↑' : isDecrease ? '↓' : '→'} 
              {Math.abs(percentageChange)}%
            </span>
            <div className="tooltip">
              Last week: ₦{previousWeek.averagePrice.toLocaleString()}
            </div>
          </div>
        )}
      </div>
      
      <div className="price-chart">
        <LineChart
          data={trendData.history}
          xKey="weekStart"
          yKey="averagePrice"
          xLabel="Week"
          yLabel="Average Price (₦)"
        />
      </div>
      
      <div className="price-range">
        <div className="range-item">
          <span className="label">Lowest:</span>
          <span className="value">₦{currentWeek.minPrice.toLocaleString()}</span>
        </div>
        <div className="range-item">
          <span className="label">Highest:</span>
          <span className="value">₦{currentWeek.maxPrice.toLocaleString()}</span>
        </div>
        <div className="range-item">
          <span className="label">Listings:</span>
          <span className="value">{currentWeek.totalListings}</span>
        </div>
      </div>
    </div>
  );
}
```

## Security Considerations
1. Price trend data is read-only for all authenticated users (farmers and buyers)
2. Validate all input parameters for the API endpoints
3. Implement rate limiting to prevent abuse
4. Ensure proper error handling for missing or invalid data

## Performance Considerations
1. Add appropriate indexes for efficient querying:
   ```typescript
   WeeklyCategoryPriceSchema.index({ category: 1, weekStartDate: -1 });
   ```
2. Consider caching the results for frequently accessed categories
3. The weekly aggregation job should be optimized to handle large numbers of products
4. Consider implementing incremental updates if the dataset becomes very large

## Testing Strategy
1. Unit tests for price calculation and trend analysis
2. Integration tests for the weekly aggregation job
3. API tests for the price trends endpoint
4. Edge case testing for categories with few or no products

## Future Enhancements
1. Add regional price tracking (by state/LGA)
2. Implement price alerts for significant changes
3. Add price comparison between different time periods
4. Export functionality for price history data
5. Price forecasting based on historical trends
