@font-face {
  font-family: 'euclidBold', sans-serif;
  src: url('public/general/euclidBold.ttf') format('tff');
}

@font-face {
  font-family: 'euclidMedium', sans-serif;
  src: url('public/general/euclidMedium.ttf') format('tff');
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: 'euclidMedium';
}

html,
body,
:root {
  height: 100dvh;
}

body {
  overflow: hidden;
  font-family: 'euclidMedium';
}

main {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 40px;
  height: 100dvh;
  position: relative;

  background-image: url('./eweko.png'); /* Replace 'your-image-url.jpg' with the actual URL or path to your image */
  background-size: cover; /* Cover the entire container while maintaining aspect ratio */
  background-position: center; /* Center the background image */
  background-repeat: no-repeat; /* Do not repeat the background image */
}

div {
  background-color: rgba(0, 0, 0, 0.8);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

h1 {
  font-weight: bolder;
  font-family: 'euclidBold', sans-serif;
  color: #85b04c;
  z-index: 50;
}

a {
  background-color: #85b04c;
  color: white;
  padding: 12px 25px;
  z-index: 50;
  border-radius: 50px;
  text-decoration: none;
  font-weight: bold;
  font-family: 'euclidMedium', sans-serif;
}
