const { MongoClient } = require('mongodb');
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// MongoDB connection
const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';
const MONGODB_DATABASE = 'ewekoapi';

// PostgreSQL connection
const PG_CONFIG = {
  host: 'localhost',
  port: 5432,
  user: 'ewekoadmin',
  password: '',
  database: 'ewekodb',
};

async function getMongoCollections() {
  const client = new MongoClient(MONGODB_URI);
  await client.connect();
  const db = client.db(MONGODB_DATABASE);
  
  const collections = await db.listCollections().toArray();
  const collectionData = {};
  
  for (const collection of collections) {
    const name = collection.name;
    const count = await db.collection(name).countDocuments();
    const sample = await db.collection(name).findOne();
    
    collectionData[name] = {
      count,
      sampleFields: sample ? Object.keys(sample) : [],
      sample: sample
    };
  }
  
  await client.close();
  return collectionData;
}

async function getPostgreSQLTables() {
  const client = new Client(PG_CONFIG);
  await client.connect();
  
  // Get all tables
  const tablesResult = await client.query(`
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
    AND table_name != 'migrations'
  `);
  
  const tableData = {};
  
  for (const table of tablesResult.rows) {
    const tableName = table.table_name;
    
    // Get column information
    const columnsResult = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = $1
      ORDER BY ordinal_position
    `, [tableName]);
    
    // Get row count
    const countResult = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
    
    tableData[tableName] = {
      count: parseInt(countResult.rows[0].count),
      columns: columnsResult.rows.map(col => ({
        name: col.column_name,
        type: col.data_type,
        nullable: col.is_nullable === 'YES',
        default: col.column_default
      }))
    };
  }
  
  await client.end();
  return tableData;
}

function getTypeORMEntities() {
  const entitiesDir = path.join(__dirname, 'src');
  const entities = {};
  
  function scanDirectory(dir, moduleName = '') {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory() && item === 'entities') {
        // Found an entities directory
        const entityFiles = fs.readdirSync(itemPath);
        
        for (const entityFile of entityFiles) {
          if (entityFile.endsWith('.entity.ts')) {
            const entityPath = path.join(itemPath, entityFile);
            const content = fs.readFileSync(entityPath, 'utf8');
            
            const entityName = entityFile.replace('.entity.ts', '');
            const parentModule = path.basename(path.dirname(itemPath));
            
            // Extract field names from the entity file
            const fieldMatches = content.match(/@Column\(\)[\s\S]*?(\w+):/g) || [];
            const fields = fieldMatches.map(match => {
              const fieldName = match.match(/(\w+):$/);
              return fieldName ? fieldName[1] : null;
            }).filter(Boolean);
            
            // Also look for @PrimaryGeneratedColumn
            const primaryMatches = content.match(/@PrimaryGeneratedColumn\(\)[\s\S]*?(\w+):/g) || [];
            const primaryFields = primaryMatches.map(match => {
              const fieldName = match.match(/(\w+):$/);
              return fieldName ? fieldName[1] : null;
            }).filter(Boolean);
            
            entities[entityName] = {
              module: parentModule,
              fields: [...primaryFields, ...fields],
              filePath: entityPath
            };
          }
        }
      } else if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scanDirectory(itemPath, item);
      }
    }
  }
  
  scanDirectory(entitiesDir);
  return entities;
}

async function generateComparisonReport() {
  console.log('🔍 Starting comprehensive schema comparison...\n');
  
  try {
    console.log('📊 Fetching MongoDB collections...');
    const mongoData = await getMongoCollections();
    
    console.log('🐘 Fetching PostgreSQL tables...');
    const pgData = await getPostgreSQLTables();
    
    console.log('🏗️  Analyzing TypeORM entities...');
    const entityData = getTypeORMEntities();
    
    console.log('\n' + '='.repeat(80));
    console.log('📋 COMPREHENSIVE SCHEMA COMPARISON REPORT');
    console.log('='.repeat(80));
    
    // MongoDB Collections Summary
    console.log('\n📦 MONGODB COLLECTIONS:');
    console.log('-'.repeat(50));
    Object.entries(mongoData).forEach(([name, data]) => {
      console.log(`${name}: ${data.count} documents`);
      console.log(`  Fields: ${data.sampleFields.join(', ')}`);
    });
    
    // PostgreSQL Tables Summary
    console.log('\n🐘 POSTGRESQL TABLES:');
    console.log('-'.repeat(50));
    Object.entries(pgData).forEach(([name, data]) => {
      console.log(`${name}: ${data.count} rows`);
      console.log(`  Columns: ${data.columns.map(col => `${col.name}(${col.type})`).join(', ')}`);
    });
    
    // TypeORM Entities Summary
    console.log('\n🏗️  TYPEORM ENTITIES:');
    console.log('-'.repeat(50));
    Object.entries(entityData).forEach(([name, data]) => {
      console.log(`${name} (${data.module}): ${data.fields.join(', ')}`);
    });
    
    // Data Migration Status
    console.log('\n📊 DATA MIGRATION STATUS:');
    console.log('-'.repeat(50));
    
    const migrationStatus = [];
    
    // Check users migration
    const mongoUsers = (mongoData.buyers?.count || 0) + (mongoData.farmers?.count || 0);
    const pgUsers = pgData.users?.count || 0;
    migrationStatus.push({
      collection: 'users (buyers + farmers)',
      mongo: mongoUsers,
      postgres: pgUsers,
      status: mongoUsers === pgUsers ? '✅ COMPLETE' : '⚠️  PARTIAL'
    });
    
    // Check other collections
    const commonCollections = ['categories', 'produces', 'preferences', 'notifications'];
    commonCollections.forEach(collection => {
      const mongoCount = mongoData[collection]?.count || 0;
      const pgCount = pgData[collection]?.count || 0;
      migrationStatus.push({
        collection,
        mongo: mongoCount,
        postgres: pgCount,
        status: mongoCount <= pgCount ? '✅ COMPLETE' : '⚠️  PARTIAL'
      });
    });
    
    migrationStatus.forEach(status => {
      console.log(`${status.collection}: MongoDB(${status.mongo}) → PostgreSQL(${status.postgres}) ${status.status}`);
    });
    
    // Schema Mismatches
    console.log('\n⚠️  POTENTIAL SCHEMA ISSUES:');
    console.log('-'.repeat(50));
    
    let issuesFound = false;
    
    // Check if all PostgreSQL tables have corresponding entities
    Object.keys(pgData).forEach(tableName => {
      const entityName = tableName.replace(/s$/, ''); // Remove plural 's'
      const alternativeNames = [
        tableName,
        tableName.replace(/_/g, ''), // Remove underscores
        tableName.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(''), // PascalCase
      ];
      
      const hasEntity = alternativeNames.some(name => entityData[name] || entityData[name.toLowerCase()]);
      
      if (!hasEntity) {
        console.log(`❌ Table '${tableName}' has no corresponding TypeORM entity`);
        issuesFound = true;
      }
    });
    
    if (!issuesFound) {
      console.log('✅ No major schema mismatches detected');
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('📋 SUMMARY');
    console.log('='.repeat(80));
    console.log(`MongoDB Collections: ${Object.keys(mongoData).length}`);
    console.log(`PostgreSQL Tables: ${Object.keys(pgData).length}`);
    console.log(`TypeORM Entities: ${Object.keys(entityData).length}`);
    console.log(`Total MongoDB Documents: ${Object.values(mongoData).reduce((sum, data) => sum + data.count, 0)}`);
    console.log(`Total PostgreSQL Rows: ${Object.values(pgData).reduce((sum, data) => sum + data.count, 0)}`);
    
  } catch (error) {
    console.error('❌ Schema comparison failed:', error);
    throw error;
  }
}

// Run the comparison
if (require.main === module) {
  generateComparisonReport().catch(console.error);
}

module.exports = { generateComparisonReport };
