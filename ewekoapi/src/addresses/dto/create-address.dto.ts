import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateAddressDto {
  @ApiProperty({
    description: 'The ID of the user this address belongs to.',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  user_id: string;

  @ApiProperty({
    description: 'House or building number.',
    example: '12A',
    default: '12A',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  house_number: string;

  @ApiProperty({
    description: 'Street name of the address.',
    example: 'Main Street',
    default: 'Main Street',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  street_name: string;

  @ApiProperty({
    description: 'Local Government Area (LGA) of the address.',
    example: 'Epe',
    default: 'Epe',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  lga: string;

  @ApiProperty({
    description: 'Community within the LGA (optional).',
    example: 'Ijebu-Ode',
    required: false,
  })
  @IsString()
  @IsOptional()
  community?: string;

  @ApiProperty({
    description: 'State where the address is located.',
    example: 'Lagos',
    default: 'Lagos',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  state: string;


  @ApiProperty({
    description: 'Indicates whether this is the default address.',
    example: true,
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  is_default?: boolean;
}
