import { ApiProperty } from '@nestjs/swagger';

export class ResponseAddressDto {
  @ApiProperty({
    description: 'The ID of the user this address belongs to.',
    example: '63f8c92b9b84aeb0c8d4c021',
  })
  user_id: string;

  @ApiProperty({
    description: 'House or building number.',
    example: '12A',
  })
  house_number: string;

  @ApiProperty({
    description: 'Street name of the address.',
    example: 'Main Street',
  })
  street_name: string;

  @ApiProperty({
    description: 'Local Government Area (LGA) of the address.',
    example: 'Ikeja',
  })
  lga: string;

  @ApiProperty({
    description: 'State where the address is located.',
    example: 'Lagos',
  })
  state: string;

  @ApiProperty({
    description: 'Country where the address is located.',
    example: 'Nigeria',
  })
  country: string;

  @ApiProperty({
    description: 'Indicates whether this is the default address.',
    example: true,
  })
  is_default: boolean;

}
