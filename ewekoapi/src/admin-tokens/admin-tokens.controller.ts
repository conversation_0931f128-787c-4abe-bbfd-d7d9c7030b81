import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import {
  CreateAdminTokenDto,
  RenewAdminTokenDto,
} from './dto/create-admin-token.dto';
import { AdminTokensService } from './admin-tokens.service';
import { ApiTags } from '@nestjs/swagger';
import { EwekoAuthGuard, RolesGuard } from 'src/auth/jwt.guard';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { UserType } from 'src/shared/enums';

@ApiTags('admin-tokens')
@Controller('admin-tokens')
@UseGuards(EwekoAuthGuard, RolesGuard)
@Roles(UserType.ADMIN)
export class AdminTokensController {
  constructor(private readonly adminTokensService: AdminTokensService) {}

  // @Post()
  // create(@Body() createAdminTokenDto: CreateAdminTokenDto) {
  //   return this.adminTokensService.create(createAdminTokenDto);
  // }

  // @Post('renew')
  // renew(@Body() renewAdminTokenDto: RenewAdminTokenDto) {
  //   return this.adminTokensService.renew(renewAdminTokenDto);
  // }

  // @Get()
  // findAll() {
  //   return this.adminTokensService.findAll();
  // }

  // @Patch('deactivate/:token')
  // deactivateToken(@Param('token') token: string) {
  //   return this.adminTokensService.deactivateToken(token);
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.adminTokensService.findOne(id);
  // }

  // @Delete(':token')
  // deleteToken(@Param('token') token: string) {
  //   return this.adminTokensService.deleteToken(token);
  // }
}
