import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsBoolean,
  IsOptional,
  Matches,
  Length,
} from 'class-validator';

export class CreateAdminTokenDto {
  @ApiProperty({
    description: 'The ID of the admin associated with the token.',
    example: 'uuid-string',
    required: true,
  })
  @IsString()
  admin_id: string;

  @ApiProperty({
    description: 'Indicates if the token is for a super admin.',
    example: false,
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  is_super?: boolean;
}

export class RenewAdminTokenDto {
  @ApiProperty({
    description: 'Old admin token to be renewed.',
    example: '3WKABCDE1234567',
    required: true,
  })
  @IsString()
  @Matches(/^3WK/, {
    message: 'Token must start with the prefix "3WK".',
  })
  @Length(16, 16, {
    message: 'Token must be exactly 16 characters long.',
  })
  admin_token: string;
}

export class DeactivateTokenDto {
  @ApiProperty({
    description: 'The token to be deactivated.',
    example: '3WK1234567890',
    required: true,
  })
  @IsString()
  @Length(16, 16, { message: 'Token must be exactly 16 characters long.' })
  token: string;
}

export class DeleteTokenDto {
  @ApiProperty({
    description: 'The token to be deactivated.',
    example: '3WK1234567890',
    required: true,
  })
  @IsString()
  @Length(16, 16, { message: 'Token must be exactly 16 characters long.' })
  token: string;
}
