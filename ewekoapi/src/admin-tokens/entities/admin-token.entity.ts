import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity('admin_tokens')
export class AdminToken {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  admin_id: string;

  @Column()
  token: string;

  @Column({ type: 'enum', enum: ['access', 'refresh', 'reset'], default: 'access' })
  type: string;

  @Column({ type: 'timestamp' })
  expires_at: Date;

  @Column({ default: false })
  used: boolean;

  @Column({ default: false })
  revoked: boolean;

  @Column({ nullable: true })
  revoked_reason: string;

  @Column({ type: 'timestamp', nullable: true })
  revoked_at: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships - using forward reference to avoid circular dependency
  @ManyToOne('Admin', 'tokens')
  @JoinColumn({ name: 'admin_id' })
  admin: any;
}
