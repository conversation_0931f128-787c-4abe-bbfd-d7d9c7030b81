import {
  <PERSON><PERSON><PERSON>,
  ClassSerializerInterceptor,
  MiddlewareConsumer,
  NestModule,
  Logger,
} from '@nestjs/common';
import { SentryGlobalFilter, SentryModule } from '@sentry/nestjs/setup';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { LoggerInterceptor } from 'src/shared/logger.interceptor';
import { CaseInterceptor } from 'src/shared/case.interceptor';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { OtpsModule } from './otps/otps.module';
import { WeeklyPriceModule } from './weekly-price/weekly-price.module';
import { AddressesModule } from './addresses/addresses.module';
import { AdminTokensModule } from './admin-tokens/admin-tokens.module';
import { NotificationsModule } from './notifications/notifications.module';
import { CategoryModule } from './category/category.module';
import { ProduceModule } from './produce/produce.module';
import { CartModule } from './cart/cart.module';
import { OrdersModule } from './orders/orders.module';
import { PaymentsModule } from './payments/payments.module';
import { TransactionsModule } from './transactions/transactions.module';
import { CronsModule } from './crons/crons.module';
import { WalletsModule } from './wallets/wallets.module';
import { PreferencesModule } from './preferences/preferences.module';

@Module({
  imports: [
    SentryModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('DB_HOST'),
        port: Number(configService.get<string>('DB_PORT') ?? 5432),
        username: configService.get<string>('DB_USERNAME'),
        password: configService.get<string>('DB_PASSWORD'),
        database: configService.get<string>('DB_NAME'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: process.env.NODE_ENV === 'development',
        logging: process.env.NODE_ENV === 'development',
      }),
    }),
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot(),
    ThrottlerModule.forRoot([
      {
        ttl: 60000,
        limit: 10,
      },
    ]),
    AuthModule,
    UsersModule,
    OtpsModule,
    AddressesModule,
    AdminTokensModule,
    NotificationsModule,
    CategoryModule,
    ProduceModule,
    CartModule,
    OrdersModule,
    PaymentsModule,
    TransactionsModule,
    CronsModule,
    WalletsModule,
    PreferencesModule,
    WeeklyPriceModule,
  ],
  controllers: [],
  providers: [
    JwtService,
    {
      provide: APP_FILTER,
      useClass: SentryGlobalFilter,
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggerInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: CaseInterceptor,
    },
    Logger,
  ],
})
// export class AppModule {}
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // consumer.apply(LoggerMiddleware).forRoutes('*');
    // .apply(AuthMiddleware)
    // .forRoutes('/users/', '/products/', '/admin/');
  }
}
