import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  // CreateAdminDto,
  // CreateAgentDto,
  CreateBuyerDto,
  CreateFarmerDto,
  CreateUserDto,
  // CreateFarmerDto,
} from 'src/users/dto/create-user.dto';
import {
  ForgotPasswordEmailDto,
  ForgotPasswordPhoneDto,
  LoginDto,
  ResetPasswordDto,
  ResetPasswordEmailDto,
  Send2FACodeDto,
  VerifyAdminLoginDto,
  VerifyEmailDto,
  VerifyLoginDto,
  VerifyPhoneDto,
} from './dto/dtos';
import { VerifyOtpDto } from 'src/otps/dto/update-otp.dto';
import { Response as ExpressResponse } from 'express';
import { CurrentUser } from 'src/shared/decorators/currentUser.decorator';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { EwekoAuthGuard } from './jwt.guard';

@Controller('auth')
@ApiTags('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  @ApiOperation({
    summary: 'Create a new user account',
    description: 'Creates a new user account for any user type (buyer, farmer, agent)',
  })
  @ApiBody({
    type: CreateUserDto,
    description: 'User account creation payload',
  })
  @ApiResponse({
    status: 201,
    description: 'User account created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data provided',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Email or phone number already exists',
  })
  signup(@Body() createUserDto: CreateUserDto) {
    return this.authService.signup(createUserDto);
  }

  @Post('signup/buyers')
  @ApiOperation({
    summary: 'Create a new buyer account',
    description: 'Creates a new buyer in the system with the provided details',
  })
  @ApiBody({
    type: CreateBuyerDto,
    description: 'Buyer account creation payload',
  })
  @ApiResponse({
    status: 201,
    description: 'Buyer created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data provided',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Email or phone number already exists',
  })
  signupBuyer(@Body() createBuyerDto: CreateBuyerDto) {
    return this.authService.signupBuyer(createBuyerDto);
  }

  @Post('signup/farmers')
  @ApiOperation({
    summary: 'Create a new farmer account',
    description: 'Creates a new farmer in the system with the provided details',
  })
  @ApiBody({
    type: CreateFarmerDto,
    description: 'Farmer account creation payload',
  })
  @ApiResponse({
    status: 201,
    description: 'Farmer created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data provided',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Email or phone number already exists',
  })
  signupFarmer(@Body() createFarmerDto: CreateFarmerDto) {
    return this.authService.signupFarmer(createFarmerDto);
  }

  // @Post('signup/agents')
  // @ApiOperation({
  //   summary: 'Create a new agent account',
  //   description: 'Creates a new agent in the system with the provided details',
  // })
  // @ApiBody({
  //   type: CreateAgentDto,
  //   description: 'Agent account creation payload',
  // })
  // @ApiResponse({
  //   status: 201,
  //   description: 'Agent created successfully',
  // })
  // @ApiResponse({
  //   status: 400,
  //   description: 'Bad Request - Invalid input data provided',
  // })
  // @ApiResponse({
  //   status: 409,
  //   description: 'Conflict - Email or phone number already exists',
  // })
  // signupAgent(@Body() createAgentDto: CreateAgentDto) {
  //   return this.authService.signupAgent(createAgentDto);
  // }

  // @Post('signup/admins')
  // @ApiOperation({
  //   summary: 'Create a new admin account',
  //   description: 'Creates a new admin in the system with the provided details',
  // })
  // @ApiBody({
  //   type: CreateAdminDto,
  //   description: 'Admin account creation payload',
  // })
  // @ApiResponse({
  //   status: 201,
  //   description: 'Admin created successfully',
  // })
  // @ApiResponse({
  //   status: 400,
  //   description: 'Bad Request - Invalid input data provided',
  // })
  // @ApiResponse({
  //   status: 409,
  //   description: 'Conflict - Email or phone number already exists',
  // })
  // signupAdmin(@Body() createAdminDto: CreateAdminDto) {
  //   return this.authService.signupAdmin(createAdminDto);
  // }

  @Post('verify/email')
  @ApiOperation({
    summary: 'Send verification email',
    description: 'Sends a verification email to the provided email address',
  })
  @ApiBody({
    type: VerifyEmailDto,
    description: 'Email verification payload',
  })
  @ApiResponse({
    status: 200,
    description: 'Verification email sent successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid email address provided',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - User with the provided email does not exist',
  })
  sendVerifyEmail(@Body() verifyEmailDto: VerifyEmailDto) {
    return this.authService.sendVerifyEmail(verifyEmailDto.email);
  }

  @Post('verify/phone')
  @ApiOperation({
    summary: 'Send verification SMS',
    description: 'Sends a verification SMS to the provided phone number',
  })
  @ApiBody({
    type: VerifyPhoneDto,
    description: 'Phone verification payload',
  })
  @ApiResponse({
    status: 200,
    description: 'Verification SMS sent successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid phone number provided',
  })
  @ApiResponse({
    status: 404,
    description:
      'Not Found - User with the provided phone number does not exist',
  })
  sendVerifyPhone(@Body() verifyPhoneDto: VerifyPhoneDto) {
    return this.authService.sendVerifyPhone(verifyPhoneDto.primaryPhone);
  }

  @Post('verify/otp/:code')
  @ApiOperation({
    summary: 'Verify OTP',
    description: 'Verify the OTP sent to the user',
  })
  @ApiParam({
    name: 'code',
    description: 'Code of the OTP to retrieve',
  })
  @ApiResponse({
    status: 200,
    description: 'OTP Verification successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid OTP provided',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - User with the provided OTP does not exist',
  })
  verifyOtp(@Param('code') code: string) {
    return this.authService.verifyOtp(code);
  }

  @Post('forgot-password/email')
  @ApiOperation({
    summary: 'Send password reset email',
    description: 'Sends a password reset email to the provided email address',
  })
  @ApiBody({
    type: ForgotPasswordEmailDto,
    description: 'Password reset payload',
  })
  @ApiResponse({
    status: 200,
    description: 'Password reset email sent successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid email address provided',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - User with the provided email does not exist',
  })
  sendPasswordResetEmail(@Body() forgotPasswordDto: ForgotPasswordEmailDto) {
    return this.authService.sendPasswordResetEmail(forgotPasswordDto.email);
  }

  @Post('forgot-password/phone')
  @ApiOperation({
    summary: 'Send password reset SMS',
    description: 'Sends a password reset SMS to the provided phone number',
  })
  @ApiBody({
    type: ForgotPasswordPhoneDto,
    description: 'Password reset SMS payload',
  })
  @ApiResponse({
    status: 200,
    description: 'Password reset SMS sent successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid phone number provided',
  })
  @ApiResponse({
    status: 404,
    description:
      'Not Found - User with the provided phone number does not exist',
  })
  sendPasswordResetPhone(@Body() verifyPhoneDto: ForgotPasswordPhoneDto) {
    return this.authService.sendPasswordResetPhone(verifyPhoneDto.primaryPhone);
  }

  @Post('reset-password/email')
  @ApiOperation({
    summary: 'Reset password using email',
    description:
      'Resets the password using the provided token and new password',
  })
  @ApiQuery({
    name: 'token',
    description: 'JWT token for password reset',
    required: true,
  })
  @ApiBody({
    type: ResetPasswordEmailDto,
    description: 'New password and confirm new password',
  })
  @ApiResponse({
    status: 200,
    description: 'Password has been reset successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data or passwords do not match',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or expired token',
  })
  async resetPasswordEmail(
    @Query('token') token: string,
    @Body() resetPasswordDto: ResetPasswordEmailDto,
  ) {
    return this.authService.resetPasswordEmail(token, resetPasswordDto);
  }

  @Post('reset-password/phone')
  @ApiOperation({
    summary: 'Reset password using SMS',
    description: 'Resets the password using the provided OTP and new password',
  })
  @ApiBody({
    type: ResetPasswordDto,
    description: 'New password and confirm new password',
  })
  @ApiResponse({
    status: 200,
    description: 'Password has been reset successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data or passwords do not match',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or expired OTP',
  })
  async resetPasswordPhone(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPasswordPhone(resetPasswordDto);
  }

  @UseGuards(EwekoAuthGuard)
  @Post('change-password')
  @ApiOperation({
    summary: 'Change password',
    description: 'Changes the password using userId and new password',
  })
  @ApiBody({
    type: ResetPasswordEmailDto,
    description: 'New password and confirm new password',
  })
  @ApiResponse({
    status: 200,
    description: 'Password changed successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data or passwords do not match',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid userId',
  })
  async changePassword(
    @CurrentUser('id') userId: string,
    @Body() resetPasswordDto: ResetPasswordEmailDto,
  ) {
    return this.authService.changePassword(userId, resetPasswordDto);
  }

  @Post('login')
  @ApiOperation({
    summary: 'User login',
    description: 'Logs in a user with the provided credentials.',
  })
  @ApiBody({
    type: LoginDto,
    description: 'The login credentials (email and password)',
  })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid credentials.',
  })
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @Post('send-2fa-code')
  @ApiOperation({
    summary: 'Send 2FA Code',
    description: 'Send a two-factor authentication code to a user.',
  })
  @ApiBody({
    description: 'The payload required to send a 2FA code.',
    type: Send2FACodeDto,
  })
  @ApiResponse({
    status: 200,
    description: '2FA code sent successfully.',
  })
  @ApiResponse({
    status: 404,
    description:
      'Not Found - No user matches the provided email or phone number.',
  })
  async send2FACode(@Body() send2FACodeDto: Send2FACodeDto) {
    return this.authService.send2FACode(send2FACodeDto.emailOrPhone);
  }

  @Post('verify/login')
  @ApiOperation({
    summary: '2FA Verification',
    description: 'Verify user login via 2FA.',
  })
  @ApiBody({
    type: VerifyLoginDto,
    description: 'The verification credentials (loggedIn userId and OTP)',
  })
  @ApiResponse({
    status: 200,
    description: 'Login verified successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid OTP.',
  })
  async verifyLogin(@Body() verifyLoginDto: VerifyLoginDto) {
    return this.authService.verifyLogin(verifyLoginDto);
  }

  // @Post('verify/login/admin')
  // @ApiOperation({
  //   summary: 'Admin 2FA Verification',
  //   description: 'Verify admin login via 2FA.',
  // })
  // @ApiBody({
  //   type: VerifyAdminLoginDto,
  //   description: 'The verification credentials (loggedIn adminToken and OTP)',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Login verified successfully',
  // })
  // @ApiResponse({
  //   status: 400,
  //   description: 'Bad Request - Invalid OTP.',
  // })
  // async verifyAdminLogin(
  //   @Body() verifyAdminLoginDto: VerifyAdminLoginDto,
  //   @Res({ passthrough: true }) res: ExpressResponse,
  // ) {
  //   return this.authService.verifyAdminLogin(verifyAdminLoginDto, res);
  // }
}
