import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsUUID,
  IsNotEmpty,
  IsPhoneNumber,
  IsString,
  Length,
  Matches,
} from 'class-validator';

export class VerifyEmailDto {
  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
    default: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  email: string;
}

export class VerifyPhoneDto {
  @ApiProperty({
    description: 'Phone number of the user',
    example: '+2348034023726',
    default: '+2348034023726',
    required: true,
  })
  @IsString()
  @IsPhoneNumber()
  primaryPhone: string;
}

export class ForgotPasswordEmailDto {
  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
    default: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  email: string;
}

export class ForgotPasswordPhoneDto {
  @ApiProperty({
    description: 'Phone number of the user',
    example: '+2348034023726',
    default: '+2348034023726',
    required: true,
  })
  @IsString()
  @IsPhoneNumber()
  primaryPhone: string;
}
 
export class ResetPasswordDto {
  @ApiProperty({
    description: 'OTP (6-digit number)',
    example: '123456',
    default: '123456',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{6}$/, {
    message: 'OTP must be a 6-digit number',
  })
  otp: string;
  @ApiProperty({
    description: 'Password of the user',
    example: 'SecurePassword123!',
    default: 'SecurePassword123!',
    required: true,
  })
  @IsString()
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&^#_=+?{}|:;,.<>~`-])[A-Za-z\d@$!%*?&^#_=+?{}|:;,.<>~`-]{6,}$/,
    {
      message:
        'Password must contain at least 1 uppercase, 1 lowercase, 1 number, and 1 special character from the following symbols: @$!%*?&^#_=+?{}|:;,.<>~`-',
    },
  )
  newPassword: string;

  @ApiProperty({
    description: 'Password of the user',
    example: 'SecurePassword123!',
    default: 'SecurePassword123!',
    required: true,
  })
  @IsString()
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&^#_=+?{}|:;,.<>~`-])[A-Za-z\d@$!%*?&^#_=+?{}|:;,.<>~`-]{6,}$/,
    {
      message:
        'Password must contain at least 1 uppercase, 1 lowercase, 1 number, and 1 special character from the following symbols: @$!%*?&^#_=+?{}|:;,.<>~`-',
    },
  )
  confirmNewPassword: string;
}

export class ResetPasswordEmailDto {
  @ApiProperty({
    description: 'Password of the user',
    example: 'SecurePassword123!',
    default: 'SecurePassword123!',
    required: true,
  })
  @IsString()
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&^#_=+?{}|:;,.<>~`-])[A-Za-z\d@$!%*?&^#_=+?{}|:;,.<>~`-]{6,}$/,
    {
      message:
        'Password must contain at least 1 uppercase, 1 lowercase, 1 number, and 1 special character from the following symbols: @$!%*?&^#_=+?{}|:;,.<>~`-',
    },
  )
  newPassword: string;

  @ApiProperty({
    description: 'Password of the user',
    example: 'SecurePassword123!',
    default: 'SecurePassword123!',
    required: true,
  })
  @IsString()
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&^#_=+?{}|:;,.<>~`-])[A-Za-z\d@$!%*?&^#_=+?{}|:;,.<>~`-]{6,}$/,
    {
      message:
        'Password must contain at least 1 uppercase, 1 lowercase, 1 number, and 1 special character from the following symbols: @$!%*?&^#_=+?{}|:;,.<>~`-',
    },
  )
  confirmNewPassword: string;
}
export class LoginDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
    default: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Password for the user account.',
    type: String,
    minLength: 6,
    required: true,
  })
  @IsString()
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&^#_=+?{}|:;,.<>~`-])[A-Za-z\d@$!%*?&^#_=+?{}|:;,.<>~`-]{6,}$/,
    {
      message:
        'Password must contain at least 1 uppercase, 1 lowercase, 1 number, and 1 special character from the following symbols: @$!%*?&^#_=+?{}|:;,.<>~`-',
    },
  )
  password: string;
}

export class Send2FACodeDto {
  @ApiProperty({
    description:
      'The email or phone number of the user to receive the 2FA code.',
    example: '<EMAIL> or +**********',
    required: true,
  })
  @IsString()
  @IsNotEmpty({ message: 'Email or phone number must not be empty' })
  emailOrPhone: string;
}

export class VerifyLoginDto {
  @ApiProperty({
    description: 'The unique identifier for the user.',
    example: 'user123',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'OTP (6-digit number)',
    example: '123456',
    default: '123456',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{6}$/, {
    message: 'OTP must be a 6-digit number',
  })
  otp: string;
}

export class VerifyAdminLoginDto {
  @ApiProperty({
    description: 'Old admin token to be renewed.',
    example: '3WKABCDE1234567',
    required: true,
  })
  @IsString()
  @Matches(/^3WK/, {
    message: 'Token must start with the prefix "3WK".',
  })
  @Length(16, 16, {
    message: 'Token must be exactly 16 characters long.',
  })
  adminToken: string;

  @ApiProperty({
    description: 'OTP (6-digit number)',
    example: '123456',
    default: '123456',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{6}$/, {
    message: 'OTP must be a 6-digit number',
  })
  otp: string;
}
