import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AdminRole, UserType } from 'src/shared/enums';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(readonly configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  async validate(payload: {
    id: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    email: string;
    userType: UserType;
    farmName?: string;
    adminRole?: AdminRole;
    profilePicture?: string;
  }) {
    return payload;
  }
}
