import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsInt, Min, <PERSON>N<PERSON><PERSON>, IsString } from 'class-validator';

export class AddToCartDto {
  @ApiProperty({
    description: 'ID of the produce to add',
    example: '660e1436a1bcf44c9159e015',
  })
  @IsUUID()
  produceId: string;

  @ApiProperty({
    description: 'Quantity of the produce to add',
    example: 5,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({
    description: 'Unit price of the produce',
    example: 1200.5,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  price: number;
}


export class CartItemResponseDto {
  @ApiProperty({
    description: 'ID of the produce',
    example: '507f1f77bcf86cd799439011',
  })
  @IsUUID()
  produce: string;

  @ApiProperty({
    description: 'Quantity of the produce in cart',
    example: 2,
    minimum: 1,
  })
  quantity: number;

  @ApiProperty({
    description: 'Total price for this item (quantity * price)',
    example: 19.98,
  })
  totalPrice: number;
}

export class CartResponseDto {
  @ApiProperty({
    description: 'Cart ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'ID of the user who owns the cart',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Array of items in the cart',
    type: [CartItemResponseDto],
  })
  items: CartItemResponseDto[];

  @ApiProperty({
    description: 'Total cost of all items in cart',
    example: 59.97,
  })
  totalCost: number;

  @ApiProperty({
    description: 'Cart creation timestamp',
    example: '2024-02-13T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Cart last update timestamp',
    example: '2024-02-13T10:30:00.000Z',
  })
  updatedAt: Date;
}
