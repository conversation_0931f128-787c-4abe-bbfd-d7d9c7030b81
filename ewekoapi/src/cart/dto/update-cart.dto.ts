import { ApiProperty, PartialType } from '@nestjs/swagger';
import { AddToCartDto } from './create-cart.dto';
import { IsInt, IsMongoId, IsNumber, Min } from 'class-validator';

export class UpdateCartDto extends PartialType(AddToCartDto) {}

export class UpdateCartItemDto {
  @ApiProperty({
    description: 'ID of the produce in the cart',
    example: '660e1436a1bcf44c9159e015',
  })
  @IsMongoId()
  produceId: string;
  
  @ApiProperty({
    description: 'New quantity of the produce',
    example: 2,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({
    description: 'Current price per unit of the produce',
    example: 9.99,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  price: number;
}

export class UpdateItemQuantityDto {
  @ApiProperty({
    description: 'ID of the produce in the cart',
    example: '660e1436a1bcf44c9159e015',
  })
  @IsMongoId()
  produceId: string;

  @ApiProperty({
    description: 'Updated quantity of the item',
    example: 3,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  quantity: number;
}