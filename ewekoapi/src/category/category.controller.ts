import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { CategoryService } from './category.service';
import {
  CategoryResponseDto,
  CreateCategoryDto,
} from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { EwekoAuthGuard, RolesGuard } from 'src/auth/jwt.guard';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { UserType } from 'src/shared/enums';

@ApiTags('categories')
@Controller('category')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new category' })
  @ApiResponse({
    status: 201,
    description: 'The category has been successfully created.',
    type: CategoryResponseDto,
  })
  // @Roles(UserType.ADMIN, UserType.FARMER)
  create(@Body() createCategoryDto: CreateCategoryDto) {
    return this.categoryService.create(createCategoryDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all categories' })
  @ApiResponse({
    status: 200,
    description: 'Returns all categories',
    type: [CategoryResponseDto],
  })
  // @Roles(UserType.ADMIN, UserType.BUYER, UserType.FARMER)
  findAll() {
    return this.categoryService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a category by ID' })
  @ApiParam({ name: 'id', type: String, description: 'The category ID' })
  @ApiResponse({
    status: 200,
    description: 'The category has been found',
    type: CategoryResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Category not found',
  })
  // @Roles(UserType.ADMIN, UserType.BUYER, UserType.FARMER)
  findOne(@Param('id') id: string) {
    return this.categoryService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an existing category' })
  @ApiParam({ name: 'id', type: String, description: 'The category ID' })
  @ApiResponse({
    status: 200,
    description: 'The category has been successfully updated',
    type: CategoryResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Category not found',
  })
  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN, UserType.FARMER)
  update(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ) {
    return this.categoryService.update(id, updateCategoryDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a category by ID' })
  @ApiParam({ name: 'id', type: String, description: 'The category ID' })
  @ApiResponse({
    status: 200,
    description: 'The category has been successfully removed',
  })
  @ApiResponse({
    status: 404,
    description: 'Category not found',
  })
  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN)
  remove(@Param('id') id: string): Promise<void> {
    return this.categoryService.remove(id);
  }
}
