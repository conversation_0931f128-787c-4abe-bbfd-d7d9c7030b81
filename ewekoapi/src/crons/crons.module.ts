import { Module } from '@nestjs/common';
import { CronsService } from './crons.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { Otp } from '../otp/entities/otp.entity';
import { Notification } from '../notifications/entities/notification.entity';
import { Buyer } from '../users/entities/buyers/buyer.entity';
import { Farmer } from '../users/entities/farmers/farmer.entity';
import { WeeklyPriceModule } from '../weekly-price/weekly-price.module';

@Module({
  imports: [
    WeeklyPriceModule,
    TypeOrmModule.forFeature([User, Otp, Notification, Buyer, Farmer]),
  ],
  controllers: [],
  providers: [CronsService],
  exports: [CronsService, TypeOrmModule],
})
export class CronsModule {}
