import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Otp } from 'src/otp/entities/otp.entity';
import { Notification } from 'src/notifications/entities/notification.entity';
import { Buyer } from 'src/users/entities/buyers/buyer.entity';
import { Farmer } from 'src/users/entities/farmers/farmer.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import { isBefore, subDays } from 'date-fns';
import { UserType } from 'src/shared/enums';
import { WeeklyPriceService } from '../weekly-price/weekly-price.service';

@Injectable()
export class CronsService {
  private readonly logger = new Logger(CronsService.name);

  constructor(
    @InjectRepository(Otp)
    private readonly otpRepository: Repository<Otp>,
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    @InjectRepository(Buyer)
    private readonly buyerRepository: Repository<Buyer>,
    @InjectRepository(Farmer)
    private readonly farmerRepository: Repository<Farmer>,
    private readonly weeklyPriceService: WeeklyPriceService,
  ) {}

  // Weekly price calculation job - runs every Sunday at midnight currently set to run every 30 minutes
  @Cron(CronExpression.EVERY_30_MINUTES)
  async handleWeeklyPriceCalculation() {
    console.log('------------------------');
    console.log('Starting weekly price calculation job');
    
    try {
      await this.weeklyPriceService.calculateWeeklyAverages();
      console.log('Successfully completed weekly price calculation');
      console.log('------------------------');
    } catch (error) {
      console.error('Error in weekly price calculation:', error);
      console.log('------------------------');
      throw error;
    }
  }

  // Cron job to delete expired OTPs
  @Cron(CronExpression.EVERY_5_MINUTES)
  async deleteExpiredOtps() {
    const currentDate = new Date();

    const expiredOtps = await this.otpRepository.find();
    const otpsToDelete = expiredOtps.filter((otp) =>
      isBefore(new Date(otp.expires_at), currentDate),
    );

    if (otpsToDelete.length > 0) {
      await this.otpRepository.remove(otpsToDelete);
      console.log('------------------------');
      console.log(`Deleted ${otpsToDelete.length} expired OTPs`);
      console.log('------------------------');
    }
  }

  // Cron job to delete notifications older than 14 days
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async deleteOldNotifications() {
    const thirtyDaysAgo = subDays(new Date(), 14);

    const oldNotifications = await this.notificationRepository.find({
      where: {
        created_at: { $lt: thirtyDaysAgo } as any,
      },
    });

    // Delete old notifications from the Notification collection
    const result = await this.notificationRepository.delete({
      created_at: { $lt: thirtyDaysAgo } as any,
    });

    if (result.affected && result.affected > 0) {
      console.log('--------------------------');
      console.log(`Deleted ${result.affected} old notifications`);
      console.log('--------------------------');

      // Note: In TypeORM/PostgreSQL, we don't need to manually update user notification arrays
      // as this would be handled by proper foreign key relationships
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async deleteReadNotifications() {
    try {
      // Find and delete read notifications
      const result = await this.notificationRepository.delete({
        is_read: true,
      });

      if (result.affected && result.affected > 0) {
        console.log('------------------------');
        console.log(`Deleted ${result.affected} read notifications`);
        console.log('------------------------');

        // Note: In TypeORM/PostgreSQL, we don't need to manually update user notification arrays
        // as this would be handled by proper foreign key relationships
      }
    } catch (error) {
      console.error('Error deleting read notifications:', error);
    }
  }
}
