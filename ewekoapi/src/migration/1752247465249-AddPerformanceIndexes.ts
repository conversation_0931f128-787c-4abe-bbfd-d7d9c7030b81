import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPerformanceIndexes1752247465249 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add indexes for frequently queried fields to improve performance

        // Users table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_users_email" ON "users" ("email")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_users_primary_phone" ON "users" ("primary_phone")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_users_user_type" ON "users" ("user_type")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_users_username" ON "users" ("username")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_users_is_active" ON "users" ("is_active")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_users_verified" ON "users" ("verified")`);

        // Produces table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_produces_farmer_id" ON "produces" ("farmer_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_produces_category_id" ON "produces" ("category_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_produces_name" ON "produces" ("name")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_produces_price" ON "produces" ("price")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_produces_stock" ON "produces" ("stock")`);

        // Orders table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_orders_buyer_id" ON "orders" ("buyer_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_orders_farmer_id" ON "orders" ("farmer_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_orders_status" ON "orders" ("status")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_orders_payment_status" ON "orders" ("payment_status")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_orders_created_at" ON "orders" ("created_at")`);

        // Notifications table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_notifications_user_id" ON "notifications" ("user_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_notifications_is_read" ON "notifications" ("is_read")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_notifications_user_id_is_read" ON "notifications" ("user_id", "is_read")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_notifications_created_at" ON "notifications" ("created_at")`);

        // Transactions table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_transactions_user_id" ON "transactions" ("user_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_transactions_buyer_id" ON "transactions" ("buyer_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_transactions_farmer_id" ON "transactions" ("farmer_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_transactions_status" ON "transactions" ("status")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_transactions_payment_method" ON "transactions" ("payment_method")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_transactions_created_at" ON "transactions" ("created_at")`);

        // Addresses table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_addresses_user_id" ON "addresses" ("user_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_addresses_is_default" ON "addresses" ("is_default")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_addresses_state" ON "addresses" ("state")`);

        // Wallets table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_wallets_farmer_id" ON "wallets" ("farmer_id")`);

        // Cart table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_carts_user_id" ON "carts" ("user_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_carts_active" ON "carts" ("active")`);

        // Cart items table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_cart_items_cart_id" ON "cart_items" ("cart_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_cart_items_produce_id" ON "cart_items" ("produce_id")`);

        // Order items table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_order_items_order_id" ON "order_items" ("order_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_order_items_produce_id" ON "order_items" ("produce_id")`);

        // Preferences table indexes
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_preferences_user_id" ON "preferences" ("user_id")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop all performance indexes

        // Users table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_users_email"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_users_primary_phone"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_users_user_type"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_users_username"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_users_is_active"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_users_verified"`);

        // Produces table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_produces_farmer_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_produces_category_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_produces_name"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_produces_price"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_produces_stock"`);

        // Orders table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_orders_buyer_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_orders_farmer_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_orders_status"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_orders_payment_status"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_orders_created_at"`);

        // Notifications table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_notifications_user_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_notifications_is_read"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_notifications_user_id_is_read"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_notifications_created_at"`);

        // Transactions table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_transactions_user_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_transactions_buyer_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_transactions_farmer_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_transactions_status"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_transactions_payment_method"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_transactions_created_at"`);

        // Addresses table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_addresses_user_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_addresses_is_default"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_addresses_state"`);

        // Wallets table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_wallets_farmer_id"`);

        // Cart table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_carts_user_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_carts_active"`);

        // Cart items table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_cart_items_cart_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_cart_items_produce_id"`);

        // Order items table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_order_items_order_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_order_items_produce_id"`);

        // Preferences table indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_preferences_user_id"`);
    }

}
