import {
  <PERSON><PERSON><PERSON>,
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsArray,
  IsBoolean,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import {
  NotificationDestination,
  NotificationTrigger,
  UserType,
} from 'src/shared/enums';

export class CreateNotificationDto {
  @ApiProperty({
    description: 'Type of user receiving the notification',
    example: UserType.BUYER,
    enum: UserType,
  })
  @IsEnum(UserType)
  @IsNotEmpty()
  userType: UserType;

  @ApiProperty({
    description: 'ID of the user receiving the notification',
    example: '64f81b5e6f65d8a3c4b93c21',
  })
  @IsMongoId()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Subject of the notification',
    example: 'Welcome to Our Platform!',
  })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty({
    description: 'Message content of the notification',
    example: 'Thank you for registering. Start exploring our services today!',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Trigger event for the notification',
    example: 'USER_REGISTRATION',
    enum: NotificationTrigger,
    default: NotificationTrigger.USER_REGISTRATION,
  })
  @IsEnum(NotificationTrigger)
  @IsOptional()
  trigger?: NotificationTrigger;

  @ApiProperty({
    description: 'Delivery destinations for the notification',
    example: ['IN_APP', 'EMAIL'],
    enum: NotificationDestination,
    isArray: true,
    default: [NotificationDestination.EMAIL],
  })
  @IsArray()
  @IsEnum(NotificationDestination, { each: true })
  @IsOptional()
  destinations?: NotificationDestination[];
}

