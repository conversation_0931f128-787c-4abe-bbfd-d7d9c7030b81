import {
  Is<PERSON>rray,
  IsEnum,
  Is<PERSON>umber,
  IsString,
  IsNotEmpty,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { PaymentMethod, PaymentStatus } from 'src/shared/enums';

export class OrderItemDto {
  @ApiProperty({
    description: 'The ID of the produce item being ordered.',
    example: '60b9edfc7c2e7b3c8c8a3d58',
  })
  @IsString()
  @IsNotEmpty()
  produce: string; // ID of the produce item

  @ApiProperty({
    description: 'Quantity of the produce item being ordered.',
    example: 2,
  })
  @IsNumber()
  @IsNotEmpty()
  quantity: number;

  @ApiProperty({
    description: 'Total price of the item (quantity * unit price).',
    example: 500,
  })
  @IsNumber()
  @IsNotEmpty()
  total_price: number;
}

export class CreateOrderDto {
  @ApiProperty({
    description: 'The ID of the user placing the order.',
    example: '60b9edfc7c2e7b3c8c8a3d56',
  })
  @IsString()
  @IsNotEmpty()
  user_id: string;

  @ApiProperty({
    description: 'List of items included in the order.',
    type: [OrderItemDto],
    example: [
      {
        produce: '60b9edfc7c2e7b3c8c8a3d58',
        quantity: 2,
        total_price: 500,
      },
    ],
  })
  @IsArray()
  @Type(() => OrderItemDto)
  items: OrderItemDto[];

  @ApiProperty({
    description: 'Total cost of the items in the order.',
    example: 1000,
  })
  @IsNumber()
  @IsNotEmpty()
  total_cost: number;

  @ApiProperty({
    description: 'Shipping fee for the order.',
    example: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  shipping_fee: number;

  @ApiProperty({
    description: 'Payment method used for the order.',
    example: 'credit card',
  })
  @IsString()
  @IsNotEmpty()
  payment_method: string;

  @ApiProperty({
    description: 'The shipping address for the order.',
    example: '123 Main St, Anytown, USA',
  })
  @IsString()
  @IsNotEmpty()
  shipping_address: string;

  @ApiProperty({
    description: 'Final total cost including shipping fee.',
    example: 1100,
  })
  @IsNumber()
  @IsNotEmpty()
  final_total_cost: number;
}

export class OrderResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the order',
    example: '65a4c0d2f2a9b12a3c456789',
  })
  id: string;

  @ApiProperty({
    description: 'User ID who placed the order',
    example: '65a4c0d2f2a9b12a3c456111',
  })
  user_id: string;

  @ApiProperty({
    description: 'List of items in the order',
    type: [OrderItemDto],
  })
  items: OrderItemDto[];

  @ApiProperty({
    description: 'Total cost of the order excluding shipping',
    example: 10000,
  })
  total_cost: number;

  @ApiProperty({
    description: 'Shipping fee for the order',
    example: 500,
  })
  shipping_fee: number;

  @ApiProperty({
    description: 'Final total cost including shipping fee',
    example: 10500,
  })
  final_total_cost: number;

  @ApiProperty({
    description: 'Payment method used',
    example: PaymentMethod.CENTIIVPAY,
    enum: PaymentMethod,
  })
  payment_method: PaymentMethod;

  @ApiProperty({
    description: 'Current payment status of the order',
    example: PaymentStatus.PENDING,
    enum: PaymentStatus,
  })
  payment_status: PaymentStatus;

  @ApiProperty({
    description: 'Current status of the order',
    example: 'Processing',
  })
  order_status: string;

  @ApiProperty({
    description: 'Timestamp when the order was created',
    example: '2025-01-30T12:45:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Timestamp when the order was last updated',
    example: '2025-01-30T12:50:00.000Z',
  })
  updated_at: Date;
}
