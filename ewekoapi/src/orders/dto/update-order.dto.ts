import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsDateString } from 'class-validator';
import { OrderStatus, PaymentStatus } from 'src/shared/enums';

export class UpdateOrderDto {
  @ApiProperty({
    description: 'Updated status of the order',
    example: OrderStatus.SHIPPED,
    enum: OrderStatus,
    required: false,
  })
  @IsEnum(OrderStatus)
  @IsOptional()
  order_status?: OrderStatus;

  @ApiProperty({
    description: 'Updated payment status of the order',
    example: PaymentStatus.COMPLETED,
    enum: PaymentStatus,
    required: false,
  })
  @IsEnum(PaymentStatus)
  @IsOptional()
  payment_status?: PaymentStatus;

  @ApiProperty({
    description: 'Date when the order was shipped',
    example: '2025-02-05T15:00:00.000Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  shipped_date?: Date;
}
