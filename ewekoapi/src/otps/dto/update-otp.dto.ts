import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString, Matches } from 'class-validator';

export class UpdateOtpDto {
  @ApiProperty({
    description: 'Indicates whether the OTP has been used',
    example: true,
  })
  @IsBoolean()
  isUsed: boolean;
}

export class VerifyOtpDto {
  @ApiProperty({
    description: 'The OTP (6-digit number) to be verified',
    example: '123456',
    default: '123456',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{6}$/, {
    message: 'OTP must be a 6-digit number',
  })
  code: string;
}
