import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { OtpsService } from './otps.service';
import { CreateOtpDto } from './dto/create-otp.dto';
import { UpdateOtpDto } from './dto/update-otp.dto';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

@Controller('otps')
@ApiTags('otps')
export class OtpsController {
  constructor(private readonly otpsService: OtpsService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new OTP',
    description: 'Creates a new OTP with the provided details',
  })
  @ApiBody({
    type: CreateOtpDto,
    description: 'OTP creation payload',
  })
  @ApiResponse({
    status: 201,
    description: 'OTP created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data provided',
  })
  create(@Body() createOtpDto: CreateOtpDto) {
    return this.otpsService.create(createOtpDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all OTPs',
    description: 'Returns a list of all OTPs',
  })
  @ApiResponse({
    status: 200,
    description: 'List of OTPs returned successfully',
  })
  findAll() {
    return this.otpsService.findAll();
  }

  @Get('code/:code')
  @ApiOperation({
    summary: 'Get OTP by code',
    description: 'Returns an OTP by its code',
  })
  @ApiParam({
    name: 'code',
    description: 'Code of the OTP to retrieve',
  })
  @ApiResponse({
    status: 200,
    description: 'OTP returned successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - OTP with the provided code does not exist',
  })
  findByCode(@Param('code') code: string) {
    return this.otpsService.findByCode(code);
  }
 
  @Get(':id')
  @ApiOperation({
    summary: 'Get OTP by ID',
    description: 'Returns an OTP by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the OTP to retrieve',
  })
  @ApiResponse({
    status: 200,
    description: 'OTP returned successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - OTP with the provided ID does not exist',
  })
  findById(@Param('id') id: string) {
    return this.otpsService.findById(id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update OTP by ID',
    description: 'Updates an OTP with the provided details',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the OTP to update',
  })
  @ApiBody({
    type: UpdateOtpDto,
    description: 'OTP update payload',
  })
  @ApiResponse({
    status: 200,
    description: 'OTP updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - OTP with the provided ID does not exist',
  })
  update(@Param('id') id: string, @Body() updateOtpDto: UpdateOtpDto) {
    return this.otpsService.update(id, updateOtpDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete OTP by ID',
    description: 'Deletes an OTP by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the OTP to delete',
  })
  @ApiResponse({
    status: 200,
    description: 'OTP deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - OTP with the provided ID does not exist',
  })
  remove(@Param('id') id: string) {
    return this.otpsService.remove(id);
  }
}
