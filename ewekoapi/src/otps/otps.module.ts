import { Module } from '@nestjs/common';
import { OtpsService } from './otps.service';
import { OtpsController } from './otps.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Otp } from '../otp/entities/otp.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Otp])],
  controllers: [OtpsController],
  providers: [OtpsService],
  exports: [OtpsService, TypeOrmModule],
})
export class OtpsModule {}
