import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateOtpDto } from './dto/create-otp.dto';
import { UpdateOtpDto } from './dto/update-otp.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Otp } from '../otp/entities/otp.entity';
import { Repository } from 'typeorm';
import { addMinutes, isBefore, isEqual } from 'date-fns';
import { Type } from 'class-transformer';

@Injectable()
export class OtpsService {
  constructor(
    @InjectRepository(Otp)
    private readonly otpRepository: Repository<Otp>
  ) {}

  async create(createOtpDto: CreateOtpDto) {
    const otp = this.otpRepository.create({
      user_id: createOtpDto.userId,
      use_case: createOtpDto.useCase,
      code: this.generateOTP(),
      expires_at: this.getExpiry(),
    });

    const savedOtp = await this.otpRepository.save(otp);

    return {
      code: savedOtp.code,
      useCase: savedOtp.use_case,
    };
  }

  async findAll() {
    return await this.otpRepository.find();
  }

  async findById(id: string): Promise<Otp> {
    const otp = await this.otpRepository.findOne({ where: { id } });
    if (!otp) {
      throw new NotFoundException(`OTP not found`);
    }
    return otp;
  }

  async findByCode(code: string): Promise<Otp> {
    const otp = await this.otpRepository.findOne({ where: { code } });

    if (!otp) {
      throw new NotFoundException(`OTP not found`);
    }
    return otp;
  }

  async update(id: string, updateOtpDto: UpdateOtpDto): Promise<Otp> {
    // Transform DTO field to database field
    const updateData = {
      used: updateOtpDto.isUsed
    };
    await this.otpRepository.update(id, updateData);
    const otp = await this.otpRepository.findOne({ where: { id } });

    if (!otp) {
      throw new NotFoundException(`OTP not found`);
    }
    return otp;
  }

  async remove(id: string): Promise<void> {
    const result = await this.otpRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`OTP not found`);
    }
  }

  private generateOTP = (): string => {
    const digits = '0123456789';
    let otp = '';

    for (let i = 0; i < 6; i++) {
      otp += digits[Math.floor(Math.random() * digits.length)];
    }

    return otp;
  };

  private getExpiry = (): Date => {
    const createdAt = new Date();
    return addMinutes(createdAt, 5);
  };

  private isTokenExpired(expiry: Date): boolean {
    const currentDate = new Date();
    return isBefore(expiry, currentDate) || isEqual(expiry, currentDate);
  }
}
