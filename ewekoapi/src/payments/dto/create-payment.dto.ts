import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
} from 'class-validator';

export class CreatePaymentDto {}

export class CreateDirectPaymentLinkDto {
  @ApiProperty({
    description: 'The amount to be paid',
    example: 100.5,
  })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  amount: number;

  @ApiProperty({
    description: 'Optional notes or description for the payment',
    example: 'Payment for service rendered',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class SingleDisbursementDto {
  @ApiProperty({
    description: 'Amount to be disbursed',
    example: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    description: 'Currency of the disbursement',
    example: 'NGN',
  })
  @IsString()
  @IsNotEmpty()
  currency: string;

  @ApiProperty({
    description: 'Recipient account number',
    example: '**********',
  })
  @IsString()
  @IsNotEmpty()
  recipient_account_number: string;

  @ApiProperty({
    description: 'Recipient bank code',
    example: '123456',
  })
  @IsString()
  @IsNotEmpty()
  recipient_bank_code: string;

  @ApiProperty({
    description: 'Description of the disbursement',
    example: 'Description of the disbursement',
  })
  @IsString()
  @IsNotEmpty()
  description: string;
}