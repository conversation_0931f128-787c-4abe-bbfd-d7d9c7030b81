import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Res,
} from '@nestjs/common';
import { PaymentsService } from './payments.service';
import {
  CreateDirectPaymentLinkDto,
  SingleDisbursementDto,
} from './dto/create-payment.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';

@Controller('payments')
@ApiTags('payments')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @ApiOperation({ summary: 'Create a direct payment link' })
  @ApiResponse({
    status: 201,
    description: 'Payment link created successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid request body' })
  @Post('direct-pay')
  async createDirectPaymentLink(
    @Body() createDirectPaymentLinkDto: CreateDirectPaymentLinkDto,
  ) {
    return this.paymentsService.createDirectPaymentLink(
      createDirectPaymentLinkDto,
    );
  }

  @ApiOperation({ summary: 'Get all direct payment links' })
  @ApiResponse({ status: 200, description: 'List of all payment links' })
  @Get('direct-pay')
  async findAllDirectPaymentLinks() {
    return this.paymentsService.findAllDirectPaymentLink();
  }

  @ApiOperation({ summary: 'Get a direct payment link by ID' })
  @ApiResponse({ status: 200, description: 'Payment link found' })
  @ApiResponse({ status: 404, description: 'Payment link not found' })
  @Get('direct-pay/:id')
  async findOneDirectPaymentLink(@Param('id') id: string) {
    return this.paymentsService.findOneDirectPaymentLink(id);
  }

  @ApiOperation({ summary: 'Delete a direct payment link by ID' })
  @ApiResponse({
    status: 200,
    description: 'Payment link deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Payment link not found' })
  @Delete('direct-pay/:id')
  async deleteDirectPaymentLink(@Param('id') id: string) {
    return this.paymentsService.deleteDirectPaymentLink(id);
  }

  @ApiOperation({ summary: 'Create a single disbursement transaction' })
  @ApiResponse({
    status: 201,
    description: 'Disbursement initiated successfully',
  })
  @ApiResponse({
    status: 500,
    description: 'Centiiv API Key or URL is missing',
  })
  @Post('disburse/single')
  async createSingleDisbursement(
    @Body() createDisbursementDto: SingleDisbursementDto,
  ) {
    return await this.paymentsService.createSingleDisbursement(
      createDisbursementDto,
    );
  }

  @ApiOperation({ summary: "Fetch Eweko's account details from Centiiv" })
  @ApiResponse({
    status: 200,
    description: 'Account details fetched successfully',
  })
  @ApiResponse({
    status: 500,
    description: 'Centiiv API Key or URL is missing',
  })
  @Get('disburse/account-details')
  async fetchAccountDetails() {
    return await this.paymentsService.fetchAccountDetails();
  }

  @ApiOperation({ summary: 'Check the status of a disbursement transaction' })
  @ApiResponse({
    status: 200,
    description: 'Transaction status retrieved successfully',
  })
  @ApiResponse({ status: 400, description: 'Payment link not found' })
  @ApiResponse({
    status: 500,
    description: 'Centiiv API Key or URL is missing',
  })
  @Get('status/:partialAssociateId')
  async checkDisbursementStatus(
    @Param('partialAssociateId') partialAssociateId: string,
  ) {
    return await this.paymentsService.checkDisbursementStatus(
      partialAssociateId,
    );
  }

  @ApiOperation({ summary: 'Handle Paystack webhook events' })
  @ApiResponse({ status: 200, description: 'Webhook received successfully' })
  @Post('webhooks/paystack')
  async handlePaystackWebhook(@Req() req: Request, @Res() res: Response) {
    return this.paymentsService.paystackWebhook(req, res);
  }

  @ApiOperation({ summary: 'Handle Centiiv webhook events' })
  @ApiResponse({ status: 200, description: 'Webhook received successfully' })
  @Post('webhooks/centiiv')
  async handleCentiivWebhook(@Req() req: Request, @Res() res: Response) {
    return this.paymentsService.centiivWebhook(req, res);
  }
}
