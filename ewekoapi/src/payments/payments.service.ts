import {
  BadRequestException,
  Console<PERSON>ogger,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateDirectPaymentLinkDto,
  CreatePaymentDto,
  SingleDisbursementDto,
} from './dto/create-payment.dto';
import { ConfigService } from '@nestjs/config';
import { createHmac } from 'crypto';
import { PaymentStatus, UserType } from 'src/shared/enums';
import { OrdersService } from 'src/orders/orders.service';
import { TransactionsService } from 'src/transactions/transactions.service';
import { UsersService } from 'src/users/users.service';
import { ProduceService } from 'src/produce/produce.service';
import axios from 'axios';
import { InjectRepository } from '@nestjs/typeorm';
import { Wallet } from '../wallets/entities/wallet.entity';
import { Produce } from '../produce/entities/produce.entity';
import { Transaction, TransactionStatus } from '../transactions/entities/transaction.entity';
import { Order } from '../orders/entities/order.entity';
import { User } from '../users/entities/user.entity';
import { Buyer } from '../users/entities/buyers/buyer.entity';
import { Farmer } from '../users/entities/farmers/farmer.entity';
import { Repository } from 'typeorm';
import { Request, Response } from 'express';
import crypto from 'crypto';

@Injectable()
export class PaymentsService {
  private readonly centiivKey: string;
  private readonly centiivUrl: string;
  private readonly logger = new Logger(PaymentsService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly orderService: OrdersService,
    private readonly transactionService: TransactionsService,
    private readonly userService: UsersService,
    private readonly produceService: ProduceService,
    @InjectRepository(Wallet) private walletRepository: Repository<Wallet>,
    @InjectRepository(Produce) private produceRepository: Repository<Produce>,
    // Note: Connection would be handled differently in TypeORM if needed
  ) {
    this.centiivKey = this.configService.get<string>('CENTIIV_TEST_APIKEY');
    this.centiivUrl = this.configService.get<string>('CENTIIV_SANDBOX_URL');
  }

  // CentiivPay
  // Direct Payment Link
  async createDirectPaymentLink(createPaymentDto: CreateDirectPaymentLinkDto) {
    try {
      // Check if centiivKey and centiivUrl are not empty or null
      if (!this.centiivKey || !this.centiivUrl) {
        throw new InternalServerErrorException(
          'Centiiv API Key or URL is missing',
        );
      }

      // Prepare the payload for the request
      const payload = {
        amount: createPaymentDto.amount,
        notes: createPaymentDto.notes,
      };

      // Make the request to Centiiv's API
      const response = await axios.post(
        `${this.centiivUrl}/direct-pay`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${this.centiivKey}`,
            'Content-Type': 'application/json',
          },
        },
      );

      // Log and return the response from the API
      console.log('Direct payment link created successfully');
      return response.data;
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException(error);
    }
  }

  async findOneDirectPaymentLink(id: string) {
    try {
      // Check if centiivKey and centiivUrl are not empty or null
      if (!this.centiivKey || !this.centiivUrl) {
        throw new InternalServerErrorException(
          'Centiiv API Key or URL is missing',
        );
      }

      // Make the request to Centiiv's API
      const response = await axios.get(`${this.centiivUrl}/direct-pay/${id}`, {
        headers: {
          Authorization: `Bearer ${this.centiivKey}`,
          'Content-Type': 'application/json',
        },
      });

      // Log and return the response from the API
      console.log('Direct payment link fetched successfully');
      return response.data;
    } catch (error) {
      if (
        error.response.data.statusCode === 404 &&
        error.response.data.code === 'PAYMENT_LINK_NOT_FOUND'
      ) {
        throw new BadRequestException(error.response.data);
      } else {
        throw new InternalServerErrorException(error);
      }
    }
  }

  async findAllDirectPaymentLink() {
    try {
      // Check if centiivKey and centiivUrl are not empty or null
      if (!this.centiivKey || !this.centiivUrl) {
        throw new InternalServerErrorException(
          'Centiiv API Key or URL is missing',
        );
      }

      // Make the request to Centiiv's API
      const response = await axios.get(`${this.centiivUrl}/direct-pay`, {
        headers: {
          Authorization: `Bearer ${this.centiivKey}`,
          'Content-Type': 'application/json',
        },
      });

      // Log and return the response from the API
      console.log('All direct payment links fetched successfully');
      return response.data;
    } catch (error) {
      if (
        error.response.data.statusCode === 404 &&
        error.response.data.code === 'PAYMENT_LINK_NOT_FOUND'
      ) {
        throw new BadRequestException(error.response.data);
      } else {
        throw new InternalServerErrorException(error);
      }
    }
  }

  async deleteDirectPaymentLink(id: string) {
    try {
      // Check if centiivKey and centiivUrl are not empty or null
      if (!this.centiivKey || !this.centiivUrl) {
        throw new InternalServerErrorException(
          'Centiiv API Key or URL is missing',
        );
      }

      // Make the request to Centiiv's API
      const response = await axios.delete(
        `${this.centiivUrl}/direct-pay/${id}`,
        {
          headers: {
            Authorization: `Bearer ${this.centiivKey}`,
            'Content-Type': 'application/json',
          },
        },
      );

      // Log and return the response from the API
      console.log('Direct payment link deleted successfully');
      return response.data;
    } catch (error) {
      if (
        error.response.data.statusCode === 404 &&
        error.response.data.code === 'PAYMENT_LINK_NOT_FOUND'
      ) {
        throw new BadRequestException(error.response.data);
      } else {
        throw new InternalServerErrorException(error);
      }
    }
  }

  // Disbursement - Single
  async createSingleDisbursement(createDisbursementDto: SingleDisbursementDto) {
    try {
      // Check if centiivKey and centiivUrl are not empty or null
      if (!this.centiivKey || !this.centiivUrl) {
        throw new InternalServerErrorException(
          'Centiiv API Key or URL is missing',
        );
      }

      // Prepare the payload for the request
      const payload = {
        amount: createDisbursementDto.amount,
        currency: createDisbursementDto.currency,
        recipientAccountNumber: createDisbursementDto.recipient_account_number,
        recipientBankCode: createDisbursementDto.recipient_bank_code,
        description: createDisbursementDto.description,
      };

      // Make the request to Centiiv's API
      const response = await axios.post(
        `${this.centiivUrl}/baas/disburse`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${this.centiivKey}`,
            'Content-Type': 'application/json',
          },
        },
      );

      // Log and return the response from the API
      console.log('Disbursement requested successfully');
      return response.data;
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException(error);
    }
  }

  async fetchAccountDetails() {
    try {
      // Check if centiivKey and centiivUrl are not empty or null
      if (!this.centiivKey || !this.centiivUrl) {
        throw new InternalServerErrorException(
          'Centiiv API Key or URL is missing',
        );
      }

      // Make the request to Centiiv's API
      const response = await axios.get(`${this.centiivUrl}/baas/account`, {
        headers: {
          Authorization: `Bearer ${this.centiivKey}`,
          'Content-Type': 'application/json',
        },
      });

      // Log and return the response from the API
      console.log("Eweko's fetched successfully");
      return response.data;
    } catch (error) {
      if (
        error.response.data.statusCode === 404 &&
        error.response.data.code === 'PAYMENT_LINK_NOT_FOUND'
      ) {
        throw new BadRequestException(error.response.data);
      } else {
        throw new InternalServerErrorException(error);
      }
    }
  }

  async checkDisbursementStatus(partialAssociateId: string) {
    try {
      // Check if centiivKey and centiivUrl are not empty or null
      if (!this.centiivKey || !this.centiivUrl) {
        throw new InternalServerErrorException(
          'Centiiv API Key or URL is missing',
        );
      }

      // Make the request to Centiiv's API
      const response = await axios.get(
        `${this.centiivUrl}/baas/transaction/${partialAssociateId}`,
        {
          headers: {
            Authorization: `Bearer ${this.centiivKey}`,
            'Content-Type': 'application/json',
          },
        },
      );

      // Log and return the response from the API
      console.log('Disbursement transaction status checked successfully');
      return response.data;
    } catch (error) {
      if (
        error.response.data.statusCode === 404 &&
        error.response.data.code === 'PAYMENT_LINK_NOT_FOUND'
      ) {
        throw new BadRequestException(error.response.data);
      } else {
        throw new InternalServerErrorException(error);
      }
    }
  }

  async paystackWebhook(req: Request, res: Response) {
    try {
      // 1. Validate IP address
      // const allowedIPs: string[] = [
      //   '************',
      //   '*************',
      //   '*************',
      // ];

      // const clientIPString = Array.isArray(req.headers['x-forwarded-for'])
      //   ? req.headers['x-forwarded-for'][0]
      //   : req.headers['x-forwarded-for'];

      // const clientIP = clientIPString ? clientIPString.split(':')[0] : '';

      // if (!clientIP || !allowedIPs.includes(clientIP)) {
      //   console.warn(
      //     `Unauthorized webhook access from IP: ${clientIP || 'unknown'}`,
      //   );
      //   return res.status(HttpStatus.FORBIDDEN).json({
      //     success: false,
      //     message: 'Unauthorized access',
      //   });
      // }

      const allowedIPs = ['************', '*************', '*************'];

      const clientIPRaw = req.headers['x-forwarded-for'] as string;
      const clientIP = clientIPRaw?.split(',')[0].trim();

      if (!clientIP || !allowedIPs.includes(clientIP)) {
        console.warn(
          `Unauthorized webhook access from IP: ${clientIP || 'unknown'}`,
        );
        return res.status(HttpStatus.FORBIDDEN).json({
          success: false,
          message: 'Unauthorized access',
        });
      }

      console.log('---------------------------');
      console.log('x-forwarded-for:', req.headers['x-forwarded-for']);
      console.log('---------------------------');

      // 2. Validate signature
      const signature = Array.isArray(req.headers['x-paystack-signature'])
        ? req.headers['x-paystack-signature'][0]
        : req.headers['x-paystack-signature'];

      if (!signature) {
        console.warn('Missing Paystack signature');
        return res.status(HttpStatus.FORBIDDEN).json({
          success: false,
          message: 'Invalid paystack webhook request',
        });
      }

      // 3. Parse and validate body
      let body = req.body;
      const isValid = await this.validatePaystackWebhook(body, signature);

      if (!isValid) {
        console.warn('Invalid Paystack signature');
        return res.status(HttpStatus.FORBIDDEN).json({
          success: false,
          message: 'Invalid webhook signature',
        });
      }

      // 4. Handle only charge.success events
      if (body.event !== 'charge.success') {
        return res.status(HttpStatus.OK).json({
          success: true,
          message: 'Event acknowledged but not processed',
        });
      }

      // 5. Extract data
      const { amount, reference, metadata } = body.data;

      const orderId = metadata.custom_fields.find(
        (field) => field.variable_name === 'order_id',
      )?.value;
      const buyerId = metadata.custom_fields.find(
        (field) => field.variable_name === 'buyer_id',
      )?.value;
      const transactionId = metadata.custom_fields.find(
        (field) => field.variable_name === 'transaction_id',
      )?.value;

      if (!orderId || !buyerId || !transactionId) {
        console.error('Missing required metadata in webhook payload');
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Incomplete payment metadata',
        });
      }

      // 6. Find order, buyer and transaction
      const order = await this.orderService.findOne(orderId);
      if (!order) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          message: 'Order not found',
        });
      }

      const user = await this.userService.findById(buyerId);
      if (!user || user.user_type !== UserType.BUYER) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          message: 'Buyer not found or invalid user type',
        });
      }

      const existingTransaction =
        await this.transactionService.findOne(transactionId);
      if (!existingTransaction) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          message: 'Transaction not found',
        });
      }

      // 7. Check if already processed (idempotency)
      if (existingTransaction.status === TransactionStatus.SUCCESS) {
        return res.status(HttpStatus.OK).json({
          success: true,
          message: 'Transaction already processed',
        });
      }

      // 8. Verify amount and ownership
      if (amount / 100 !== existingTransaction.total_amount) {
        console.error(
          `Payment amount mismatch: expected ${existingTransaction.total_amount}, got ${amount}`,
        );
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Payment amount does not match order amount',
        });
      }

      if (
        user.id !== existingTransaction.buyer_id ||
        order.id !== existingTransaction.order_id
      ) {
        console.error('Transaction details mismatch with order or buyer');
        return res.status(HttpStatus.CONFLICT).json({
          success: false,
          message: 'Transaction details mismatch',
        });
      }

      // 9. Update order and transaction
      await this.orderService.update(orderId, {
        payment_status: PaymentStatus.COMPLETED,
      });

      await this.transactionService.update(transactionId, {
        status: TransactionStatus.COMPLETED,
        processed: true,
      });

      // Update produce stock
      // I have order, loop through order items, update the produce stock
      await Promise.all(
        order.items.map(async (item) => {
          // Extract the correct ID from produce
          const produceId = typeof item.produce === 'string'
            ? item.produce
            : (item.produce as any)?.id || (item.produce as any)?.id;

          const produce = await this.produceRepository.findOne({ where: { id:
            produceId.toString() } });

          if (!produce) {
            throw new Error(`Produce not found`);
          }

          if (produce.stock < item.quantity) {
            throw new Error(`Insufficient stock for ${produce.name}`);
          }

          produce.stock -= item.quantity;
          await this.produceRepository.save(produce);
        }),
      );

      // 10. Process farmer payments
      const ewekoPercentage = 0.02; // 2% commission
      const orderItems = order.items;
      const farmersArr: { farmerId: string; farmerEarnings: number }[] = [];

      // Collect farmer earnings

      for (const orderItem of orderItems) {
        try {
          // Extract the correct ObjectId
          const produceId =
            orderItem.produce && typeof orderItem.produce === 'object'
              ? (orderItem.produce as any).id.toString() // 👈 Typecasting `as any`
              : orderItem.produce.toString(); // If already an ObjectId, use it

          console.log('Extracted Produce ID:', produceId);

          // Fetch the produce by its ID
          const product = await this.produceService.findOne(produceId);
          if (!product) continue;

          // Extract farmer ID properly
          const farmerId =
            product.farmer && typeof product.farmer === 'object'
              ? product.farmer.id
              : product.farmer.toString();

          console.log('Extracted Farmer ID:', farmerId);

          const farmer = await this.userService.findById(farmerId);
          if (!farmer) continue;

          const totalPrice = orderItem.total_price;
          const existingFarmer = farmersArr.find(
            (f) => f.farmerId === farmerId,
          );

          if (!existingFarmer) {
            farmersArr.push({ farmerId, farmerEarnings: totalPrice });
          } else {
            existingFarmer.farmerEarnings += totalPrice;
          }
        } catch (error) {
          console.error(`Error processing order item: ${error.message}`);
        }
      }

      // Distribute earnings
      if (farmersArr.length > 0) {
        const totalEarnings = farmersArr.reduce(
          (sum, farmer) => sum + farmer.farmerEarnings,
          0,
        );

        const ewekoEarning = ewekoPercentage * totalEarnings;
        const finalFarmerAmount = totalEarnings - ewekoEarning;

        // Update farmer wallets
        for (const farmerData of farmersArr) {
          try {
            const farmerShare =
              (farmerData.farmerEarnings / totalEarnings) * finalFarmerAmount;

            const farmer = await this.userService.findById(farmerData.farmerId);

            const wallet = await this.walletRepository.findOne({
              where: { farmer_id: farmer.id },
            });

            if (!wallet) {
              return res.status(HttpStatus.NOT_FOUND).json({
                success: false,
                message: `Wallet not found for farmer ${farmerData.farmerId}`,
              });
            }

            wallet.balance = wallet.balance + farmerShare;
            wallet.balance = wallet.balance + farmerShare;
            await this.walletRepository.save(wallet);
          } catch (error) {
            console.error(`Error updating farmer wallet: ${error.message}`);
          }
        }
      }

      console.info(`Successfully processed payment for order: ${orderId}`);

      // 11. Return success
      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Payment processed successfully',
      });
    } catch (error) {
      console.error('Error processing paystack webhook:', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'An error occurred while processing paystack webhook',
      });
    }
  }

  private async validatePaystackWebhook(
    body: any,
    signature: string,
  ): Promise<boolean> {
    const paystackSK = this.configService.get<string>('PAYSTACK_SK');

    const hash = createHmac('sha512', paystackSK)
      .update(JSON.stringify(body))
      .digest('hex');

    return hash === signature;
  }

  async centiivWebhook(req: Request, res: Response) {
    try {
      const allowedDomains = [
        'http://localhost:3000',
        'https://ewekoaggregate.com',
        'https://www.ewekoaggregate.com',
      ];

      const origin = req.headers.origin;

      // Get signature
      const signature = Array.isArray(req.headers['x-centiiv-signature'])
        ? req.headers['x-centiiv-signature'][0]
        : req.headers['x-centiiv-signature'];

      const body = req.body;

      let isValid = false;

      if (signature) {
        const rawPayload = JSON.stringify(body); // Assumes body parser retains raw body
        isValid = await this.validateCentiivWebhook(rawPayload, signature);
      }

      const isAllowedDomain = allowedDomains.includes(origin);

      if (!isAllowedDomain && !isValid) {
        console.warn('Unauthorized Centiiv webhook access');
        return res.status(HttpStatus.FORBIDDEN).json({
          success: false,
          message: 'Unauthorized webhook source',
        });
      }

      if (body.status !== 'successful') {
        return res.status(HttpStatus.OK).json({
          success: true,
          message: 'Event acknowledged but not processed',
        });
      }

      const { amount, metadata } = body;

      const orderId = metadata.orderId;
      const buyerId = metadata.buyerId;
      const transactionId = metadata.transactionId;

      if (!orderId || !buyerId || !transactionId) {
        console.error('Missing required metadata in webhook payload');
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Incomplete payment metadata',
        });
      }

      // 6. Find order, buyer and transaction
      const order = await this.orderService.findOne(orderId);
      if (!order) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          message: 'Order not found',
        });
      }

      const user = await this.userService.findById(buyerId);
      if (!user || user.user_type !== UserType.BUYER) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          message: 'Buyer not found or invalid user type',
        });
      }

      const existingTransaction =
        await this.transactionService.findOne(transactionId);
      if (!existingTransaction) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          message: 'Transaction not found',
        });
      }

      // 7. Check if already processed (idempotency)
      if (existingTransaction.status === TransactionStatus.SUCCESS) {
        return res.status(HttpStatus.OK).json({
          success: true,
          message: 'Transaction already processed',
        });
      }

      // 8. Verify amount and ownership

      if (amount !== existingTransaction.total_amount) {
        console.error(
          `Payment amount mismatch: expected ${existingTransaction.total_amount}, got ${amount}`,
        );
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Payment amount does not match order amount',
        });
      }

      if (
        user.id !== existingTransaction.buyer_id ||
        order.id !== existingTransaction.order_id
      ) {
        console.error('Transaction details mismatch with order or buyer');
        return res.status(HttpStatus.CONFLICT).json({
          success: false,
          message: 'Transaction details mismatch',
        });
      }

      // 9. Update order and transaction
      await this.orderService.update(orderId, {
        payment_status: PaymentStatus.COMPLETED,
      });

      await this.transactionService.update(transactionId, {
        status: TransactionStatus.COMPLETED,
        processed: true,
      });

      // Update produce stock
      // I have order, loop through order items, update the produce stock
      await Promise.all(
        order.items.map(async (item) => {
          // Extract the correct ID from produce
          const produceId = typeof item.produce === 'string'
            ? item.produce
            : (item.produce as any)?.id || (item.produce as any)?.id;

          const produce = await this.produceRepository.findOne({ where: { id:
            produceId.toString() } });

          if (!produce) {
            throw new Error(`Produce not found`);
          }

          if (produce.stock < item.quantity) {
            throw new Error(`Insufficient stock for ${produce.name}`);
          }

          produce.stock -= item.quantity;
          await this.produceRepository.save(produce);
        }),
      );

      // 10. Process farmer payments
      const ewekoPercentage = 0.02; // 2% commission
      const orderItems = order.items;
      const farmersArr: { farmerId: string; farmerEarnings: number }[] = [];

      // Collect farmer earnings

      for (const orderItem of orderItems) {
        try {
          // Extract the correct ObjectId
          const produceId =
            orderItem.produce && typeof orderItem.produce === 'object'
              ? (orderItem.produce as any).id.toString() // 👈 Typecasting `as any`
              : orderItem.produce.toString(); // If already an ObjectId, use it

          console.log('Extracted Produce ID:', produceId);

          // Fetch the produce by its ID
          const product = await this.produceService.findOne(produceId);
          if (!product) continue;

          // Extract farmer ID properly
          const farmerId =
            product.farmer && typeof product.farmer === 'object'
              ? product.farmer.id
              : product.farmer.toString();

          console.log('Extracted Farmer ID:', farmerId);

          const farmer = await this.userService.findById(farmerId);
          if (!farmer) continue;

          const totalPrice = orderItem.total_price;
          const existingFarmer = farmersArr.find(
            (f) => f.farmerId === farmerId,
          );

          if (!existingFarmer) {
            farmersArr.push({ farmerId, farmerEarnings: totalPrice });
          } else {
            existingFarmer.farmerEarnings += totalPrice;
          }
        } catch (error) {
          console.error(`Error processing order item: ${error.message}`);
        }
      }

      // Distribute earnings
      if (farmersArr.length > 0) {
        const totalEarnings = farmersArr.reduce(
          (sum, farmer) => sum + farmer.farmerEarnings,
          0,
        );

        const ewekoEarning = ewekoPercentage * totalEarnings;
        const finalFarmerAmount = totalEarnings - ewekoEarning;

        // Update farmer wallets
        for (const farmerData of farmersArr) {
          try {
            const farmerShare =
              (farmerData.farmerEarnings / totalEarnings) * finalFarmerAmount;

            const farmer = await this.userService.findById(farmerData.farmerId);

            const wallet = await this.walletRepository.findOne({
              where: { farmer_id: farmer.id },
            });

            if (!wallet) {
              return res.status(HttpStatus.NOT_FOUND).json({
                success: false,
                message: `Wallet not found for farmer ${farmerData.farmerId}`,
              });
            }

            wallet.balance = wallet.balance + farmerShare;
            wallet.balance = wallet.balance + farmerShare;
            await this.walletRepository.save(wallet);
          } catch (error) {
            console.error(`Error updating farmer wallet: ${error.message}`);
          }
        }
      }

      console.info(`Successfully processed payment for order: ${orderId}`);

      // 11. Return success
      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Payment processed successfully',
      });
    } catch (error) {
      console.error('Error processing centiiv webhook:', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'An error occurred while processing centiiv webhook',
      });
    }
  }

  // const isValid = await this.validateCentiivWebhook(body, signature);

  // if (!isValid) {
  //   console.warn('Invalid Paystack signature');
  //   return res.status(HttpStatus.FORBIDDEN).json({
  //     success: false,
  //     message: 'Invalid webhook signature',
  //   });
  // }

  private async validateCentiivWebhook(
    payload: string,
    signature: string,
  ): Promise<boolean> {
    const hmac = crypto.createHmac(
      'sha256',
      this.configService.getOrThrow('CENTIIV_WEBHOOK_SECRET'),
    );
    hmac.update(payload);
    const calculatedSignature = hmac.digest('hex');
    return calculatedSignature === signature;
  }
}
