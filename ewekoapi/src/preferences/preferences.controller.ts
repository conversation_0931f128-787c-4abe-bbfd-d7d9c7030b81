import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { PreferencesService } from './preferences.service';
import {
  CreatePreferencesDto,
  PreferencesResponseDto,
  UpdatePreferencesDto,
} from './dto/create-preference.dto';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { EwekoAuthGuard, RolesGuard } from 'src/auth/jwt.guard';

@Controller('preferences')
@ApiTags('preferences')
export class PreferencesController {
  constructor(private readonly preferencesService: PreferencesService) {}

  @Post()
  @ApiOperation({ summary: 'Create user preferences' })
  @ApiResponse({
    status: 201,
    description: 'Preferences created successfully',
    type: PreferencesResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Preferences already exist for this user',
  })
  @ApiBody({ type: CreatePreferencesDto })
  async create(@Body() createPreferencesDto: CreatePreferencesDto) {
    return this.preferencesService.create(createPreferencesDto);
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Get()
  @ApiOperation({ summary: 'Get all preferences' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved preferences',
    type: [PreferencesResponseDto],
  })
  async findAll() {
    return this.preferencesService.findAll();
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Get(':userId')
  @ApiOperation({ summary: 'Get preferences by user ID' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved preferences',
    type: PreferencesResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Preferences not found',
  })
  async findOne(@Param('userId') userId: string) {
    return this.preferencesService.findOne(userId);
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Patch(':userId')
  @ApiOperation({ summary: 'Update preferences by user ID' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'Preferences updated successfully',
    type: PreferencesResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Preferences not found',
  })
  @ApiBody({ type: UpdatePreferencesDto })
  async update(
    @Param('userId') userId: string,
    @Body() updatePreferencesDto: UpdatePreferencesDto,
  ) {
    return this.preferencesService.update(userId, updatePreferencesDto);
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Delete(':userId')
  @ApiOperation({ summary: 'Delete preferences by user ID' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'Preferences deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Preferences not found',
  })
  async remove(@Param('userId') userId: string) {
    return this.preferencesService.remove(userId);
  }
}
