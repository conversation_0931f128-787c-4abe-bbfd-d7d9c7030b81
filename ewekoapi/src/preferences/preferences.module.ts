import { Module } from '@nestjs/common';
import { PreferencesService } from './preferences.service';
import { PreferencesController } from './preferences.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { Preferences } from './entities/preferences.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Preferences]),
  ],
  controllers: [PreferencesController],
  providers: [PreferencesService],
  exports: [PreferencesService, TypeOrmModule],
})
export class PreferencesModule {}
