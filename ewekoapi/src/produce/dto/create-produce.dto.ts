import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsMongoId,
  IsArray,
  IsDate,
  Min,
  IsNotEmpty,
  IsUrl,
  IsDateString,
  IsUUID,
} from 'class-validator';


export class CreateProduceDto {
  @ApiProperty({
    description: 'ID of the farmer',
    example: '65a2bc34ef123456789abcde',
  })
  @IsString()
  @IsNotEmpty()
  farmer: string;

  @ApiProperty({
    description: 'ID of the category',
    example: '65a2bc34ef123456789abcd1',
  })
  @IsString()
  @IsNotEmpty()
  category: string;

  @ApiProperty({ description: 'Produce name', example: 'Organic Tomatoes' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Produce description',
    example: 'Fresh organic tomatoes from local farms',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ description: 'Price of the produce', example: 500 })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({ description: 'Negotiable price', example: 450 })
  @IsNumber()
  @Min(0)
  negotiable_price: number;

  @ApiProperty({ description: 'Stock available', example: 100 })
  @IsNumber()
  @Min(0)
  stock: number;

  @ApiProperty({ description: 'Minimum order quantity', example: 5 })
  @IsNumber()
  @Min(1)
  min_order_qty: number;

  @ApiProperty({
    description: 'Harvest date',
    example: '2025-01-24T00:00:00.000Z',
  })
  @IsDateString()
  @IsNotEmpty()
  harvest_date: Date;

  @ApiProperty({
    description: 'Array of image URLs',
    example: [
      'https://example.com/images/produce1.jpg',
      'https://example.com/images/produce2.png',
    ],
  })
  @IsArray()
  @IsString({ each: true })
  @IsUrl({}, { each: true })
  images: string[];
}

export class ProduceResponseDto {
  @ApiProperty({
    description: 'Produce ID',
    example: '65a2de56ef123456789klmno',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'ID of the farmer',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsUUID()
  farmer_id: string;

  @ApiProperty({
    description: 'ID of the category',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
  })
  @IsUUID()
  category_id: string;

  @ApiProperty({ description: 'Produce name', example: 'Organic Tomatoes' })
  name: string;

  @ApiProperty({
    description: 'Produce description',
    example: 'Fresh organic tomatoes from local farms',
  })
  description: string;

  @ApiProperty({ description: 'Price of the produce', example: 500 })
  price: number;

  @ApiProperty({ description: 'Negotiable price', example: 450 })
  negotiable_price: number;

  @ApiProperty({ description: 'Stock available', example: 100 })
  stock: number;

  @ApiProperty({ description: 'Minimum order quantity', example: 5 })
  min_order_qty: number;

  @ApiProperty({
    description: 'Harvest date',
    example: '2025-01-24T00:00:00.000Z',
  })
  harvest_date: Date;

  @ApiProperty({
    description: 'Array of image URLs',
    example: [
      'https://example.com/images/produce1.jpg',
      'https://example.com/images/produce2.png',
    ],
  })
  @IsArray()
  @IsString({ each: true })
  @IsUrl({}, { each: true })
  images: string[];

  @ApiProperty({
    description: 'Unique produce slug',
    example: 'organic-tomatoes',
  })
  slug: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2025-01-24T12:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2025-01-24T12:30:00.000Z',
  })
  updated_at: Date;
}
