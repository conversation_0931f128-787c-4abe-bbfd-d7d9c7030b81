import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ProduceService } from './produce.service';
import { CreateProduceDto, ProduceResponseDto } from './dto/create-produce.dto';
import { UpdateProduceDto } from './dto/update-produce.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { UserType } from 'src/shared/enums';
import { EwekoAuthGuard, RolesGuard } from 'src/auth/jwt.guard';
import { CurrentUser } from 'src/shared/decorators/currentUser.decorator';

@Controller('produce')
@ApiTags('produce')
export class ProduceController {
  constructor(private readonly productsService: ProduceService) {}

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN, UserType.FARMER)
  @Post()
  @ApiOperation({ summary: 'Create a new product' })
  @ApiResponse({
    status: 201,
    description: 'Product successfully created',
    type: ProduceResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiBody({ type: CreateProduceDto })
  create(@Body() createProduceDto: CreateProduceDto) {
    return this.productsService.create(createProduceDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all products' })
  @ApiResponse({
    status: 200,
    description: 'List of all products',
    type: [ProduceResponseDto],
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  findAll(
    @Query() paginationQuery: PaginationQueryDto,
    @Query('search') search?: string,
  ) {
    return this.productsService.findAll(paginationQuery, search);
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN, UserType.FARMER)
  @Get('farmer')
  @ApiOperation({ summary: 'Get all products by farmer' })
  @ApiResponse({
    status: 200,
    description: 'List of all products by this farmer',
    type: [ProduceResponseDto],
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  findAllByFarmer(
    @CurrentUser('id') farmerId: string,
    @Query() paginationQuery: PaginationQueryDto,
    @Query('search') search?: string,
  ) {
    return this.productsService.findAllByFarmer(farmerId, paginationQuery, search);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a product by ID' })
  @ApiResponse({
    status: 200,
    description: 'Product details',
    type: ProduceResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Product not found' })
  @ApiParam({ name: 'id', required: true, description: 'Product ID' })
  findOne(@Param('id') id: string) {
    return this.productsService.findOne(id);
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN, UserType.FARMER)
  @Patch(':id')
  @ApiOperation({ summary: 'Update a product by ID' })
  @ApiResponse({
    status: 200,
    description: 'Product successfully updated',
    type: ProduceResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  @ApiParam({ name: 'id', required: true, description: 'Product ID' })
  @ApiBody({ type: UpdateProduceDto })
  update(@Param('id') id: string, @Body() updateProduceDto: UpdateProduceDto) {
    return this.productsService.update(id, updateProduceDto);
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN, UserType.FARMER)
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a product by ID' })
  @ApiResponse({ status: 200, description: 'Product successfully deleted' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  @ApiParam({ name: 'id', required: true, description: 'Product ID' })
  remove(@Param('id') id: string): Promise<{ message: string }> {
    return this.productsService.remove(id);
  }
}
