import { Module } from '@nestjs/common';
import { ProduceService } from './produce.service';
import { ProduceController } from './produce.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { Farmer } from '../users/entities/farmers/farmer.entity';
import { Category } from '../category/entities/category.entity';
import { Produce } from './entities/produce.entity';
import { PaginationService } from 'src/shared/pagination/pagination.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Farmer, Produce, Category]),
  ],
  controllers: [ProduceController],
  providers: [ProduceService, PaginationService],
  exports: [ProduceService, TypeOrmModule],
})
export class ProduceModule {}
