import { CreateProduceDto } from './dto/create-produce.dto';
import { UpdateProduceDto } from './dto/update-produce.dto';

import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Farmer } from '../users/entities/farmers/farmer.entity';
import { Category } from '../category/entities/category.entity';
import { Produce } from './entities/produce.entity';
import { slugify } from 'src/shared/utils';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { PaginationService } from 'src/shared/pagination/pagination.service';

@Injectable()
export class ProduceService {
  constructor(
    @InjectRepository(Produce)
    private produceRepository: Repository<Produce>,
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
    @InjectRepository(Farmer)
    private farmerRepository: Repository<Farmer>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly paginationService: PaginationService,
  ) {}

  async create(createProduceDto: CreateProduceDto) {
    console.log(createProduceDto);

    const { farmer, category, name } = createProduceDto;

    // Check if the farmer exists
    const existingFarmer = await this.farmerRepository.findOne({ where: { id: farmer } });
    if (!existingFarmer) {
      throw new NotFoundException(`Farmer not found`);
    }

    // Check if the category exists
    const existingCategory = await this.categoryRepository.findOne({ where: { id: category } });
    if (!existingCategory) {
      throw new NotFoundException(`Category not found`);
    }

    // Check for duplicate produce name
    const existingName = await this.produceRepository.findOne({ where: { name } });
    if (existingName) {
      throw new ConflictException(
        `A produce with the name "${name}" already exists`,
      );
    }

    // Generate and check slug
    const slug = slugify(name);
    const existingSlug = await this.produceRepository.findOne({ where: { slug } });
    if (existingSlug) {
      throw new ConflictException(
        `A produce with the slug "${slug}" already exists`,
      );
    }

    // Create and save the product
    const newProduct = this.produceRepository.create({
      ...createProduceDto,
      farmer: existingFarmer,
      category: existingCategory,
      slug,
    });

    return await this.produceRepository.save(newProduct);
  }



  async findAll(paginationQuery: PaginationQueryDto, searchQuery?: string) {
    const { page = 1, limit = 10 } = paginationQuery;

    // Base query
    const baseQuery = searchQuery
      ? {
          name: { $regex: searchQuery, $options: 'i' },
        }
      : {};

    // Build the query
    let queryBuilder = this.produceRepository.createQueryBuilder('produce');

    // Sort by name if using regex search (since we don't have text score)
    if (searchQuery) {
      queryBuilder = queryBuilder.where('produce.name ILIKE :search', { search: `%${searchQuery}%` });
    }

    // Add relations and execute query with pagination
    const products = await queryBuilder
      .leftJoinAndSelect('produce.category', 'category')
      .leftJoinAndSelect('produce.farmer', 'farmer')
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    // Return paginated response
    return this.paginationService.paginate(products, {
      page,
      limit,
    });
  }

  async findAllByFarmer(
    farmerId: string,
    paginationQuery: PaginationQueryDto,
    searchQuery?: string,
  ) {
    const { page = 1, limit = 10 } = paginationQuery;

    const filter: Record<string, any> = {
      farmer_id: farmerId,
    };

    let queryBuilder = this.produceRepository
      .createQueryBuilder('produce')
      .leftJoinAndSelect('produce.category', 'category')
      .leftJoinAndSelect('produce.farmer', 'farmer');

    if (searchQuery) {
      queryBuilder = queryBuilder.where('produce.name ILIKE :searchQuery', {
        searchQuery: `%${searchQuery}%`
      });
    }

    const products = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy('produce.created_at', 'DESC')
      .getMany();

    return this.paginationService.paginate(products, {
      page,
      limit,
    });
  }

  async findOne(id: string) {
    const product = await this.produceRepository.findOne({
      where: { id },
      relations: ['category', 'farmer']
    });
    if (!product) {
      throw new NotFoundException(`Produce not found`);
    }
    return product;
  }

  async update(
    id: string,
    updateProduceDto: UpdateProduceDto,
  ): Promise<Produce> {
    const updateProduceData: any = {
      ...updateProduceDto,
    };

    if (updateProduceDto.name) {
      updateProduceData.slug = slugify(updateProduceDto.name);
    }
    const result = await this.produceRepository.update(id, updateProduceData);
    if (result.affected === 0) {
      throw new NotFoundException(`Produce with id ${id} not found`);
    }
    const updatedProduct = await this.produceRepository.findOne({ where: { id } });
    if (!updatedProduct) {
      throw new NotFoundException(`Produce not found`);
    }
    return updatedProduct;
  }

  async remove(id: string): Promise<{ message: string }> {
    const result = await this.produceRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Produce not found`);
    }
    return { message: `Produce successfully deleted` };
  }
}
