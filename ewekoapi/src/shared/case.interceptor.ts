import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import camelcaseKeys from 'camelcase-keys';
import snakecaseKeys from 'snakecase-keys';

// Helper function to safely convert keys
function safeSnakeCaseKeys(obj: any): any {
  if (!obj || typeof obj !== 'object') return obj;
  if (Array.isArray(obj)) return obj.map(safeSnakeCaseKeys);
  if (obj instanceof Date || obj instanceof Buffer) return obj;
  
  try {
    return snakecaseKeys(obj, { 
      deep: true,
      stopPaths: ['password', 'token', 'secret', 'otp', 'code'],
    });
  } catch (error) {
    console.warn('Failed to convert to snake_case:', error.message);
    console.log('Problematic object:', JSON.stringify(obj, null, 2));
    return obj;
  }
}

function safeCamelCaseKeys(obj: any): any {
  if (!obj || typeof obj !== 'object') return obj;
  if (Array.isArray(obj)) return obj.map(safeCamelCaseKeys);
  if (obj instanceof Date || obj instanceof Buffer) return obj;
  
  try {
    return camelcaseKeys(obj, { 
      deep: true,
      stopPaths: ['password', 'token', 'secret', 'otp', 'code'],
    });
  } catch (error) {
    console.warn('Failed to convert to camelCase:', error.message);
    console.log('Problematic object:', JSON.stringify(obj, null, 2));
    return obj;
  }
}

@Injectable()
export class CaseInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // Convert incoming request body to snake_case
    const req = context.switchToHttp().getRequest();
    if (req.body && typeof req.body === 'object' && !Array.isArray(req.body)) {
      console.log('🔍 Incoming request body:', JSON.stringify(req.body, null, 2));
      req.body = safeSnakeCaseKeys(req.body);
      console.log('🔍 Converted request body:', JSON.stringify(req.body, null, 2));
    }
    
    return next.handle().pipe(
      map(data => {
        console.log('🔍 Outgoing response data:', JSON.stringify(data, null, 2));
        const converted = safeCamelCaseKeys(data);
        console.log('🔍 Converted response data:', JSON.stringify(converted, null, 2));
        return converted;
      })
    );
  }
} 