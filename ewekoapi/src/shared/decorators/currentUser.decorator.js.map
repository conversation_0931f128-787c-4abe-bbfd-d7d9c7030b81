{"version": 3, "file": "currentUser.decorator.js", "sourceRoot": "/", "sources": ["../shared/decorators/currentUser.decorator.ts"], "names": [], "mappings": ";;;AAAA,2CAAwE;AAE3D,QAAA,WAAW,GAAG,IAAA,6BAAoB,EAC7C,CAAC,IAAwB,EAAE,GAAqB,EAAE,EAAE;IAClD,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;IAChD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAE1B,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACpC,CAAC,CACF,CAAC", "sourcesContent": ["import { createParamDecorator, ExecutionContext } from '@nestjs/common';\n\nexport const CurrentUser = createParamDecorator(\n  (data: string | undefined, ctx: ExecutionContext) => {\n    const request = ctx.switchToHttp().getRequest();\n    const user = request.user;\n\n    return data ? user?.[data] : user;\n  },\n);\n"]}