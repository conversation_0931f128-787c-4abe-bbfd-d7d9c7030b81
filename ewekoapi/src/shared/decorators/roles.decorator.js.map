{"version": 3, "file": "roles.decorator.js", "sourceRoot": "/", "sources": ["../shared/decorators/roles.decorator.ts"], "names": [], "mappings": ";;;AAAA,2CAA6C;AAGtC,MAAM,KAAK,GAAG,CAAC,GAAG,KAAiB,EAAE,EAAE,CAAC,IAAA,oBAAW,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAA9D,QAAA,KAAK,SAAyD", "sourcesContent": ["import { SetMetadata } from '@nestjs/common';\nimport { UserType } from '../enums';\n\nexport const Roles = (...roles: UserType[]) => SetMetadata('roles', roles);\n"]}