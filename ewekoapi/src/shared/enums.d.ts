export declare enum OtpUseCase {
    LOGIN = "LOGIN",
    '2FA' = "2FA",
    D2FA = "D2FA",
    VRFY_EMAIL = "VRFY_EMAIL",
    VRFY_PHONE = "VRFY_PHONE",
    PWDR = "PWDR"
}
export declare enum OrderStatus {
    PROCESSING = "Processing",
    SHIPPED = "Shipped",
    DELIVERED = "Delivered",
    CANCELLED = "Cancelled"
}
export declare enum PaymentStatus {
    PENDING = "Pending",
    COMPLETED = "Completed",
    REFUNDED = "Refunded",
    FAILED = "Failed"
}
export declare enum PaymentMethod {
    CENTIIVPAY = "CentiivPay",
    PAYSTACK = "Paystack"
}
export declare enum PaymentType {
    PURCHASE = "Purchase",
    CONTRACT_FARMING = "Contract Farming"
}
export declare enum UserType {
    BUYER = "BUYER",
    FARMER = "FARMER",
    ADMIN = "ADMIN",
    AGENT = "AGENT"
}
export declare enum AdminRole {
    SUPER_ADMIN = "SUPER_ADMIN",
    SUB_ADMIN = "SUB_ADMIN"
}
export declare enum NegotiationStatus {
    PENDING = "PENDING",
    ACCEPTED = "ACCEPTED",
    REJECTED = "REJECTED"
}
export declare enum NotificationTrigger {
    USER_REGISTRATION = "USER_REGISTRATION",
    USER_VERIFICATION = "USER_VERIFICATION",
    USER_LOGIN = "USER_LOGIN",
    PASSWORD_RESET = "PASSWORD_RESET",
    NEW_ORDER = "NEW_ORDER",
    ORDER_STATUS = "ORDER_STATUS_CHANGE",
    SMS_VERIFICATION = "SMS_VERIFICATION",
    EMAIL_VERIFICATION = "EMAIL_VERIFICATION"
}
export declare enum NotificationDestination {
    EMAIL = "EMAIL",
    SMS = "SMS"
}
export declare enum QuoteStatus {
    PENDING = "PENDING",
    ACCEPTED = "ACCEPTED",
    REJECTED = "REJECTED"
}
