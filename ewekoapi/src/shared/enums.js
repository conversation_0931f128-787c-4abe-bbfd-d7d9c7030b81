"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuoteStatus = exports.NotificationDestination = exports.NotificationTrigger = exports.NegotiationStatus = exports.AdminRole = exports.UserType = exports.PaymentType = exports.PaymentMethod = exports.PaymentStatus = exports.OrderStatus = exports.OtpUseCase = void 0;
var OtpUseCase;
(function (OtpUseCase) {
    OtpUseCase["LOGIN"] = "LOGIN";
    OtpUseCase["2FA"] = "2FA";
    OtpUseCase["D2FA"] = "D2FA";
    OtpUseCase["VRFY_EMAIL"] = "VRFY_EMAIL";
    OtpUseCase["VRFY_PHONE"] = "VRFY_PHONE";
    OtpUseCase["PWDR"] = "PWDR";
})(OtpUseCase || (exports.OtpUseCase = OtpUseCase = {}));
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["PROCESSING"] = "Processing";
    OrderStatus["SHIPPED"] = "Shipped";
    OrderStatus["DELIVERED"] = "Delivered";
    OrderStatus["CANCELLED"] = "Cancelled";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
var PaymentStatus;
(function (PaymentStatus) {
    PaymentStatus["PENDING"] = "Pending";
    PaymentStatus["COMPLETED"] = "Completed";
    PaymentStatus["REFUNDED"] = "Refunded";
    PaymentStatus["FAILED"] = "Failed";
})(PaymentStatus || (exports.PaymentStatus = PaymentStatus = {}));
var PaymentMethod;
(function (PaymentMethod) {
    PaymentMethod["CENTIIVPAY"] = "CentiivPay";
    PaymentMethod["PAYSTACK"] = "Paystack";
})(PaymentMethod || (exports.PaymentMethod = PaymentMethod = {}));
var PaymentType;
(function (PaymentType) {
    PaymentType["PURCHASE"] = "Purchase";
    PaymentType["CONTRACT_FARMING"] = "Contract Farming";
})(PaymentType || (exports.PaymentType = PaymentType = {}));
var UserType;
(function (UserType) {
    UserType["BUYER"] = "BUYER";
    UserType["FARMER"] = "FARMER";
    UserType["ADMIN"] = "ADMIN";
    UserType["AGENT"] = "AGENT";
})(UserType || (exports.UserType = UserType = {}));
var AdminRole;
(function (AdminRole) {
    AdminRole["SUPER_ADMIN"] = "SUPER_ADMIN";
    AdminRole["SUB_ADMIN"] = "SUB_ADMIN";
})(AdminRole || (exports.AdminRole = AdminRole = {}));
var NegotiationStatus;
(function (NegotiationStatus) {
    NegotiationStatus["PENDING"] = "PENDING";
    NegotiationStatus["ACCEPTED"] = "ACCEPTED";
    NegotiationStatus["REJECTED"] = "REJECTED";
})(NegotiationStatus || (exports.NegotiationStatus = NegotiationStatus = {}));
var NotificationTrigger;
(function (NotificationTrigger) {
    NotificationTrigger["USER_REGISTRATION"] = "USER_REGISTRATION";
    NotificationTrigger["USER_VERIFICATION"] = "USER_VERIFICATION";
    NotificationTrigger["USER_LOGIN"] = "USER_LOGIN";
    NotificationTrigger["PASSWORD_RESET"] = "PASSWORD_RESET";
    NotificationTrigger["NEW_ORDER"] = "NEW_ORDER";
    NotificationTrigger["ORDER_STATUS"] = "ORDER_STATUS_CHANGE";
    NotificationTrigger["SMS_VERIFICATION"] = "SMS_VERIFICATION";
    NotificationTrigger["EMAIL_VERIFICATION"] = "EMAIL_VERIFICATION";
})(NotificationTrigger || (exports.NotificationTrigger = NotificationTrigger = {}));
var NotificationDestination;
(function (NotificationDestination) {
    NotificationDestination["EMAIL"] = "EMAIL";
    NotificationDestination["SMS"] = "SMS";
})(NotificationDestination || (exports.NotificationDestination = NotificationDestination = {}));
var QuoteStatus;
(function (QuoteStatus) {
    QuoteStatus["PENDING"] = "PENDING";
    QuoteStatus["ACCEPTED"] = "ACCEPTED";
    QuoteStatus["REJECTED"] = "REJECTED";
})(QuoteStatus || (exports.QuoteStatus = QuoteStatus = {}));
//# sourceMappingURL=enums.js.map