{"version": 3, "file": "enums.js", "sourceRoot": "/", "sources": ["../shared/enums.ts"], "names": [], "mappings": ";;;AAAA,IAAY,UAOX;AAPD,WAAY,UAAU;IACpB,6BAAe,CAAA;IACf,yBAAa,CAAA;IACb,2BAAa,CAAA;IACb,uCAAyB,CAAA;IACzB,uCAAyB,CAAA;IACzB,2BAAa,CAAA;AACf,CAAC,EAPW,UAAU,0BAAV,UAAU,QAOrB;AAED,IAAY,WAKX;AALD,WAAY,WAAW;IACrB,wCAAyB,CAAA;IACzB,kCAAmB,CAAA;IACnB,sCAAuB,CAAA;IACvB,sCAAuB,CAAA;AACzB,CAAC,EALW,WAAW,2BAAX,WAAW,QAKtB;AAED,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,oCAAmB,CAAA;IACnB,wCAAuB,CAAA;IACvB,sCAAqB,CAAA;IACrB,kCAAiB,CAAA;AACnB,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAED,IAAY,aAGX;AAHD,WAAY,aAAa;IACvB,0CAAyB,CAAA;IACzB,sCAAqB,CAAA;AACvB,CAAC,EAHW,aAAa,6BAAb,aAAa,QAGxB;AAED,IAAY,WAGX;AAHD,WAAY,WAAW;IACrB,oCAAqB,CAAA;IACrB,oDAAqC,CAAA;AACvC,CAAC,EAHW,WAAW,2BAAX,WAAW,QAGtB;AAED,IAAY,QAKX;AALD,WAAY,QAAQ;IAClB,2BAAe,CAAA;IACf,6BAAiB,CAAA;IACjB,2BAAe,CAAA;IACf,2BAAe,CAAA;AACjB,CAAC,EALW,QAAQ,wBAAR,QAAQ,QAKnB;AAED,IAAY,SAGX;AAHD,WAAY,SAAS;IACnB,wCAA2B,CAAA;IAC3B,oCAAuB,CAAA;AACzB,CAAC,EAHW,SAAS,yBAAT,SAAS,QAGpB;AAED,IAAY,iBAIX;AAJD,WAAY,iBAAiB;IAC3B,wCAAmB,CAAA;IACnB,0CAAqB,CAAA;IACrB,0CAAqB,CAAA;AACvB,CAAC,EAJW,iBAAiB,iCAAjB,iBAAiB,QAI5B;AAED,IAAY,mBASX;AATD,WAAY,mBAAmB;IAC7B,8DAAuC,CAAA;IACvC,8DAAuC,CAAA;IACvC,gDAAyB,CAAA;IACzB,wDAAiC,CAAA;IACjC,8CAAuB,CAAA;IACvB,2DAAoC,CAAA;IACpC,4DAAqC,CAAA;IACrC,gEAAyC,CAAA;AAC3C,CAAC,EATW,mBAAmB,mCAAnB,mBAAmB,QAS9B;AAED,IAAY,uBAGX;AAHD,WAAY,uBAAuB;IACjC,0CAAe,CAAA;IACf,sCAAW,CAAA;AACb,CAAC,EAHW,uBAAuB,uCAAvB,uBAAuB,QAGlC;AAED,IAAY,WAIX;AAJD,WAAY,WAAW;IACrB,kCAAmB,CAAA;IACnB,oCAAqB,CAAA;IACrB,oCAAqB,CAAA;AACvB,CAAC,EAJW,WAAW,2BAAX,WAAW,QAItB", "sourcesContent": ["export enum OtpUseCase {\n  LOGIN = 'LOGIN',\n  '2FA' = '2FA',\n  D2FA = 'D2FA',\n  VRFY_EMAIL = 'VRFY_EMAIL',\n  VRFY_PHONE = 'VRFY_PHONE',\n  PWDR = 'PWDR',\n}\n\nexport enum OrderStatus {\n  PROCESSING = 'Processing',\n  SHIPPED = 'Shipped',\n  DELIVERED = 'Delivered',\n  CANCELLED = 'Cancelled',\n}\n\nexport enum PaymentStatus {\n  PENDING = 'Pending',\n  COMPLETED = 'Completed',\n  REFUNDED = 'Refunded',\n  FAILED = 'Failed',\n}\n\nexport enum PaymentMethod {\n  CENTIIVPAY = 'CentiivPay',\n  PAYSTACK = 'Paystack',\n}\n\nexport enum PaymentType {\n  PURCHASE = 'Purchase',\n  CONTRACT_FARMING = 'Contract Farming',\n}\n\nexport enum UserType {\n  BUYER = 'BUYER',\n  FARMER = 'FARMER',\n  ADMIN = 'ADMIN',\n  AGENT = 'AGENT',\n}\n\nexport enum AdminRole {\n  SUPER_ADMIN = 'SUPER_ADMIN',\n  SUB_ADMIN = 'SUB_ADMIN',\n}\n\nexport enum NegotiationStatus {\n  PENDING = 'PENDING',\n  ACCEPTED = 'ACCEPTED',\n  REJECTED = 'REJECTED',\n}\n\nexport enum NotificationTrigger {\n  USER_REGISTRATION = 'USER_REGISTRATION',\n  USER_VERIFICATION = 'USER_VERIFICATION',\n  USER_LOGIN = 'USER_LOGIN',\n  PASSWORD_RESET = 'PASSWORD_RESET',\n  NEW_ORDER = 'NEW_ORDER',\n  ORDER_STATUS = 'ORDER_STATUS_CHANGE',\n  SMS_VERIFICATION = 'SMS_VERIFICATION',\n  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',\n}\n\nexport enum NotificationDestination {\n  EMAIL = 'EMAIL',\n  SMS = 'SMS',\n}\n\nexport enum QuoteStatus {\n  PENDING = 'PENDING',\n  ACCEPTED = 'ACCEPTED',\n  REJECTED = 'REJECTED',\n}\n"]}