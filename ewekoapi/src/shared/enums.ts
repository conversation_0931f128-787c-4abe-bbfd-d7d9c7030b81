export enum OtpUseCase {
  LOGIN = 'LOGIN',
  '2FA' = '2FA',
  D2FA = 'D2FA',
  VRFY_EMAIL = 'VRFY_EMAIL',
  VRFY_PHONE = 'VRFY_PHONE',
  PWDR = 'PWDR',
}

export enum OrderStatus {
  PROCESSING = 'Processing',
  SHIPPED = 'Shipped',
  DELIVERED = 'Delivered',
  CANCELLED = 'Cancelled',
}

export enum PaymentStatus {
  PENDING = 'Pending',
  COMPLETED = 'Completed',
  REFUNDED = 'Refunded',
  FAILED = 'Failed',
}

export enum PaymentMethod {
  CENTIIVPAY = 'Centiiv',
  PAYSTACK = 'Paystack',
}

export enum PaymentType {
  PURCHASE = 'Purchase',
  CONTRACT_FARMING = 'Contract Farming',
}

export enum UserType {
  BUYER = 'BUYER',
  FARMER = 'FARMER',
  ADMIN = 'ADMIN',
  AGENT = 'AGENT',
  LOGISTICS_AGENT = 'LOGISTICS_AGENT',
}


export enum AdminRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  SUB_ADMIN = 'SUB_ADMIN',
}

export enum NegotiationStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
}

export enum NotificationTrigger {
  USER_REGISTRATION = 'USER_REGISTRATION',
  USER_VERIFICATION = 'USER_VERIFICATION',
  USER_LOGIN = 'USER_LOGIN',
  PASSWORD_RESET = 'PASSWORD_RESET',
  NEW_ORDER = 'NEW_ORDER',
  ORDER_STATUS = 'ORDER_STATUS_CHANGE',
  SMS_VERIFICATION = 'SMS_VERIFICATION',
  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',
}

export enum NotificationDestination {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  BOTH = 'BOTH',
}

export enum QuoteStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
}

export interface PaginationResult<T> {
  data: T[];
  count: number;
  total: number;
  page: number;
  limit: number;
  offset: number;
  totalPages: number;
}
