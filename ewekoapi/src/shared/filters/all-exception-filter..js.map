{"version": 3, "file": "all-exception-filter..js", "sourceRoot": "/", "sources": ["../shared/filters/all-exception-filter..ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAMwB;AAGjB,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,KAAK,CAAC,SAAkB,EAAE,IAAmB;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;QAEjC,IAAI,MAAM,GAAG,mBAAU,CAAC,qBAAqB,CAAC;QAC9C,IAAI,OAAO,GAAG,uBAAuB,CAAC;QACtC,IAAI,YAAY,GAAG,EAAE,CAAC;QAEtB,IAAI,SAAS,YAAY,sBAAa,EAAE,CAAC;YAEvC,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,YAAY,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAE7C,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACrC,OAAO,GAAG,YAAY,CAAC;YACzB,CAAC;iBAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBAC5C,OAAO,GAAI,YAAoB,CAAC,OAAO,IAAI,OAAO,CAAC;gBACnD,YAAY,GAAG,YAAY,CAAC;YAC9B,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,YAAY,KAAK,EAAE,CAAC;YAEtC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAC5B,YAAY,GAAG;gBACb,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,KAAK,EAAE,SAAS,CAAC,KAAK;aACvB,CAAC;QACJ,CAAC;QAED,MAAM,aAAa,GAAG;YACpB,UAAU,EAAE,MAAM;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,CAAC,GAAG;YACjB,OAAO;YACP,YAAY;SACb,CAAC;QAEF,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;CACF,CAAA;AAxCY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,cAAK,GAAE;GACK,mBAAmB,CAwC/B", "sourcesContent": ["import {\n  ExceptionFilter,\n  Catch,\n  ArgumentsHost,\n  HttpException,\n  HttpStatus,\n} from '@nestjs/common';\n\n@Catch()\nexport class AllExceptionsFilter implements ExceptionFilter {\n  catch(exception: unknown, host: ArgumentsHost) {\n    const ctx = host.switchToHttp();\n    const response = ctx.getResponse();\n    const request = ctx.getRequest();\n\n    let status = HttpStatus.INTERNAL_SERVER_ERROR;\n    let message = 'Internal Server Error';\n    let errorDetails = {};\n\n    if (exception instanceof HttpException) {\n      // If it's an HttpException, extract status and response data\n      status = exception.getStatus();\n      const responseBody = exception.getResponse();\n\n      if (typeof responseBody === 'string') {\n        message = responseBody;\n      } else if (typeof responseBody === 'object') {\n        message = (responseBody as any).message || message;\n        errorDetails = responseBody;\n      }\n    } else if (exception instanceof Error) {\n      // For other errors, like standard JavaScript errors\n      message = exception.message;\n      errorDetails = {\n        name: exception.name,\n        stack: exception.stack,\n      };\n    }\n\n    const errorResponse = {\n      statusCode: status,\n      timestamp: new Date().toISOString(),\n      path: request.url,\n      message,\n      errorDetails, // Include detailed error information\n    };\n\n    response.status(status).json(errorResponse);\n  }\n}\n"]}