"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SentryExceptionFilter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SentryExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const Sentry = require("@sentry/node");
let SentryExceptionFilter = SentryExceptionFilter_1 = class SentryExceptionFilter {
    constructor() {
        this.logger = new common_1.Logger(SentryExceptionFilter_1.name);
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        let status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'Internal server error';
        if (exception instanceof common_1.HttpException) {
            status = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            message =
                typeof exceptionResponse === 'string'
                    ? exceptionResponse
                    : exceptionResponse.message || message;
        }
        Sentry.captureException(exception);
        this.logger.error(`Error: ${message} | Status: ${status} | Path: ${request.url}`, exception instanceof Error ? exception.stack : `${exception}`);
        response.status(status).json({
            statusCode: status,
            message,
            path: request.url,
        });
    }
};
exports.SentryExceptionFilter = SentryExceptionFilter;
exports.SentryExceptionFilter = SentryExceptionFilter = SentryExceptionFilter_1 = __decorate([
    (0, common_1.Catch)()
], SentryExceptionFilter);
//# sourceMappingURL=sentry-exceptions.filter.js.map