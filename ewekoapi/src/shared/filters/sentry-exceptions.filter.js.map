{"version": 3, "file": "sentry-exceptions.filter.js", "sourceRoot": "/", "sources": ["../shared/filters/sentry-exceptions.filter.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAOwB;AACxB,uCAAuC;AAGhC,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAA3B;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IAmCnE,CAAC;IAjCC,KAAK,CAAC,SAAkB,EAAE,IAAmB;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;QAEjC,IAAI,MAAM,GAAG,mBAAU,CAAC,qBAAqB,CAAC;QAC9C,IAAI,OAAO,GAAG,uBAAuB,CAAC;QAEtC,IAAI,SAAS,YAAY,sBAAa,EAAE,CAAC;YACvC,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAClD,OAAO;gBACL,OAAO,iBAAiB,KAAK,QAAQ;oBACnC,CAAC,CAAC,iBAAiB;oBACnB,CAAC,CAAE,iBAAyB,CAAC,OAAO,IAAI,OAAO,CAAC;QACtD,CAAC;QAGD,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAGnC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,UAAU,OAAO,cAAc,MAAM,YAAY,OAAO,CAAC,GAAG,EAAE,EAC9D,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE,CAC9D,CAAC;QAGF,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;YAC3B,UAAU,EAAE,MAAM;YAClB,OAAO;YACP,IAAI,EAAE,OAAO,CAAC,GAAG;SAClB,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AApCY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,cAAK,GAAE;GACK,qBAAqB,CAoCjC", "sourcesContent": ["import {\n  Catch,\n  ArgumentsHost,\n  ExceptionFilter,\n  HttpException,\n  HttpStatus,\n  Logger,\n} from '@nestjs/common';\nimport * as Sentry from '@sentry/node';\n\n@Catch()\nexport class SentryExceptionFilter implements ExceptionFilter {\n  private readonly logger = new Logger(SentryExceptionFilter.name);\n\n  catch(exception: unknown, host: ArgumentsHost) {\n    const ctx = host.switchToHttp();\n    const response = ctx.getResponse();\n    const request = ctx.getRequest();\n\n    let status = HttpStatus.INTERNAL_SERVER_ERROR;\n    let message = 'Internal server error';\n\n    if (exception instanceof HttpException) {\n      status = exception.getStatus();\n      const exceptionResponse = exception.getResponse();\n      message =\n        typeof exceptionResponse === 'string'\n          ? exceptionResponse\n          : (exceptionResponse as any).message || message;\n    }\n\n    // Capture exception with Sentry\n    Sentry.captureException(exception);\n\n    // Log the error\n    this.logger.error(\n      `Error: ${message} | Status: ${status} | Path: ${request.url}`,\n      exception instanceof Error ? exception.stack : `${exception}`,\n    );\n\n    // Send error response to the client\n    response.status(status).json({\n      statusCode: status,\n      message,\n      path: request.url,\n    });\n  }\n}\n"]}