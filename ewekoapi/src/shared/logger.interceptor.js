"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
let LoggerInterceptor = class LoggerInterceptor {
    constructor() {
        this.logger = new common_1.Logger('HTTP');
    }
    intercept(context, next) {
        const req = context.switchToHttp().getRequest();
        const method = req.method;
        const originalUrl = req.originalUrl;
        const startTime = Date.now();
        return next.handle().pipe((0, operators_1.map)((data) => {
            const duration = Date.now() - startTime;
            const { statusCode } = context.switchToHttp().getResponse();
            const contentLength = req.socket.bytesWritten;
            this.logger.warn('==========================');
            this.logger.debug(`${method} ${originalUrl} ${statusCode} - ${contentLength} bytes (${duration} ms)`);
            this.logger.warn('-------------------------');
            this.logger.debug(`Request payload: ${JSON.stringify(req.body, null, 2)}`);
            this.logger.warn('-------------------------');
            this.logger.debug(`Response payload: ${JSON.stringify(data, null, 2)}`);
            this.logger.warn('==========================');
            return data;
        }));
    }
};
exports.LoggerInterceptor = LoggerInterceptor;
exports.LoggerInterceptor = LoggerInterceptor = __decorate([
    (0, common_1.Injectable)()
], LoggerInterceptor);
//# sourceMappingURL=logger.interceptor.js.map