{"version": 3, "file": "logger.interceptor.js", "sourceRoot": "/", "sources": ["../shared/logger.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAMwB;AAExB,8CAA0C;AAGnC,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAAvB;QACG,WAAM,GAAG,IAAI,eAAM,CAAC,MAAM,CAAC,CAAC;IAkCtC,CAAC;IAhCC,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,GAAG,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QAChD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,IAAI,EAAE,EAAE;YAEX,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;YAC5D,MAAM,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;YAG9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,GAAG,MAAM,IAAI,WAAW,IAAI,UAAU,MAAM,aAAa,WAAW,QAAQ,MAAM,CACnF,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAE9C,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,oBAAoB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CACxD,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAE9C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAExE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAA;AAnCY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;GACA,iBAAiB,CAmC7B", "sourcesContent": ["import {\n  Injectable,\n  NestInterceptor,\n  Execution<PERSON><PERSON><PERSON>t,\n  <PERSON><PERSON><PERSON><PERSON>,\n  Lo<PERSON>,\n} from '@nestjs/common';\nimport { Observable } from 'rxjs';\nimport { tap, map } from 'rxjs/operators';\n\n@Injectable()\nexport class LoggerInterceptor implements NestInterceptor {\n  private logger = new Logger('HTTP');\n\n  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {\n    const req = context.switchToHttp().getRequest();\n    const method = req.method;\n    const originalUrl = req.originalUrl;\n    const startTime = Date.now();\n\n    return next.handle().pipe(\n      map((data) => {\n        // Capture the response payload\n        const duration = Date.now() - startTime;\n        const { statusCode } = context.switchToHttp().getResponse();\n        const contentLength = req.socket.bytesWritten;\n\n        // Log request and response details\n        this.logger.warn('==========================');\n        this.logger.debug(\n          `${method} ${originalUrl} ${statusCode} - ${contentLength} bytes (${duration} ms)`,\n        );\n        this.logger.warn('-------------------------');\n\n        this.logger.debug(\n          `Request payload: ${JSON.stringify(req.body, null, 2)}`,\n        );\n        this.logger.warn('-------------------------');\n\n        this.logger.debug(`Response payload: ${JSON.stringify(data, null, 2)}`);\n\n        this.logger.warn('==========================');\n        return data; // Ensure the response payload is returned\n      }),\n    );\n  }\n}\n"]}