import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON>t,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, map } from 'rxjs/operators';

@Injectable()
export class LoggerInterceptor implements NestInterceptor {
  private logger = new Logger('HTTP');

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest();
    const method = req.method;
    const originalUrl = req.originalUrl;
    const startTime = Date.now();

    return next.handle().pipe(
      map((data) => {
        // Capture the response payload
        const duration = Date.now() - startTime;
        const { statusCode } = context.switchToHttp().getResponse();
        const contentLength = req.socket.bytesWritten;

        // Log request and response details
        this.logger.warn('==========================');
        this.logger.debug(
          `${method} ${originalUrl} ${statusCode} - ${contentLength} bytes (${duration} ms)`,
        );
        this.logger.warn('-------------------------');

        this.logger.debug(
          `Request payload: ${JSON.stringify(req.body, null, 2)}`,
        );
        this.logger.warn('-------------------------');

        this.logger.debug(`Response payload: ${JSON.stringify(data, null, 2)}`);

        this.logger.warn('==========================');
        return data; // Ensure the response payload is returned
      }),
    );
  }
}
