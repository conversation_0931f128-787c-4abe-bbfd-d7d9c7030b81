{"version": 3, "file": "pagination-query.dto.js", "sourceRoot": "/", "sources": ["../shared/pagination/pagination-query.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAyD;AACzD,yDAAyC;AAEzC,MAAa,kBAAkB;IAA/B;QAKE,SAAI,GAAY,CAAC,CAAC;QAMlB,UAAK,GAAY,EAAE,CAAC;IACtB,CAAC;CAAA;AAZD,gDAYC;AAPC;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;IACP,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,qBAAG,EAAC,CAAC,CAAC;;gDACW;AAMlB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;IACP,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,qBAAG,EAAC,CAAC,CAAC;;iDACa", "sourcesContent": ["import { IsOptional, IsInt, Min } from 'class-validator';\nimport { Type } from 'class-transformer';\n\nexport class PaginationQueryDto {\n  @IsOptional()\n  @IsInt()\n  @Type(() => Number)\n  @Min(1)\n  page?: number = 1;\n\n  @IsOptional()\n  @IsInt()\n  @Type(() => Number)\n  @Min(1)\n  limit?: number = 10;\n}\n"]}