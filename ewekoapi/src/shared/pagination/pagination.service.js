"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginationService = void 0;
const common_1 = require("@nestjs/common");
let PaginationService = class PaginationService {
    paginate(items, paginationQuery) {
        const page = Math.max(1, paginationQuery.page || 1);
        const limit = Math.min(Math.max(1, paginationQuery.limit || 10), 100);
        const skip = (page - 1) * limit;
        const paginatedItems = items.slice(skip, skip + limit);
        const total = items.length;
        const count = paginatedItems.length;
        const totalPages = Math.ceil(total / limit);
        return {
            data: paginatedItems,
            count,
            total,
            page,
            limit,
            offset: skip,
            totalPages,
        };
    }
};
exports.PaginationService = PaginationService;
exports.PaginationService = PaginationService = __decorate([
    (0, common_1.Injectable)()
], PaginationService);
//# sourceMappingURL=pagination.service.js.map