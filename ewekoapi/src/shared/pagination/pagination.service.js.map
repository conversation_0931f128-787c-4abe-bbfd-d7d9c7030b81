{"version": 3, "file": "pagination.service.js", "sourceRoot": "/", "sources": ["../shared/pagination/pagination.service.ts"], "names": [], "mappings": ";;;;;;;;;AAuCA,2CAA4C;AAKrC,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,QAAQ,CACN,KAAU,EACV,eAAmC;QAEnC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;QACpD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACtE,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAEhC,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC;QACvD,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,MAAM,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC;QACpC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,OAAO;YACL,IAAI,EAAE,cAAc;YACpB,KAAK;YACL,KAAK;YACL,IAAI;YACJ,KAAK;YACL,MAAM,EAAE,IAAI;YACZ,UAAU;SACX,CAAC;IACJ,CAAC;CACF,CAAA;AAxBY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;GACA,iBAAiB,CAwB7B", "sourcesContent": ["// import { Injectable } from '@nestjs/common';\n// import { MongoRepository, Repository } from 'typeorm';\n// import { PaginationResult } from './types';\n// import { PaginationQueryDto } from './pagination-query.dto';\n\n// @Injectable()\n// export class PaginationService {\n//   async paginate<T>(\n//     repository: MongoRepository<T>,\n//     paginationQuery: PaginationQueryDto,\n//     relations: string[] = [],\n//   ): Promise<PaginationResult<T>> {\n//     const { page, limit } = paginationQuery;\n//     const take = limit || 10;\n//     const skip = (page - 1) * take;\n\n//     const [data, total] = await repository.findAndCount({\n//       relations,\n//       skip,\n//       take,\n//     });\n\n//     const count = data.length;\n//     const totalPages = Math.ceil(total / take);\n//     const status = count > 0 ? 'success' : 'no data';\n\n//     return {\n//       data,\n//       count,\n//       total,\n//       page,\n//       limit: take,\n//       offset: skip,\n//       totalPages,\n//       status,\n//     };\n//   }\n// }\n\nimport { Injectable } from '@nestjs/common';\nimport { PaginationQueryDto } from './pagination-query.dto';\nimport { PaginationResult } from '../types';\n\n@Injectable()\nexport class PaginationService {\n  paginate<T>(\n    items: T[],\n    paginationQuery: PaginationQueryDto,\n  ): PaginationResult<T> {\n    const page = Math.max(1, paginationQuery.page || 1);\n    const limit = Math.min(Math.max(1, paginationQuery.limit || 10), 100); // Ensures 1 ≤ limit ≤ 100\n    const skip = (page - 1) * limit;\n\n    const paginatedItems = items.slice(skip, skip + limit);\n    const total = items.length;\n    const count = paginatedItems.length;\n    const totalPages = Math.ceil(total / limit);\n\n    return {\n      data: paginatedItems,\n      count,\n      total,\n      page,\n      limit,\n      offset: skip,\n      totalPages,\n    };\n  }\n}\n"]}