

import { Injectable } from '@nestjs/common';
import { PaginationQueryDto } from './pagination-query.dto';
import { PaginationResult } from '../types';

@Injectable()
export class PaginationService {
  paginate<T>(
    items: T[],
    paginationQuery: PaginationQueryDto,
  ): PaginationResult<T> {
    const page = Math.max(1, paginationQuery.page || 1);
    const limit = Math.min(Math.max(1, paginationQuery.limit || 10), 100); // Ensures 1 ≤ limit ≤ 100
    const skip = (page - 1) * limit;

    const paginatedItems = items.slice(skip, skip + limit);
    const total = items.length;
    const count = paginatedItems.length;
    const totalPages = Math.ceil(total / limit);

    return {
      data: paginatedItems,
      count,
      total,
      page,
      limit,
      offset: skip,
      totalPages,
    };
  }
}
