{"version": 3, "file": "types.js", "sourceRoot": "/", "sources": ["../shared/types.ts"], "names": [], "mappings": "", "sourcesContent": ["\n// declare global {\n//   namespace Express {\n//     interface Request {\n//       user: {\n//         id: string;\n//         firstName: string;\n//         lastName: string;\n//         phoneNumber: string;\n//         email: string;\n//         userType: UserType;\n//         farmName?: string;\n//         adminRole?: AdminRole;\n//       };\n//     }\n//   }\n// }\n\nexport interface PaginationResult<T> {\n  data: T[];\n  count: number;\n  total: number;\n  page: number;\n  limit: number | null;\n  offset: number | null;\n  totalPages: number;\n}\n\nexport interface AdminToken {\n  token: string;\n  adminId: string;\n  createdAt: Date;\n  expiresAt: Date;\n}"]}