
// declare global {
//   namespace Express {
//     interface Request {
//       user: {
//         id: string;
//         firstName: string;
//         lastName: string;
//         phoneNumber: string;
//         email: string;
//         userType: UserType;
//         farmName?: string;
//         adminRole?: AdminRole;
//       };
//     }
//   }
// }

export interface PaginationResult<T> {
  data: T[];
  count: number;
  total: number;
  page: number;
  limit: number | null;
  offset: number | null;
  totalPages: number;
}

export interface AdminToken {
  token: string;
  adminId: string;
  createdAt: Date;
  expiresAt: Date;
}