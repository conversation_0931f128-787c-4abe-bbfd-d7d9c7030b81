"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.slugify = void 0;
const slugify = (name) => {
    if (!name) {
        throw new Error('Name is required to create a slug');
    }
    return name
        .normalize('NFKD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .trim()
        .replace(/[\s-]+/g, '-');
};
exports.slugify = slugify;
//# sourceMappingURL=utils.js.map