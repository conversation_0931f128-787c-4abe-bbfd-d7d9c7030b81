{"version": 3, "file": "utils.js", "sourceRoot": "/", "sources": ["../shared/utils.ts"], "names": [], "mappings": ";;;AAAO,MAAM,OAAO,GAAG,CAAC,IAAY,EAAU,EAAE;IAC9C,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,IAAI;SACR,SAAS,CAAC,MAAM,CAAC;SACjB,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;SAC/B,WAAW,EAAE;SACb,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;SAC5B,IAAI,EAAE;SACN,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AAC7B,CAAC,CAAC;AAZW,QAAA,OAAO,WAYlB", "sourcesContent": ["export const slugify = (name: string): string => {\n  if (!name) {\n    throw new Error('Name is required to create a slug');\n  }\n\n  return name\n    .normalize('NFKD') // Normalize to decompose diacritics (e.g., é -> e)\n    .replace(/[\\u0300-\\u036f]/g, '') // Remove diacritic marks\n    .toLowerCase() // Convert to lowercase\n    .replace(/[^a-z0-9\\s-]/g, '') // Remove invalid characters\n    .trim() // Remove leading/trailing whitespace\n    .replace(/[\\s-]+/g, '-'); // Replace spaces or repeated hyphens with a single hyphen\n};\n"]}