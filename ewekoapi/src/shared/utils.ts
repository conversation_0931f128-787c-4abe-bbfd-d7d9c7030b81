export const slugify = (name: string): string => {
  if (!name) {
    throw new Error('Name is required to create a slug');
  }

  return name
    .normalize('NFKD') // Normalize to decompose diacritics (e.g., é -> e)
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritic marks
    .toLowerCase() // Convert to lowercase
    .replace(/[^a-z0-9\s-]/g, '') // Remove invalid characters
    .trim() // Remove leading/trailing whitespace
    .replace(/[\s-]+/g, '-'); // Replace spaces or repeated hyphens with a single hyphen
};
