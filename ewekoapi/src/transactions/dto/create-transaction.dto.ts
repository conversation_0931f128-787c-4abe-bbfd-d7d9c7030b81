import { ApiProperty } from '@nestjs/swagger';
import { PaymentMethod, PaymentStatus, PaymentType } from 'src/shared/enums';
import {
  IsEnum,
  IsMongoId,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateTransactionDto {
  @ApiProperty({
    description: 'The ID of the user making the transaction',
    example: '65cabc1234def56789abcd01',
  })
  @IsMongoId()
  @IsNotEmpty()
  user: string;

  @ApiProperty({
    description: 'The associated order ID',
    example: '65cabd2234def56789abcd02',
  })
  @IsMongoId()
  @IsNotEmpty()
  order: string;

  @ApiProperty({
    description: 'The associated invoice ID (optional)',
    example: '65cabf3234def56789abcd03',
    required: false,
  })
  @IsMongoId()
  @IsOptional()
  invoice?: string;

  @ApiProperty({
    description: 'Total amount for the transaction',
    example: 15000,
  })
  @IsNumber()
  @IsNotEmpty()
  total_amount: number;

  @ApiProperty({
    description: 'Payment method used',
    enum: PaymentMethod,
    example: PaymentMethod.PAYSTACK,
  })
  @IsEnum(PaymentMethod)
  @IsNotEmpty()
  payment_method: PaymentMethod;

  @ApiProperty({
    description: 'Type of payment (one-time or subscription)',
    enum: PaymentType,
    example: PaymentType.PURCHASE,
  })
  @IsEnum(PaymentType)
  @IsNotEmpty()
  payment_type: PaymentType;
}

export class TransactionResponseDto {
  @ApiProperty({
    description: 'Transaction ID',
    example: '65cad2234def56789abcd05',
  })
  id: string;

  @ApiProperty({
    description: 'User ID associated with the transaction',
    example: '65cabc1234def56789abcd01',
  })
  user: string;

  @ApiProperty({
    description: 'Order ID related to the transaction',
    example: '65cabd2234def56789abcd02',
  })
  order: string;

  @ApiProperty({
    description: 'Invoice ID if applicable',
    example: '65cabf3234def56789abcd03',
    required: false,
  })
  invoice?: string;

  @ApiProperty({
    description: 'Total transaction amount',
    example: 15000,
  })
  total_amount: number;

  @ApiProperty({
    description: 'Transaction status',
    enum: PaymentStatus,
    example: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @ApiProperty({
    description: 'Payment method used',
    enum: PaymentMethod,
    example: PaymentMethod.PAYSTACK,
  })
  payment_method: PaymentMethod;

  @ApiProperty({
    description: 'Type of payment',
    enum: PaymentType,
    example: PaymentType.PURCHASE,
  })
  payment_type: PaymentType;

  @ApiProperty({
    description: 'Current payment status',
    enum: PaymentStatus,
    example: PaymentStatus.COMPLETED,
  })
  payment_status: PaymentStatus;

  @ApiProperty({
    description: 'Unique transaction reference',
    example: 'EWKTRX-ABC123XYZ456',
  })
  reference: string;

  @ApiProperty({
    description: 'Timestamp of when the transaction was created',
    example: '2025-02-10T12:30:00Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the transaction',
    example: '2025-02-10T14:00:00Z',
  })
  updated_at: Date;
}
