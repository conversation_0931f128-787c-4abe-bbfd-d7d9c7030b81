import { ApiProperty } from '@nestjs/swagger';
import { PaymentStatus } from 'src/shared/enums';
import { TransactionStatus } from '../entities/transaction.entity';
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional } from 'class-validator';

export class UpdateTransactionDto {
  @ApiProperty({
    description: 'Transaction status',
    enum: TransactionStatus,
    example: TransactionStatus.COMPLETED,
  })
  @IsEnum(TransactionStatus)
  @IsNotEmpty()
  status: TransactionStatus;

  @ApiProperty({
    description: 'Indicates whether the transaction has been processed',
    example: true,
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  processed?: boolean;
}
