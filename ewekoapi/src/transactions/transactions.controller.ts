import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import {
  CreateTransactionDto,
  TransactionResponseDto,
} from './dto/create-transaction.dto';
import { UpdateTransactionDto } from './dto/update-transaction.dto';
import {
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { EwekoAuthGuard, RolesGuard } from 'src/auth/jwt.guard';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { UserType } from 'src/shared/enums';
import { CurrentUser } from 'src/shared/decorators/currentUser.decorator';

@Controller('transactions')
@ApiTags('transactions')
// @UseGuards(EwekoAuthGuard)
export class TransactionsController {
  constructor(private readonly transactionsService: TransactionsService) {}

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN, UserType.BUYER)
  @Post()
  @ApiOperation({ summary: 'Create a new transaction' })
  @ApiResponse({
    status: 201,
    description: 'Transaction successfully created',
    type: TransactionResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid request payload' })
  create(@Body() createTransactionDto: CreateTransactionDto) {
    return this.transactionsService.create(createTransactionDto);
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN, UserType.BUYER)
  @Get()
  @ApiOperation({ summary: 'Retrieve all transactions' })
  @ApiResponse({
    status: 200,
    description: 'List of all transactions',
    type: [TransactionResponseDto],
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  findAll(@Query() paginationQuery: PaginationQueryDto) {
    return this.transactionsService.findAll(paginationQuery);
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN, UserType.FARMER)
  @Get('farmer')
  @ApiOperation({ summary: 'Get all transactions for farmer' })
  @ApiResponse({
    status: 200,
    description: 'List of all transactions for this farmer',
    type: [TransactionResponseDto],
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  findAllByFarmer(
    @CurrentUser('id') farmerId: string,
    @Query() paginationQuery: PaginationQueryDto,
  ) {
    return this.transactionsService.findAllByFarmer(farmerId, paginationQuery);
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN, UserType.BUYER)
  @Get('buyer')
  @ApiOperation({ summary: 'Get all transactions for buyer' })
  @ApiResponse({
    status: 200,
    description: 'List of all transactions for this buyer',
    type: [TransactionResponseDto],
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  findAllByBuyer(
    @CurrentUser('id') buyerId: string,
    @Query() paginationQuery: PaginationQueryDto,
  ) {
    return this.transactionsService.findAllByBuyer(buyerId, paginationQuery);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single transaction by ID' })
  @ApiParam({ name: 'id', required: true, description: 'Transaction ID' })
  @ApiResponse({
    status: 200,
    description: 'Transaction retrieved successfully',
    type: TransactionResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  findOne(@Param('id') id: string) {
    return this.transactionsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an existing transaction' })
  @ApiParam({ name: 'id', required: true, description: 'Transaction ID' })
  @ApiResponse({
    status: 200,
    description: 'Transaction updated successfully',
    type: TransactionResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  @ApiResponse({ status: 400, description: 'Invalid request payload' })
  update(
    @Param('id') id: string,
    @Body() updateTransactionDto: UpdateTransactionDto,
  ) {
    return this.transactionsService.update(id, updateTransactionDto);
  }

  @UseGuards(EwekoAuthGuard, RolesGuard)
  @Roles(UserType.ADMIN)
  @Delete('delete-all')
  async deleteAllTransactions() {
    return this.transactionsService.deleteAllTransactions();
  }
}
