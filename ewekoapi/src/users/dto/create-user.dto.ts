import { IsString, IsEmail, IsEnum, IsOptional, IsBoolean, IsDateString } from 'class-validator';
import { UserType } from '../../shared/enums';

export class CreateUserDto {
  // Authentication fields
  @IsString()
  username: string;

  @IsString()
  password: string;

  @IsEnum(UserType)
  user_type: UserType;

  @IsOptional()
  @IsBoolean()
  verified?: boolean;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  // Profile fields (now in main users table)
  @IsString()
  first_name: string;

  @IsString()
  last_name: string;

  @IsOptional()
  @IsString()
  middle_name?: string;

  @IsOptional()
  @IsString()
  prefix?: string;

  @IsOptional()
  @IsString()
  gender?: string;

  @IsOptional()
  @IsDateString()
  date_of_birth?: Date;

  @IsOptional()
  @IsString()
  profile_picture?: string;

  // Contact fields - email only (phones and addresses in separate tables)
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  primary_phone?: string;

  // Business fields (now in main users table)
  @IsOptional()
  @IsString()
  business_name?: string;

  // Common fields
  @IsOptional()
  @IsBoolean()
  is_premium?: boolean;

  @IsOptional()
  @IsBoolean()
  is_phone_verified?: boolean;

  @IsOptional()
  @IsBoolean()
  is_email_verified?: boolean;
}

export class CreateAddressDto {
  @IsString()
  street: string;

  @IsString()
  city: string;

  @IsString()
  state: string;

  @IsString()
  country: string;

  @IsOptional()
  @IsString()
  postal_code?: string;

  @IsOptional()
  @IsString()
  type?: string; // 'home', 'work', 'delivery', 'billing', 'other'

  @IsOptional()
  @IsBoolean()
  is_primary?: boolean;
}

export class CreateBuyerDto extends CreateUserDto {
  constructor() {
    super();
    this.user_type = UserType.BUYER;
  }
}

export class CreateFarmerDto extends CreateUserDto {
  @IsOptional()
  @IsString()
  farm_name?: string; // Maps to business_name

  constructor() {
    super();
    this.user_type = UserType.FARMER;
  }
}

// Response DTOs
export class BuyerResponseDto {
  id: string;
  username: string;
  user_type: UserType;
  first_name: string;
  last_name: string;
  email: string;
  primary_phone?: string;
  secondary_phone?: string;
  addresses?: CreateAddressDto[];
}

export class FarmerResponseDto {
  id: string;
  username: string;
  user_type: UserType;
  first_name: string;
  last_name: string;
  email: string;
  primary_phone?: string;
  secondary_phone?: string;
  addresses?: CreateAddressDto[];
}

export class UserStatsDto {
  total_users: number;
  total_buyers: number;
  total_farmers: number;
  total_admins: number;
}

export class UserWithTypeDto {
  id: string;
  username: string;
  type: UserType;
  verified: boolean;
  is_active: boolean;
  first_name: string;
  last_name: string;
  email: string;
  addresses?: CreateAddressDto[];
}
