import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { AdminRole } from '../../../shared/enums';

@Entity('admins')
export class Admin {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', unique: true })
  user_id: string;

  // Admin-specific fields only
  @Column({ type: 'enum', enum: AdminRole })
  role: AdminRole;

  @Column({ type: 'json', nullable: true })
  permissions: string[];

  @Column({ type: 'json', nullable: true })
  managed_departments: string[];

  @Column({ nullable: true })
  admin_token: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships - using forward reference to avoid circular dependency
  @OneToOne('User', 'admin_profile')
  @JoinColumn({ name: 'user_id' })
  user: any;
}
