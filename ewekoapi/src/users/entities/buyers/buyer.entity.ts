import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';

@Entity('buyers')
export class Buyer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', unique: true })
  user_id: string;

  // Buyer-specific fields only
  @Column({ nullable: true })
  business_name: string;

  @Column({ type: 'int', default: 0 })
  loyalty_points: number;

  @Column({ type: 'json', nullable: true })
  delivery_preferences: any;

  @Column({ type: 'json', nullable: true })
  payment_methods: any;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships - using forward references to avoid circular dependencies
  @OneToOne('User', 'buyer_profile')
  @JoinColumn({ name: 'user_id' })
  user: any;

  @OneToMany('Order', 'buyer')
  orders: any[];

  // Carts are now linked to users, not buyer profiles
}
