import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';

@Entity('farmers')
export class Farmer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', unique: true })
  user_id: string;

  // Farm-specific fields only
  @Column({ nullable: true })
  farm_name: string;

  @Column({ type: 'json', nullable: true })
  farm_location: any;

  @Column({ nullable: true })
  farm_size: string;

  @Column({ nullable: true })
  farm_address: string;

  // Banking details for farmers
  @Column({ nullable: true })
  account_number: string;

  @Column({ nullable: true })
  account_name: string;

  @Column({ nullable: true })
  bank_name: string;

  @Column({ nullable: true })
  bank_branch: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships - using forward references to avoid circular dependencies
  @OneToOne('User', 'farmer_profile')
  @JoinColumn({ name: 'user_id' })
  user: any;

  @OneToMany('Produce', 'farmer')
  produce: any[];

  @OneToOne('Wallet', 'farmer')
  wallet: any;

  @OneToMany('Order', 'farmer')
  farmer_orders: any[];
}
