import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  Index,
} from 'typeorm';
import { UserType } from '../../shared/enums';
import { Address } from '../../addresses/entities/address.entity';
import { Farmer } from './farmers/farmer.entity';
import { Buyer } from './buyers/buyer.entity';
import { Admin } from './admins/admin.entity';
import { Notification } from '../../notifications/entities/notification.entity';
import { Cart } from '../../cart/entities/cart.entity';

// Central Users Entity - All common fields in one table
@Entity('users')
@Index(['email'])
@Index(['primary_phone'])
@Index(['user_type'])
@Index(['username'])
@Index(['is_active'])
@Index(['verified'])
@Index(['user_type', 'is_active'])
@Index(['email', 'is_active'])
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Authentication fields
  @Column({ unique: true })
  username: string;

  @Column()
  password: string;

  @Column({ type: 'enum', enum: UserType })
  user_type: UserType;

  @Column({ default: false })
  verified: boolean;

  @Column({ default: true })
  is_active: boolean;

  @Column({ type: 'timestamp', nullable: true })
  last_login: Date;

  // Profile fields (previously in separate profiles table)
  @Column()
  first_name: string;

  @Column()
  last_name: string;

  @Column({ nullable: true })
  middle_name: string;

  @Column({ nullable: true })
  prefix: string;

  @Column({ type: 'enum', enum: ['male', 'female', 'other'], nullable: true })
  gender: string;

  @Column({ type: 'date', nullable: true })
  date_of_birth: Date;

  @Column({ nullable: true })
  profile_picture: string;

  // Contact fields 
  @Column({ unique: true, nullable: false })
  email: string;

  @Column({ nullable: false })
  primary_phone: string;

  @Column({ nullable: true })
  secondary_phone: string;
 


  // Common user fields
  @Column({ default: false })
  is_premium: boolean;

  @Column({ default: false })
  is_phone_verified: boolean;

  @Column({ default: false })
  is_email_verified: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships to type-specific tables only
  @OneToOne(() => Farmer, farmer => farmer.user)
  farmer_profile: Farmer;

  @OneToOne(() => Buyer, buyer => buyer.user)
  buyer_profile: Buyer;

  @OneToOne(() => Admin, admin => admin.user)
  admin_profile: Admin;

  // Relationships to other entities
  @OneToMany(() => Address, address => address.user)
  addresses: Address[];

  @OneToMany(() => Notification, notification => notification.user, { onDelete: 'CASCADE' })
  notifications: Notification[];

  @OneToMany(() => Cart, cart => cart.user, { onDelete: 'CASCADE' })
  carts: Cart[];
}
