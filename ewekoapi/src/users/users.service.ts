import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { UserType } from '../shared/enums';
import { Admin, Buyer, Farmer } from './entities';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Buyer)
    private buyerRepository: Repository<Buyer>,
    @InjectRepository(Farmer)
    private farmerRepository: Repository<Farmer>,
    @InjectRepository(Admin)
    private adminRepository: Repository<Admin>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return await this.userRepository.save(user);
  }

  async findAll(): Promise<User[]> {
    return await this.userRepository.find({
      order: { created_at: 'DESC' }
    });
  }

  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id }
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async findByEmail(email: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { email }
    });

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }

    return user;
  }

  async findByEmailOptional(email: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { email }
    });
  }

  async findUserByUsername(username: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { username }
    });

    if (!user) {
      throw new NotFoundException(`User with username ${username} not found`);
    }

    return user;
  }

  async findUserByUsernameOptional(username: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { username }
    });
  }

  async findUserByPhone(phoneNumber: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { primary_phone: phoneNumber }
    });

    if (!user) {
      throw new NotFoundException(`User with phone ${phoneNumber} not found`);
    }

    return user;
  }

  async findUserByPhoneOptional(phoneNumber: string): Promise<User | null> {
    const user = await this.userRepository.findOne({
      where: { primary_phone: phoneNumber }
    });

    return user;
  }

  async createBuyer(createBuyerDto: any): Promise<User> {
    // Hash password
    const hashedPassword = await bcrypt.hash(createBuyerDto.password, 10);

    // Map frontend fields to backend fields
    const userData = {
      ...createBuyerDto,
      password: hashedPassword,
      user_type: UserType.BUYER,
    };

    const user = await this.create(userData);

    // Create buyer profile
    const buyerProfile = this.buyerRepository.create({
      user_id: user.id,
      loyalty_points: 0,
    });

    await this.buyerRepository.save(buyerProfile);

    return user;
  }

  async createFarmer(createFarmerDto: any): Promise<User> {
    // Hash password
    const hashedPassword = await bcrypt.hash(createFarmerDto.password, 10);

    // Map frontend fields to backend fields
    const userData = {
      ...createFarmerDto,
      password: hashedPassword,
      user_type: UserType.FARMER,
    };

    const user = await this.create(userData);

    // Create farmer profile
    const farmerProfile = this.farmerRepository.create({
      user_id: user.id,
      farm_name: createFarmerDto.farm_name || createFarmerDto.business_name,
    });

    await this.farmerRepository.save(farmerProfile);

    return user;
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const result = await this.userRepository.update(id, updateUserDto);

    if (result.affected === 0) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return await this.findById(id);
  }

  async remove(id: string): Promise<void> {
    const result = await this.userRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
  }

  async updateBuyer(id: string, updateData: any): Promise<User> {
    // Update user data
    await this.userRepository.update(id, updateData);

    // Update buyer profile if needed
    const buyer = await this.buyerRepository.findOne({ where: { user_id: id } });
    if (buyer && updateData.loyalty_points !== undefined) {
      await this.buyerRepository.update(buyer.id, { loyalty_points: updateData.loyalty_points });
    }

    return await this.findById(id);
  }

  async updateFarmer(id: string, updateData: any): Promise<User> {
    // Update user data
    await this.userRepository.update(id, updateData);

    // Update farmer profile if needed
    const farmer = await this.farmerRepository.findOne({ where: { user_id: id } });
    if (farmer) {
      const farmerUpdateData: any = {};
      if (updateData.farm_name) farmerUpdateData.farm_name = updateData.farm_name;
      if (updateData.farm_size) farmerUpdateData.farm_size = updateData.farm_size;
      if (updateData.farm_address) farmerUpdateData.farm_address = updateData.farm_address;
      if (updateData.account_number) farmerUpdateData.account_number = updateData.account_number;
      if (updateData.account_name) farmerUpdateData.account_name = updateData.account_name;
      if (updateData.bank_name) farmerUpdateData.bank_name = updateData.bank_name;

      if (Object.keys(farmerUpdateData).length > 0) {
        await this.farmerRepository.update(farmer.id, farmerUpdateData);
      }
    }

    return await this.findById(id);
  }

  async findAllBuyers(): Promise<Buyer[]> {
    return await this.buyerRepository.find({
      relations: ['user'],
      order: { created_at: 'DESC' }
    });
  }

  async findBuyerById(id: string): Promise<Buyer> {
    const buyer = await this.buyerRepository.findOne({
      where: { user_id: id },
      relations: ['user']
    });

    if (!buyer) {
      throw new NotFoundException(`Buyer with ID ${id} not found`);
    }

    return buyer;
  }

  async deleteBuyer(id: string): Promise<{ message: string }> {
    const buyer = await this.buyerRepository.findOne({ where: { user_id: id } });
    if (buyer) {
      await this.buyerRepository.delete(buyer.id);
    }
    await this.remove(id);
    return { message: 'Buyer deleted successfully' };
  }

  async findAllFarmers(): Promise<Farmer[]> {
    return await this.farmerRepository.find({
      relations: ['user'],
      order: { created_at: 'DESC' }
    });
  }

  async findFarmerById(id: string): Promise<Farmer> {
    const farmer = await this.farmerRepository.findOne({
      where: { user_id: id },
      relations: ['user']
    });

    if (!farmer) {
      throw new NotFoundException(`Farmer with ID ${id} not found`);
    }

    return farmer;
  }

  async deleteFarmer(id: string): Promise<{ message: string }> {
    const farmer = await this.farmerRepository.findOne({ where: { user_id: id } });
    if (farmer) {
      await this.farmerRepository.delete(farmer.id);
    }
    await this.remove(id);
    return { message: 'Farmer deleted successfully' };
  }

  async findAllUsers(options: { skip?: number; limit?: number } = {}): Promise<User[]> {
    const { skip = 0, limit = 10 } = options;
    return await this.userRepository.find({
      skip,
      take: limit,
      order: { created_at: 'DESC' }
    });
  }

  async searchUsers(query: string, options: { skip?: number; limit?: number } = {}): Promise<User[]> {
    const { skip = 0, limit = 10 } = options;
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.addresses', 'addresses')
      .where('user.first_name ILIKE :query', { query: `%${query}%` })
      .orWhere('user.last_name ILIKE :query', { query: `%${query}%` })
      .orWhere('user.email ILIKE :query', { query: `%${query}%` })
      .orWhere('user.username ILIKE :query', { query: `%${query}%` })
      .skip(skip)
      .take(limit)
      .orderBy('user.created_at', 'DESC')
      .getMany();
  }

  async getUserStats(): Promise<any> {
    const total_users = await this.userRepository.count();
    const total_buyers = await this.buyerRepository.count();
    const total_farmers = await this.farmerRepository.count();
    const total_admins = await this.adminRepository.count();

    return {
      total_users,
      total_buyers,
      total_farmers,
      total_admins
    };
  }

  async getFarmerDashboard(farmerId: string): Promise<any> {
    const farmer = await this.findFarmerById(farmerId);

    // This would typically include more dashboard data
    return {
      farmer,
      // Add more dashboard data as needed
    };
  }
}
