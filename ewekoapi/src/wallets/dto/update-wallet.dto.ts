import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsArray, IsUUID } from 'class-validator';

export class UpdateWalletDto {
  @ApiProperty({
    description: 'Updated wallet balance',
    example: 5000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  balance?: number;

  @ApiProperty({
    description: 'Total amount earned by the farmer',
    example: 15000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  gross_revenue?: number;

}
