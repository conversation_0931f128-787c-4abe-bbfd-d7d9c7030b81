import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';

export class CategoryWeeklyPriceDto {
  @ApiProperty({ description: 'ID of the category' })
  @IsString()
  category_id: string;

  @ApiProperty({ description: 'Name of the category' })
  @IsString()
  category_name: string;

  @ApiProperty({ description: 'Current week\'s average price' })
  @IsNumber()
  current_price: number;

  @ApiProperty({ description: 'Previous week\'s average price' })
  @IsNumber()
  previous_price: number;

  @ApiProperty({ description: 'Price trend' })
  @IsString()
  trend: 'up' | 'down' | 'stable';

  @ApiProperty({ description: 'Minimum price this week' })
  @IsNumber()
  min_price: number;

  @ApiProperty({ description: 'Maximum price this week' })
  @IsNumber()
  max_price: number;
}

export class WeeklyPricesResponse {
  @ApiProperty({ 
    description: 'Array of weekly prices by category',
    type: [CategoryWeeklyPriceDto]
  })
  data: CategoryWeeklyPriceDto[];
}
