import { Controller, Get, UseGuards, Post } from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiBearerAuth,
  ApiOkResponse
} from '@nestjs/swagger';
import { EwekoAuthGuard } from '../auth/jwt.guard';
import { WeeklyPriceService } from './weekly-price.service';
import { WeeklyPricesResponse } from './dto/weekly-price.dto';

@ApiTags('Market')
@Controller('market/price-trends')
@ApiBearerAuth()
@UseGuards(EwekoAuthGuard)
export class WeeklyPriceController {
  constructor(private readonly weeklyPriceService: WeeklyPriceService) {}

  @Get('latest')
  @ApiOperation({ 
    summary: 'Get latest weekly prices for all categories',
    description: 'Returns the current and previous week\'s price data for all categories.'
  })
  @ApiOkResponse({ 
    description: 'Successfully retrieved latest weekly prices',
    type: WeeklyPricesResponse
  })
  async getLatestWeeklyPrices(): Promise<WeeklyPricesResponse> {
    return this.weeklyPriceService.getLatestWeeklyPrices();
  }

  @Post('calculate')
  @ApiOperation({
    summary: 'Trigger weekly price calculation manually',
    description: 'Admin endpoint to manually trigger the weekly price calculation.'
  })
  @ApiOkResponse({ description: 'Weekly price calculation started' })
  async triggerWeeklyCalculation() {
    await this.weeklyPriceService.calculateWeeklyAverages();
    return { message: 'Weekly price calculation started' };
  }
}
