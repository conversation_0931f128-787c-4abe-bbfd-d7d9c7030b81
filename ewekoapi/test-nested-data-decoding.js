const { MongoClient } = require('mongodb');
const zlib = require('zlib');

const MONGODB_URI = 'mongodb+srv://jamal:<EMAIL>/?retryWrites=true&w=majority&appName=ewekoapi';

// Enhanced decoding function with more methods
function tryDecodeBuffer(buffer) {
  if (!buffer) return null;
  
  // Handle different buffer formats
  let bufferData;
  if (buffer.buffer) {
    bufferData = Buffer.from(buffer.buffer);
  } else if (Buffer.isBuffer(buffer)) {
    bufferData = buffer;
  } else if (Array.isArray(buffer)) {
    bufferData = Buffer.from(buffer);
  } else {
    return null;
  }
  
  const methods = [
    // Method 1: Direct UTF-8
    () => {
      try {
        const str = bufferData.toString('utf8');
        if (str.startsWith('{') || str.startsWith('[')) {
          return JSON.parse(str);
        }
        return null;
      } catch (e) {
        return null;
      }
    },
    
    // Method 2: Try gzip decompression
    () => {
      try {
        const decompressed = zlib.gunzipSync(bufferData);
        const str = decompressed.toString('utf8');
        return JSON.parse(str);
      } catch (e) {
        return null;
      }
    },
    
    // Method 3: Try deflate decompression
    () => {
      try {
        const decompressed = zlib.inflateSync(bufferData);
        const str = decompressed.toString('utf8');
        return JSON.parse(str);
      } catch (e) {
        return null;
      }
    },
    
    // Method 4: Try base64 decode first
    () => {
      try {
        const str = bufferData.toString('utf8');
        const decoded = Buffer.from(str, 'base64');
        const decodedStr = decoded.toString('utf8');
        if (decodedStr.startsWith('{') || decodedStr.startsWith('[')) {
          return JSON.parse(decodedStr);
        }
        return null;
      } catch (e) {
        return null;
      }
    },
    
    // Method 5: Try different encodings
    () => {
      const encodings = ['latin1', 'ascii', 'hex'];
      for (const encoding of encodings) {
        try {
          const str = bufferData.toString(encoding);
          if (str.includes('{') || str.includes('[')) {
            return JSON.parse(str);
          }
        } catch (e) {
          continue;
        }
      }
      return null;
    },
    
    // Method 6: Try raw buffer inspection
    () => {
      try {
        // Check if it's a simple string without compression
        const str = bufferData.toString('binary');
        if (str.includes('firstName') || str.includes('lastName') || str.includes('email')) {
          // Try to extract JSON-like patterns
          const jsonMatch = str.match(/\{[^}]*\}/);
          if (jsonMatch) {
            return JSON.parse(jsonMatch[0]);
          }
        }
        return null;
      } catch (e) {
        return null;
      }
    }
  ];
  
  for (let i = 0; i < methods.length; i++) {
    const result = methods[i]();
    if (result) {
      console.log(`✅ Decoding successful with method ${i + 1}`);
      return result;
    }
  }
  
  return null;
}

async function testNestedDataDecoding() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db('ewekoapi');
    
    // Test with multiple users
    console.log('\n🧪 TESTING BUYERS WITH NESTED DATA:');
    const buyersWithNested = await db.collection('buyers').find({ 
      profile: { $exists: true } 
    }).limit(5).toArray();
    
    for (const buyer of buyersWithNested) {
      console.log(`\n👤 Testing buyer: ${buyer.username || buyer._id}`);
      console.log(`   Direct fields: firstName=${buyer.firstName}, lastName=${buyer.lastName}, email=${buyer.email}`);
      
      // Test profile decoding
      if (buyer.profile) {
        console.log(`   Profile buffer length: ${buyer.profile.buffer ? buyer.profile.buffer.length : 'no buffer'}`);
        const decodedProfile = tryDecodeBuffer(buyer.profile);
        if (decodedProfile) {
          console.log(`   ✅ Profile decoded: ${Object.keys(decodedProfile).join(', ')}`);
          if (decodedProfile.firstName || decodedProfile.lastName || decodedProfile.email) {
            console.log(`   📋 Profile data: firstName=${decodedProfile.firstName}, lastName=${decodedProfile.lastName}, email=${decodedProfile.email}`);
          }
        } else {
          console.log(`   ❌ Profile decoding failed`);
          // Show raw buffer info for debugging
          if (buyer.profile.buffer) {
            const buffer = Buffer.from(buyer.profile.buffer);
            console.log(`   🔍 Buffer preview: ${buffer.toString('utf8', 0, 50)}...`);
            console.log(`   🔍 Buffer hex: ${buffer.toString('hex', 0, 20)}...`);
          }
        }
      }
      
      // Test contact decoding
      if (buyer.contact) {
        const decodedContact = tryDecodeBuffer(buyer.contact);
        if (decodedContact) {
          console.log(`   ✅ Contact decoded: ${Object.keys(decodedContact).join(', ')}`);
        } else {
          console.log(`   ❌ Contact decoding failed`);
        }
      }
      
      // Test business decoding
      if (buyer.business) {
        const decodedBusiness = tryDecodeBuffer(buyer.business);
        if (decodedBusiness) {
          console.log(`   ✅ Business decoded: ${Object.keys(decodedBusiness).join(', ')}`);
        } else {
          console.log(`   ❌ Business decoding failed`);
        }
      }
    }
    
    console.log('\n🧪 TESTING FARMERS WITH NESTED DATA:');
    const farmersWithNested = await db.collection('farmers').find({ 
      profile: { $exists: true } 
    }).limit(3).toArray();
    
    for (const farmer of farmersWithNested) {
      console.log(`\n🌾 Testing farmer: ${farmer.username || farmer._id}`);
      console.log(`   Direct fields: firstName=${farmer.firstName}, lastName=${farmer.lastName}, email=${farmer.email}`);
      
      if (farmer.profile) {
        const decodedProfile = tryDecodeBuffer(farmer.profile);
        if (decodedProfile) {
          console.log(`   ✅ Profile decoded: ${Object.keys(decodedProfile).join(', ')}`);
        } else {
          console.log(`   ❌ Profile decoding failed`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

testNestedDataDecoding();
