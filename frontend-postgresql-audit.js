#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// MongoDB-specific patterns to search for
const MONGODB_PATTERNS = {
  // MongoDB ObjectId patterns
  objectId: /_id|ObjectId/g,
  mongoVersion: /__v/g,
  
  // MongoDB camelCase field patterns (should be snake_case in PostgreSQL)
  camelCaseFields: /\b(firstName|lastName|phoneNumber|userType|isPhoneVerified|isEmailVerified|isActive|lastLogin|profilePicture|createdAt|updatedAt|farmName|farmSize|businessName|loyaltyPoints|deliveryPreferences|paymentMethods|houseNumber|streetName|isDefault|totalCost|shippingFee|shippingAddress|finalTotalCost|paymentMethod|paymentStatus|paymentDate|shippedDate|negotiablePrice|minOrderQty|harvestDate|otpDestination|receivePromotions|generalUpdates|orderUpdates|transactionUpdates|paymentUpdates|deliveryUpdates|totalAmount|paymentType|gatewayReference|grossRevenue|unitPrice|totalPrice)\b/g,
  
  // MongoDB document structure patterns
  embeddedArrays: /\.(items|produces|notifications|addresses|transactions)\[/g,
  nestedObjects: /\.(farmer|category|buyer|user)\./g,
  
  // MongoDB query patterns
  mongoQueries: /\.find\(|\.findOne\(|\.findById\(|\.populate\(/g,
};

// PostgreSQL equivalent patterns
const POSTGRESQL_EQUIVALENTS = {
  _id: 'id',
  firstName: 'first_name',
  lastName: 'last_name',
  phoneNumber: 'primary_phone',
  userType: 'user_type',
  isPhoneVerified: 'is_phone_verified',
  isEmailVerified: 'is_email_verified',
  isActive: 'is_active',
  lastLogin: 'last_login',
  profilePicture: 'profile_picture',
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  farmName: 'farm_name',
  farmSize: 'farm_size',
  businessName: 'business_name',
  loyaltyPoints: 'loyalty_points',
  deliveryPreferences: 'delivery_preferences',
  paymentMethods: 'payment_methods',
  houseNumber: 'house_number',
  streetName: 'street_name',
  isDefault: 'is_default',
  totalCost: 'total_cost',
  shippingFee: 'shipping_fee',
  shippingAddress: 'shipping_address',
  finalTotalCost: 'final_total_cost',
  paymentMethod: 'payment_method',
  paymentStatus: 'payment_status',
  paymentDate: 'payment_date',
  shippedDate: 'shipped_date',
  negotiablePrice: 'negotiable_price',
  minOrderQty: 'min_order_qty',
  harvestDate: 'harvest_date',
  otpDestination: 'otp_destination',
  receivePromotions: 'receive_promotions',
  generalUpdates: 'general_updates',
  orderUpdates: 'order_updates',
  transactionUpdates: 'transaction_updates',
  paymentUpdates: 'payment_updates',
  deliveryUpdates: 'delivery_updates',
  totalAmount: 'total_amount',
  paymentType: 'payment_type',
  gatewayReference: 'gateway_reference',
  grossRevenue: 'gross_revenue',
  unitPrice: 'unit_price',
  totalPrice: 'total_price',
};

// File extensions to scan
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Directories to scan
const SCAN_DIRECTORIES = ['eweko/app', 'eweko/components', 'eweko/lib', 'eweko/hooks'];

function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      // Skip node_modules and .next directories
      if (!file.startsWith('.') && file !== 'node_modules') {
        arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
      }
    } else {
      if (EXTENSIONS.some(ext => file.endsWith(ext))) {
        arrayOfFiles.push(fullPath);
      }
    }
  });

  return arrayOfFiles;
}

function analyzeFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const issues = [];

  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    
    // Check for MongoDB ObjectId patterns
    const objectIdMatches = line.match(MONGODB_PATTERNS.objectId);
    if (objectIdMatches) {
      issues.push({
        type: 'MongoDB ObjectId',
        line: lineNumber,
        content: line.trim(),
        pattern: objectIdMatches[0],
        suggestion: objectIdMatches[0] === '_id' ? 'Use "id" instead' : 'Remove ObjectId references',
        severity: 'HIGH'
      });
    }

    // Check for MongoDB version field
    const versionMatches = line.match(MONGODB_PATTERNS.mongoVersion);
    if (versionMatches) {
      issues.push({
        type: 'MongoDB Version Field',
        line: lineNumber,
        content: line.trim(),
        pattern: versionMatches[0],
        suggestion: 'Remove __v field (not needed in PostgreSQL)',
        severity: 'MEDIUM'
      });
    }

    // Check for camelCase fields that should be snake_case
    const camelCaseMatches = line.match(MONGODB_PATTERNS.camelCaseFields);
    if (camelCaseMatches) {
      camelCaseMatches.forEach(match => {
        if (POSTGRESQL_EQUIVALENTS[match]) {
          issues.push({
            type: 'Field Naming Convention',
            line: lineNumber,
            content: line.trim(),
            pattern: match,
            suggestion: `Use "${POSTGRESQL_EQUIVALENTS[match]}" instead of "${match}"`,
            severity: 'MEDIUM'
          });
        }
      });
    }

    // Check for embedded array patterns
    const embeddedArrayMatches = line.match(MONGODB_PATTERNS.embeddedArrays);
    if (embeddedArrayMatches) {
      issues.push({
        type: 'MongoDB Embedded Arrays',
        line: lineNumber,
        content: line.trim(),
        pattern: embeddedArrayMatches[0],
        suggestion: 'Use separate API calls for related data in PostgreSQL',
        severity: 'HIGH'
      });
    }

    // Check for nested object patterns
    const nestedObjectMatches = line.match(MONGODB_PATTERNS.nestedObjects);
    if (nestedObjectMatches) {
      issues.push({
        type: 'MongoDB Nested Objects',
        line: lineNumber,
        content: line.trim(),
        pattern: nestedObjectMatches[0],
        suggestion: 'Use JOIN queries or separate API calls for related data',
        severity: 'HIGH'
      });
    }
  });

  return issues;
}

function generateReport() {
  console.log('🔍 FRONTEND POSTGRESQL COMPATIBILITY AUDIT');
  console.log('==========================================\n');

  const allFiles = [];
  SCAN_DIRECTORIES.forEach(dir => {
    if (fs.existsSync(dir)) {
      allFiles.push(...getAllFiles(dir));
    }
  });

  console.log(`📊 Scanning ${allFiles.length} files...\n`);

  const fileIssues = {};
  let totalIssues = 0;
  const severityCounts = { HIGH: 0, MEDIUM: 0, LOW: 0 };

  allFiles.forEach(filePath => {
    const issues = analyzeFile(filePath);
    if (issues.length > 0) {
      fileIssues[filePath] = issues;
      totalIssues += issues.length;
      
      issues.forEach(issue => {
        severityCounts[issue.severity]++;
      });
    }
  });

  // Generate summary
  console.log('📋 SUMMARY:');
  console.log('===========');
  console.log(`Total files scanned: ${allFiles.length}`);
  console.log(`Files with issues: ${Object.keys(fileIssues).length}`);
  console.log(`Total issues found: ${totalIssues}`);
  console.log(`  - HIGH severity: ${severityCounts.HIGH}`);
  console.log(`  - MEDIUM severity: ${severityCounts.MEDIUM}`);
  console.log(`  - LOW severity: ${severityCounts.LOW}\n`);

  // Generate detailed report
  console.log('🔍 DETAILED ISSUES:');
  console.log('===================\n');

  Object.entries(fileIssues).forEach(([filePath, issues]) => {
    console.log(`📄 ${filePath}`);
    console.log('-'.repeat(filePath.length + 2));
    
    issues.forEach(issue => {
      const severityIcon = issue.severity === 'HIGH' ? '🔴' : issue.severity === 'MEDIUM' ? '🟡' : '🟢';
      console.log(`${severityIcon} Line ${issue.line}: ${issue.type}`);
      console.log(`   Pattern: "${issue.pattern}"`);
      console.log(`   Code: ${issue.content}`);
      console.log(`   💡 ${issue.suggestion}\n`);
    });
  });

  // Generate recommendations
  console.log('💡 RECOMMENDATIONS:');
  console.log('===================');
  console.log('1. 🔄 Update TypeScript interfaces to use PostgreSQL field names');
  console.log('2. 🔧 Replace _id with id throughout the frontend');
  console.log('3. 🐍 Convert camelCase field names to snake_case');
  console.log('4. 🔗 Update API calls to handle relational data structure');
  console.log('5. 🧹 Remove MongoDB-specific fields like __v');
  console.log('6. 📝 Update form validation to match new field names');
  console.log('7. 🎨 Update UI components to use new field names');
  console.log('8. 🧪 Update tests to use PostgreSQL data structures\n');

  return {
    totalFiles: allFiles.length,
    filesWithIssues: Object.keys(fileIssues).length,
    totalIssues,
    severityCounts,
    fileIssues
  };
}

// Run the audit
if (require.main === module) {
  try {
    generateReport();
  } catch (error) {
    console.error('❌ Audit failed:', error.message);
    process.exit(1);
  }
}

module.exports = { generateReport, MONGODB_PATTERNS, POSTGRESQL_EQUIVALENTS };
