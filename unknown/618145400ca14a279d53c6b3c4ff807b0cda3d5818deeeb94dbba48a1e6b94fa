import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateCategoryDto {
  @ApiProperty({
    description: 'The name of the category',
    example: 'Fruits',
    required: true,
  })
  @IsString()
  name: string;
}

export class CategoryResponseDto {
  @ApiProperty({
    description: 'The unique identifier of the category',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'The name of the category',
    example: 'Electronics',
  })
  name: string;

  @ApiProperty({
    description: 'The slug of the category',
    example: 'electronics',
  })
  slug: string;

  @ApiProperty({
    description: 'A brief description of the category',
    example: 'All kinds of electronic devices',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'List of children categories',
    example: ['60a1e26f9c927b8b887a26a2', '60a1e26f9c927b8b887a26a3'],
    required: false,
  })
  children: string[];

  @ApiProperty({
    description: 'The parent category if any',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  parent: string;

  @ApiProperty({
    description: 'List of products associated with this category',
    example: ['f47ac10b-58cc-4372-a567-0e02b2c3d479', 'f47ac10b-58cc-4372-a567-0e02b2c3d480'],
    required: false,
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsOptional()
  products: string[];
}
